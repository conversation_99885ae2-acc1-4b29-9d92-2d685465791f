import { Box, BoxComponentProps } from "@mantine/core";
import React from "react";

type AbsoluteRatioProps = {
    ratio?: number;
    children?: React.ReactNode;
} & BoxComponentProps;

const AbsoluteRatio = React.forwardRef<HTMLDivElement, AbsoluteRatioProps>(
    (props, ref) => {
        const { ratio = 1, children, ...rest } = props;

        return (
            <Box
                {...rest}
                ref={ref}
            >
                <Box
                    className="tw-w-full tw-h-0 tw-relative tw-overflow-hidden"
                    pb={`${(1 / (ratio <= 0 ? 1 : ratio)) * 100}%`}
                >
                    <Box className="tw-top-0 tw-right-0 tw-bottom-0 tw-left-0 tw-absolute tw-overflow-hidden">
                        {children}
                    </Box>
                </Box>
            </Box>
        );
    }
);

export default AbsoluteRatio;
