import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type ModalState = {
    modalParams: {
        [key: string]: any;
    };
    alert: {
        show: boolean;
        title?: string;
        message?: string;
        variant?: "success" | "danger" | "warning" | "info";
    };
    upload: {
        show: boolean;
        title?: string;
        accept: string[];
        maxSize: number;
        required?: boolean;
    };
    languageSelect: boolean;
    passwordReset: boolean;
    profileValidate: boolean;
    calendarEventCreation: boolean;
    projectCompanyCreate: boolean;
    projectCompanyApplication: boolean;
    projectCompanyDetail: boolean;
    createUser: boolean;
    informationProject: boolean;
    confirm: ModalState["alert"] & {
        onConfirm?: () => void;
        onConfirmText?: string;
    };
    businessIntro: boolean;
    businessResult: boolean;
    businessCertificate: boolean;
};

type ModalAction = {
    open: (modal: keyof ModalState, params?: any) => void;
    close: (modal: keyof ModalState) => void;
    openConfirm: (props: {
        title?: string;
        message?: string;
        variant?: ModalState["alert"]["variant"];
        onConfirm?: () => void;
        onConfirmText?: string;
    }) => void;
    closeConfirm: () => void;
    openUpload: (opts: {
        title?: string;
        accept?: string[];
        maxSize: number;
        required?: boolean;
    }) => void;
    closeUpload: () => void;
};

const baseModalStore = create<ModalState & ModalAction>()(
    devtools(
        (set) => ({
            modalParams: {},
            alert: {
                show: false,
                title: "",
                message: "",
                variant: "info",
            },
            confirm: {
                show: false,
                title: "",
                message: "",
                onConfirm: () => {},
                onConfirmText: "",
            },
            upload: {
                title: "",
                show: false,
                accept: ["image/png", "image/jpeg"],
                maxSize: 5 * 1024 * 1024,
                required: true,
            },
            calendarEventCreation: false,
            languageSelect: false,
            passwordReset: false,
            profileValidate: false,
            projectCompanyCreate: false,
            projectCompanyApplication: false,
            projectCompanyDetail: false,
            createUser: false,
            informationProject: false,
            businessIntro: false,
            businessResult: false,
            businessCertificate: false,
            open: (modal, params) =>
                set((state) => ({
                    ...state,
                    [modal]: true,
                    modalParams: params
                        ? { ...state.modalParams, [modal]: params }
                        : state.modalParams,
                })),
            close: (modal) =>
                set((state) => ({
                    ...state,
                    [modal]: false,
                    modalParams: {
                        ...state.modalParams,
                        [modal]: undefined,
                    },
                })),
            openConfirm: ({
                title,
                message,
                variant = "info",
                onConfirm = () => {},
                onConfirmText = "",
            }) => {
                set({
                    confirm: {
                        show: true,
                        title,
                        message,
                        variant,
                        onConfirm,
                        onConfirmText,
                    },
                });
            },
            closeConfirm: () => {
                set({
                    confirm: {
                        show: false,
                        title: "",
                        message: "",
                        variant: "info",
                        onConfirm: () => {},
                        onConfirmText: "",
                    },
                });
            },
            openUpload: ({
                title = "文件上传",
                accept = ["image/png", "image/jpeg"],
                required = true,
                maxSize,
            }) => {
                set({
                    upload: {
                        show: true,
                        title,
                        accept,
                        required,
                        maxSize,
                    },
                });
            },
            closeUpload: () => {
                set({
                    upload: {
                        show: false,
                        title: "",
                        accept: ["image/png", "image/jpeg"],
                        maxSize: 5 * 1024 * 1024,
                        required: true,
                    },
                });
            },
        }),
        {
            name: "modal-store",
        }
    )
);

const useModalStore = createSelectors(baseModalStore);

export const closeAllStoreModals = () => {
    useModalStore.setState({
        modalParams: {},
        alert: {
            show: false,
            title: "",
            message: "",
            variant: "info",
        },
        confirm: {
            show: false,
            title: "",
            message: "",
            onConfirm: () => {},
            onConfirmText: "",
        },
        upload: {
            title: "",
            show: false,
            accept: ["image/png", "image/jpeg"],
            maxSize: 5 * 1024 * 1024,
            required: true,
        },
        calendarEventCreation: false,
        languageSelect: false,
        passwordReset: false,
        profileValidate: false,
        projectCompanyCreate: false,
        projectCompanyApplication: false,
        projectCompanyDetail: false,
        createUser: false,
        informationProject: false,
        businessIntro: false,
        businessResult: false,
        businessCertificate: false,
    });
};

export default useModalStore;
