import PageHeader from "@/components/member/PageHeader";
import { <PERSON>na<PERSON>utton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import {
    Anchor,
    Badge,
    Breadcrumbs,
    Divider,
    Group,
    Paper,
    ScrollArea,
    SimpleGrid,
    Stack,
    Text,
    Title,
} from "@mantine/core";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import api from "@/apis";
import dayjs from "dayjs";
import useNotificationStore from "@/store/notification";

const NotificationDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const notificationID = location.state?.notificationID;
    const [info, setInfo] = useState<Record<string, any> | null>(null);
    const markAsRead = useNotificationStore.use.markAsRead();
    const deleteNotification = useNotificationStore.use.deleteNotification();

    const [paginate, setPaginate] = useState({
        prev_id: null,
        next_id: null,
    });

    const fetchNotification = async (id: string) => {
        const res = await api.notification.info(id);
        if (res.info.notificationState == 0) {
            await markAsRead(id);
        }
        setInfo(res.info);
        setPaginate(res.paginate);
    };

    useEffect(() => {
        if (notificationID) {
            fetchNotification(notificationID);
        }
    }, [notificationID]);

    const breadcrumbItems = [
        {
            title: "通知中心",
            href: "/member/notifications",
            color: "#999999",
        },
        {
            title: "消息详情",
            href: "#",
            color: "#222222",
        },
    ].map((item, index) => (
        <Anchor
            onClick={() => navigate(item.href)}
            key={index}
            underline="never"
            c={item.color}
        >
            <Text
                fw={800}
                fz={22}
            >
                {item.title}
            </Text>
        </Anchor>
    ));

    if (!info) {
        return (
            <div>
                <PageHeader />
                <Paper
                    mt="xl"
                    p="lg"
                    radius="lg"
                >
                    <Text>未找到通知详情</Text>
                </Paper>
            </div>
        );
    }

    const handleDeleteSingle = async (id: string) => {
        try {
            const success = await deleteNotification(id);
            if (success) {
                noty.success("删除成功");
                navigate("/member/notifications");
            }
        } catch (error) {
            console.error("删除通知失败", error);
            noty.error("删除通知失败");
        }
    };

    // 处理通知内容的 A 标签点击事件
    const handleContentClick = async (e: React.MouseEvent<HTMLDivElement>) => {
        const target = e.target as HTMLElement;

        // 检查是否点击的是 a 标签
        if (target.tagName === "A") {
            e.preventDefault(); // 阻止默认跳转行为

            const href = target.getAttribute("href");

            // 调用接口
            try {
                const res = await api.notification.getThirdLink(href);

                if (res.url) {
                    // 在新窗口打开链接
                    window.open(res.url, "_blank");
                }
            } catch (error) {
                console.error("链接点击失败:", error);
            }
        }
    };

    return (
        <div>
            <PageHeader />

            {/* 通知详情 */}
            <Paper
                mt="xl"
                p="lg"
                radius="lg"
                h="70vh"
            >
                <Breadcrumbs>{breadcrumbItems}</Breadcrumbs>
                <Divider my="md" />
                <Group justify="space-between">
                    <Stack gap="xs">
                        <Title order={5}>{info.notificationTitleZH}</Title>
                        <Group>
                            <Badge
                                color="#DFEEFF"
                                className="tw-text-[#000]"
                                radius={4}
                                fw={400}
                            >
                                {info.type_remark}
                            </Badge>
                            <Text>
                                {info.created_at &&
                                    dayjs(info.created_at).format(
                                        "YYYY-MM-DD HH:mm:ss"
                                    )}
                            </Text>
                        </Group>
                    </Stack>

                    <SimpleGrid cols={3}>
                        <CnaButton
                            variant="default"
                            className="tw-text-[#FF0000]"
                            onClick={() =>
                                handleDeleteSingle(info.notificationID)
                            }
                        >
                            删除
                        </CnaButton>
                        <CnaButton
                            variant="default"
                            onClick={() => fetchNotification(paginate.prev_id)}
                            disabled={!paginate.prev_id}
                        >
                            上一条
                        </CnaButton>
                        <CnaButton
                            variant="default"
                            onClick={() => fetchNotification(paginate.next_id)}
                            disabled={!paginate.next_id}
                        >
                            下一条
                        </CnaButton>
                    </SimpleGrid>
                </Group>
                <Divider my="md" />
                <ScrollArea h="50vh">
                    <Text
                        dangerouslySetInnerHTML={{
                            __html: info.notificationDescriptionZH,
                        }}
                        onClick={handleContentClick}
                    />
                </ScrollArea>
            </Paper>
        </div>
    );
};

export default NotificationDetail;
