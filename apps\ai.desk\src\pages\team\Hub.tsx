import useRegisterStore from "@/store/register";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

const TeamHubPage = () => {
    const location = useLocation();

    const navigate = useNavigate();

    const { setRegisterInfoValue } = useRegisterStore();

    useEffect(() => {
        // Create a URLSearchParams object from the location's search property
        const queryParams = new URLSearchParams(location.search);

        // Get a specific query parameter
        const refer = queryParams.get("refer"); // Replace

        if (!refer) {
            navigate("/", { replace: true });
        } else {
            setRegisterInfoValue("refer", refer);
            navigate("/account/welcome?refer=" + refer, { replace: true });
        }
    }, [location]);

    return <AuthenticationLayout />;
};

export default TeamHubPage;
