import { NavigateFunction } from "react-router-dom";
import { cnaRequest } from "@code.8cent/utils";
import api from "@/apis";
import useUserStore, { UserProfile, UserState, UserActions } from "@/store/user";
import useWizardStore from "@code.8cent/store/wizard";
import { INTERVIEW_STATUS, INTERVIEW_STATUS_TEXT } from "@/hoc/withRouteGuard";

export interface NavigationParams {
    refer?: string;
    occupation_name?: string;
    occupation_id?: string;
    type?: string;
}

export interface UserLoginData {
    token: string;
    profile_status: number;
    profile_id: number;
    register_setting: string[];
    pay_token?: string;
    is_new?: 1 | 2;
    base_info?: 1 | 2;
}

export class NavigationLogic {
    private navigate: NavigateFunction;
    private userStore: any; // 临时使用 any 类型避免类型推断问题
    private wizardStore: any; // 临时使用 any 类型避免类型推断问题

    constructor(
        navigate: NavigateFunction,
        userStore: any,
        wizardStore: any
    ) {
        this.navigate = navigate;
        this.userStore = userStore;
        this.wizardStore = wizardStore;
    }

    /**
     * 处理用户登录后的页面跳转逻辑
     */
    async handleUserNavigation(
        userData: UserLoginData,
        params: NavigationParams
    ): Promise<void> {
        const { profile, setProfile, setStepV3, setInterviewStatus, setInterviewSuccessShown } = this.userStore;
        const { setRegisterSetting, setState: setWizardState } = this.wizardStore;

        // 更新用户资料
        const profileData = {
            profile_id: userData.profile_id,
            profile_status: userData.profile_status,
            register_setting: userData.register_setting || [],
            pay_token: userData.pay_token,
            is_new: userData.is_new,
            base_info: userData.base_info,
            occupation_id: params.occupation_id,
            occupation_name: params.occupation_name,
            refer: params.refer,
        };
        setProfile(profileData);

        // 保存参数到sessionStorage
        if (params.refer) {
            sessionStorage.setItem("refer", params.refer);
        }

        // 处理新用户逻辑
        if (userData.is_new === 1) {
            await this.handleNewUser(params);
            return;
        }

        // 处理推荐人信息
        if (params.refer) {
            await this.handleReferral(params.refer);
        }

        // 处理基本信息未填写的情况
        if (userData.base_info === 2) {
            await this.handleIncompleteBasicInfo(params);
            return;
        }

        // 根据用户状态进行跳转
        await this.handleUserStatusNavigation(userData);
    }

    /**
     * 处理新用户逻辑
     */
    private async handleNewUser(params: NavigationParams): Promise<void> {
        if (params.type === "register") {
            // 从邀请链接进来的，直接跳到注册页
            this.navigate("/account/register", { replace: true });
        } else {
            // 直接访问链接的，从welcome页开始
            this.navigate("/account/welcome", { replace: true });
        }
    }

    /**
     * 处理推荐人信息
     */
    private async handleReferral(refer: string): Promise<void> {
        try {
            const { result, error } = await cnaRequest(
                "/api/v1/team/getRecommendInfo",
                "POST",
                { token: refer }
            );

            if (result && !error) {
                await api.user.submitInviteInfo({
                    profilePartnerCode: result.data.profilePartnerCode,
                    recommend: result.data.profileID,
                    is_team: result.data.is_team,
                });
            }
        } catch (error) {
            console.error("处理推荐人信息失败:", error);
        }
    }

    /**
     * 处理基本信息未填写的情况
     */
    private async handleIncompleteBasicInfo(params: NavigationParams): Promise<void> {
        if (!params.occupation_id) {
            // 没有选择职业，跳到职业选择页面
            this.navigate("/account/fields", { replace: true });
        } else {
            // 跳到注册页面
            this.navigate("/account/register", { replace: true });
        }
    }

    /**
     * 根据用户状态进行跳转
     */
    private async handleUserStatusNavigation(userData: UserLoginData): Promise<void> {
        const { setStepV3, setInterviewStatus, setInterviewSuccessShown } = this.userStore;
        const { setRegisterSetting, setState: setWizardState } = this.wizardStore;

        // 待付款状态
        if (userData.profile_status === 1) {
            await this.handlePendingPayment(userData);
            return;
        }

        // 待审核状态
        if (userData.profile_status === 2) {
            await this.handlePendingReview(userData);
            return;
        }

        // 面试失败状态
        if (userData.profile_status === 4) {
            setStepV3("interview-status");
            setInterviewStatus("failed");
            this.navigate("/account/register", { replace: true });
            return;
        }

        // 正常状态 - 检查是否需要面试
        await this.handleNormalStatus(userData);
    }

    /**
     * 处理待付款状态
     */
    private async handlePendingPayment(userData: UserLoginData): Promise<void> {
        const { setStepV3 } = this.userStore;

        if (userData.pay_token) {
            // 创建进程
            const processRes = await api.exam.addProcess(
                "profile_register",
                userData.pay_token
            );

            // 保存业务进程编号
            await window.localForage.setItem(
                "register_payment_process_no",
                processRes.data?.process_order_no
            );
        }

        setStepV3("payment-qr");
        this.navigate("/account/register", { replace: true });
    }

    /**
     * 处理待审核状态
     */
    private async handlePendingReview(userData: UserLoginData): Promise<void> {
        const { setStepV3, setInterviewStatus, setInterviewSuccessShown } = this.userStore;
        const { setRegisterSetting, setState: setWizardState } = this.wizardStore;

        if (userData.register_setting.length > 0) {
            setRegisterSetting(userData.register_setting);
            setWizardState(0);
        }

        // 检查是否有资格认证或商业案例
        if (userData.register_setting.includes("qualification")) {
            setStepV3("qualification");
            this.navigate("/account/register", { replace: true });
            return;
        }

        if (userData.register_setting.includes("business_case")) {
            setStepV3("business_case");
            this.navigate("/account/register", { replace: true });
            return;
        }

        // 检查面试状态
        await this.checkInterviewStatus(userData.profile_id, setStepV3, setInterviewStatus, setInterviewSuccessShown);
    }

    /**
     * 处理正常状态
     */
    private async handleNormalStatus(userData: UserLoginData): Promise<void> {
        const { setInterviewSuccessShown } = this.userStore;
        const { setRegisterSetting, setState: setWizardState } = this.wizardStore;

        if (userData.register_setting?.length > 0) {
            setRegisterSetting(userData.register_setting);
            setWizardState(0);
            this.navigate("/account/wizard", { replace: true });
        } else {
            setInterviewSuccessShown(userData.profile_id, true);
            this.navigate("/account/welcome-video", { replace: true });
        }
    }

    /**
     * 检查面试状态
     */
    private async checkInterviewStatus(
        profileId: number,
        setStepV3: (step: string) => void,
        setInterviewStatus: (status: string) => void,
        setInterviewSuccessShown: (profileId: number, shown: boolean) => void
    ): Promise<void> {
        try {
            const { result: interviewResult, error: interviewError } = await cnaRequest(
                "/api/v1/interview/status",
                "GET"
            );

            if (interviewError) {
                console.error("获取面试状态失败:", interviewError);
                setStepV3("interview-status");
                setInterviewStatus("pending");
                this.navigate("/account/register", { replace: true });
                return;
            }

            const status = interviewResult?.data?.status ?? INTERVIEW_STATUS.PENDING;
            const statusText = INTERVIEW_STATUS_TEXT[status] ?? "pending";

            // 面试成功且已经展示过成功状态，跳到wizard
            const hasShownInterviewSuccess = await window.localForage.getItem(
                `hasShownInterviewSuccess_${profileId}`
            );

            if (status === INTERVIEW_STATUS.SUCCESS && hasShownInterviewSuccess) {
                const { profile } = this.userStore;
                if (profile?.register_setting.length) {
                    this.navigate("/account/wizard", { replace: true });
                } else {
                    this.navigate("/account/welcome-video", { replace: true });
                }
                return;
            }

            setStepV3("interview-status");
            setInterviewStatus(statusText);
            this.navigate("/account/register", { replace: true });
        } catch (error) {
            console.error("检查面试状态失败:", error);
            setStepV3("interview-status");
            setInterviewStatus("pending");
            this.navigate("/account/register", { replace: true });
        }
    }
}

export default NavigationLogic;
