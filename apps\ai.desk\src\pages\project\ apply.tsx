import React, { useState, useRef, useEffect } from "react";

import {
    Stepper,
    Button,
    Group,
    StepperProps,
    Stack,
    Card,
    Image,
    Text,
    Modal,
    ScrollArea,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import ReviewForm from "@/components/apply/review";
import { title } from "process";
import ReviewTable from "@/components/apply/reviewTable";
import PartnerReport from "@/components/apply/partnerReport";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import {
    PDFViewer,
    ImageViewer,
    getFileByUrl,
    PDFViewerV4,
} from "@code.8cent/react/FileViewer";
import { useDisclosure } from "@mantine/hooks";
import { useRequest } from "ahooks";
import TableModel from "@/components/apply/model/tableModel";
import UploadModel from "@/components/apply/model/uploadModel";
import {
    stepArray,
    StyledStepper,
    getCurrentStep,
} from "@/components/apply/component";
import api from "@/apis";
import WaitingPage from "@/components/apply/waiting";
import ContractPage from "@/components/apply/contract";
import ApplyReportFrom from "@/components/apply/applyReportFrom";
import ApplyReportTable from "@/components/apply/applyReportTable";
import ContractTable from "@/components/apply/contractTable";
import Activate from "@/components/apply/activate";
import Welcome from "@/components/apply/welcome";
const stepStatus = [];
import { useLocation } from "react-router-dom";
import PayModel from "@/components/apply/model/payModel";
import InvoiceModel from "@/components/apply/model/invoiceModel";
import { useTableStore } from "@/components/apply/store/tableStore";
import { useIs4Back } from "@/components/apply/store/useIs4Back";
import { useIs6Back } from "@/components/apply/store/useIs6Back";
import CompanyReview from "@/components/apply/companyReview";
import ApplyReportTableCom from "@/components/apply/applyReportTableCom";
import { CheckCircle } from "@phosphor-icons/react";
const ApplyPage = () => {
    const {
        openedTable,
        tableFile,
        closeTable,
        setTableFile,
        openTable,
        tableTitle,
        downloadTitle,
        isPay,
    } = useTableStore();
    const { is4Back, setIs4Back, toggleIs4Back } = useIs4Back();
    const { is6Back, setIs6Back } = useIs6Back();

    const [invoiceModelStatus, setInvoiceModelStatus] = useState(false);

    // const [openedTable, { open: openTable, close: closeTable }] =
    //     useDisclosure(false);
    const [openedUpload, { open: openUpload, close: closeUpload }] =
        useDisclosure(false);

    const [openedPay, { open: openPay, close: closePay }] =
        useDisclosure(false);

    const [payType, setPayType] = useState(1);
    const location = useLocation(); // 使用useLocation钩子
    const queryParams = new URLSearchParams(location.search); // 解析query参数
    const isCompany = queryParams.get("isCompany"); // 获取特定的query参数

    const [uploadType, setUploadType] = useState<string>("");
    const [active, setActive] = useState(0);
    const [oneStatus, setOneStatus] = useState("from");
    const [twoStatus, setTwoStatus] = useState("Contract");
    const [tableToken, setTableToken] = useState("");
    const scrollRef = useRef(null);
    const { run: getViewTableFile } = useRequest(
        async (url) => {
            const file = await getFileByUrl(url);

            if (file) {
                if (active != 5) {
                    setTableFile(file);
                }

                // return file;
            }
        },
        {
            manual: true,
        }
    );

    const isDesktop = useMediaQuery(`(min-width: 768px)`);
    const reviewFormRef = useRef<{
        submitForm: () => void;
        saveDraft: () => void;
    }>(null);
    useEffect(() => {
        if (tableToken) {
            let url = `${window.api_base_url}/api/v1/gsp/previewFile/${tableToken}`;
            getViewTableFile(url);
        }
    }, [tableToken]);

    const { data: progress, run: getProgress } = useRequest(async () => {
        const res = await api.apply.getProgress();
        return res;
    });

    const { run: getTableToken } = useRequest(
        async (type) => {
            const res = await api.apply.getFileToken(type);
            if (res) {
                setTableToken(res);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (progress) {
            let cur = getCurrentStep(progress);
            console.log("cur", cur);
            if (cur == 1 && progress[cur]?.includes("createForm")) {
                setOneStatus("from");
            } else {
                setOneStatus("noFrom");
                // getTableToken(1);
            }

            if (!progress[3]?.includes("printContract")) {
                setTwoStatus("noContract");
                // getTableToken(3);
            }

            setActive(Number(cur) - 1);

            if (
                progress[2]?.includes("formFail") &&
                progress[1]?.includes("commitCheck")
            ) {
                setActive(1);
            }

            if (
                progress[4]?.includes("contractFail") &&
                progress[3]?.includes("nextStep") &&
                progress[3]?.includes("uploadContract")
            ) {
                setActive(3);
            }

            if (
                progress[4]?.includes("contractFail") &&
                progress[3]?.includes("nextStep") &&
                !progress[3]?.includes("uploadContract")
            ) {
                setActive(2);
            }
            if (
                progress[5]?.includes("postReport") &&
                progress[6]?.includes("reportCompanyFail") &&
                is6Back
            ) {
                setActive(4);
            }

            if (
                progress[5]?.includes("postReport") &&
                progress[6]?.includes("reportCompanyFail") &&
                !is6Back
            ) {
                setActive(5);
            }

            if (progress[7]?.includes("reportFail") && !is4Back) {
                setActive(6);
            }

            if (progress[7]?.includes("reportFail") && is4Back) {
                setActive(4);
            }

            if (progress[9]?.includes("success")) {
                setActive(8);
            }
        }
    }, [progress, is4Back, is6Back]);

    useEffect(() => {
        console.log("active", active);
    }, [active]);

    useEffect(() => {
        console.log("is4Back----", is4Back);
    }, [is4Back]);

    // 下一步操作
    const { run: getFormCheck } = useRequest(
        async () => {
            const res = await api.apply.getFormCheck();
            if (res) {
                getProgress();
                setActive(1);
            }
        },
        {
            manual: true,
        }
    );

    const { run: getContractCheck } = useRequest(
        async () => {
            const res = await api.apply.getContractCheck();
            // console.log("res", res);
            if (res) {
                getProgress();
                setActive(3);
            }
        },
        {
            manual: true,
        }
    );

    const oneNextStep = () => {
        getFormCheck();
    };

    const twoNextStep = () => {
        getContractCheck();
    };

    const [dataLoaded, setDataLoaded] = useState(false);

    // 数据加载完成后滚动
    useEffect(() => {
        if (dataLoaded) {
            const scrollPos = sessionStorage.getItem("scrollPos");
            if (scrollPos && scrollRef.current) {
                setTimeout(() => {
                    scrollRef.current.scrollTo({
                        top: parseInt(scrollPos),
                        behavior: "smooth",
                    });
                    sessionStorage.removeItem("scrollPos");
                }, 100); // 延迟保证 DOM 渲染
            }
        }
    }, [dataLoaded]);

    useEffect(() => {
        console.log("isCompany", !isCompany);
    }, [isCompany]);

    // 付款操作

    return (
        <ScrollArea
            viewportRef={scrollRef}
            style={{ height: "100%" }}
            className="tw-h-full tw-w-full md:tw-pr-4 tw-flex "
        >
            <Group
                justify="space-between"
                align="center"
                className="tw-mb-5"
            >
                <div className="md:tw-text-[40px] tw-text-[24px] tw-font-bold  tw-leading-normal">
                    入驻平台申请流程
                </div>
                {/* <Button
                    variant="outline"
                    onClick={() => {
                        setInvoiceModelStatus(true);
                    }}
                >
                    申请开票
                </Button> */}
            </Group>

            <InvoiceModel
                opened={invoiceModelStatus}
                close={() => {
                    setInvoiceModelStatus(false);
                }}
            />

            <StyledStepper
                allowNextStepsSelect={true}
                className="tw-mx-10 tw-mb-[76px]"
                isDesktop={isDesktop}
                active={active}
                // onStepClick={(step) => {
                //     if (step > active) {
                //         setActive(step);
                //     }
                //     setActive(step);
                // }}
                iconSize={isDesktop ? 34 : 16}
                icon={
                    !isDesktop && <div className="tw-size-3 tw-bg-white"></div>
                }
                color="#182265"
                progressIcon={<div className=" ">{active + 1}</div>}
            >
                {stepArray.map((item, index) => {
                    return (
                        <Stepper.Step
                            key={index}
                            label={`第${item.index}步`}
                            description={item?.name}
                        />
                    );
                })}
            </StyledStepper>

            <Card
                radius={"10px"}
                className="tw-flex-1"
            >
                <Card.Section
                    className="tw-p-5 tw-text-[18px] tw-font-bold"
                    withBorder
                >
                    {stepArray.find((item) => item.index == active + 1)?.title}
                    {is6Back && progress[5]?.includes("postReport") && (
                        <div className="tw-text-[#DA0101] tw-text-[14px] tw-font-normal tw-mt-1">
                            以下是不符合要求的文件，请仔细检查后按照标准重新提交。
                        </div>
                    )}

                    {active == 8 && (
                        <div className="tw-text-[#DA0101] tw-text-[14px] tw-font-normal tw-mt-1">
                            重要提示：付款完成后，发送【绿智地球AI
                            账号】至企业的邮箱，请注意提示企业查收
                        </div>
                    )}
                </Card.Section>
                <Card.Section withBorder>
                    {active == 0 && oneStatus == "from" && (
                        <>
                            <ReviewForm
                                ref={reviewFormRef}
                                setTableToken={setTableToken}
                                getProgress={getProgress}
                                openTable={() => {
                                    openTable({
                                        tableTitle: "查看",
                                        downloadTitle:
                                            "绿智地球专属平台企业用户资格预审表格.pdf",
                                    });
                                }}
                            />
                        </>
                    )}

                    {active == 0 && oneStatus != "from" && (
                        <ReviewTable
                            isUpload={!progress[1]?.includes("uploadForm")}
                            isPay={!progress[1]?.includes("payOne")}
                            isPay2={!progress[1]?.includes("payTwo")}
                            openTable={() => {
                                setUploadType("table");
                                openUpload();
                            }}
                            viewTable={() => {
                                setOneStatus("from");
                            }}
                            openViewModel={() => {
                                getTableToken(1);
                                openTable({
                                    tableTitle: "查看",
                                    downloadTitle:
                                        "绿智地球专属平台企业用户资格预审表格.pdf",
                                });
                            }}
                            nextStep={oneNextStep}
                            handlePay={() => {
                                openPay();
                                setPayType(1);
                            }}
                            handlePay2={() => {
                                openPay();
                                setPayType(2);
                            }}
                            isCompany={isCompany}
                            getProgress={getProgress}
                        />
                    )}

                    {active == 1 && (
                        <WaitingPage
                            isNoPass={progress[2]?.includes("formFail")}
                            type={1}
                            goBack={() => {
                                setActive(0);
                            }}
                            isPayBack={progress[2]?.includes("payOneReturn")}
                            getProgress={getProgress}
                            isCompany={isCompany}
                        />
                    )}

                    {active == 2 && twoStatus == "Contract" && isCompany && (
                        <ContractPage getProgress={getProgress} />
                    )}
                    {active == 2 && twoStatus != "Contract" && isCompany && (
                        <ContractTable
                            isUpload={!progress[3]?.includes("uploadContract")}
                            isPay={!progress[3]?.includes("payThree")}
                            openTable={() => {
                                setUploadType("contract");
                                openUpload();
                            }}
                            viewContract={() => {
                                // setTwoStatus("Contract");
                                getTableToken(3);
                                openTable({
                                    title: "预览合同",
                                    downloadTitle:
                                        "绿智地球平台中国企业用户合同.pdf",
                                });
                            }}
                            nextStep={twoNextStep}
                            handlePay={() => {
                                openPay();
                                setPayType(3);
                            }}
                            getProgress={getProgress}
                            isCompany={isCompany}
                        />
                    )}

                    {active == 2 && !isCompany && (
                        <WaitingPage
                            title={"等待企业签署合同"}
                            desc="等待企业签署合同才能到下一步骤"
                        />
                    )}

                    {active == 3 && (
                        <WaitingPage
                            isNoPass={!progress[4]?.includes("contractCheck")}
                            type={2}
                            goBack={() => {
                                setActive(2);
                            }}
                        />
                    )}

                    {active == 4 && progress[5]?.includes("postReport") && (
                        <ApplyReportTableCom
                            getProgress={getProgress}
                            isBack={is4Back}
                            isCompany={isCompany}
                            scrollRef={scrollRef}
                            setDataLoaded={setDataLoaded}
                        />
                    )}

                    {active == 4 &&
                        progress[5]?.includes("waitCompanyUpload") && (
                            <WaitingPage
                                title={"等待企业提交资料"}
                                desc="资料提交完成后需要您对材料进行初步核对"
                            />
                        )}

                    {active == 5 &&
                        progress[6]?.includes("reportCompanyCheck") && (
                            <WaitingPage
                                title={"材料正在初步核对中"}
                                desc="审核结果将通过邮件方式通知您，请耐心等候"
                            />
                        )}
                    {active == 5 &&
                        progress[6]?.includes("reportCompanyFail") && (
                            <WaitingPage
                                // title={"材料正在初步核对中"}
                                isNoPass={progress[6]?.includes(
                                    "reportCompanyFail"
                                )}
                                type={4}
                                goBack={() => {
                                    setIs6Back(true);
                                    setActive(4);
                                }}
                            />
                        )}

                    {active == 5 &&
                        progress[6]?.includes("reportCompanySuccess") && (
                            <div></div>
                        )}

                    {active == 5 &&
                        progress[6]?.includes("partnerCheckReport") && (
                            <ApplyReportTable
                                getProgress={getProgress}
                                isBack={is4Back}
                                isCompany={isCompany}
                                scrollRef={scrollRef}
                                setDataLoaded={setDataLoaded}
                            />
                        )}

                    {/* {active == 5 && !isCompany && (
                        <WaitingPage title={"合伙人审核中"} />
                    )}
                    {active == 5 &&
                        isCompany &&
                        progress[6]?.includes("reportCompanyCheck") && (
                            <CompanyReview getProgress={getProgress} />
                        )}
                    {active == 5 &&
                        isCompany &&
                        progress[6]?.includes("reportCompanyFail") && (
                            <WaitingPage title={"等待合伙人上传"} />
                        )} */}
                    {active == 6 && (
                        <WaitingPage
                            isNoPass={!progress[7]?.includes("reportCheck")}
                            type={3}
                            goBack={() => {
                                setActive(4);
                                setIs4Back(true);
                            }}
                        />
                    )}

                    {active == 7 &&
                        progress[8]?.includes("reportPartnerPOST") &&
                        !isCompany && (
                            <PartnerReport
                                getProgress={getProgress}
                                nextStep={oneNextStep}
                            ></PartnerReport>
                        )}

                    {active == 7 &&
                        progress[8]?.includes("reportPartnerPOST") &&
                        isCompany && (
                            <WaitingPage
                                title={"尽调报告审核中"}
                                desc="审核结果将通过邮件方式通知您，请耐心等候"
                            />
                        )}

                    {active == 7 && progress[8]?.includes("reportCACheck") && (
                        <WaitingPage />
                    )}

                    {active == 8 &&
                        !isCompany &&
                        progress[9]?.includes("payFour") && (
                            <WaitingPage
                                title={"等待企业付款"}
                                desc="付款成功后成功开通账号"
                            />
                        )}
                    {active == 8 &&
                        isCompany &&
                        !progress[9]?.includes("success") && (
                            <Activate
                                isPay={
                                    progress &&
                                    !progress[9]?.includes("payFour")
                                }
                                handlePay={() => {
                                    openPay();
                                    setPayType(4);
                                }}
                                nextStep={() => {}}
                                isCompany={isCompany}
                            />
                        )}

                    {active == 8 && progress[9]?.includes("success") && (
                        <div className="tw-flex tw-justify-center tw-items-center tw-my-10">
                            <Stack
                                justify="center"
                                align="center"
                            >
                                <CheckCircle
                                    size={68}
                                    color="#34C759"
                                ></CheckCircle>
                                <div className="tw-font-bold tw-text-[20px]">
                                    付款成功
                                </div>
                                <div className="tw-text-[#666] tw-text-[14px]">
                                    欢迎入驻绿智地球
                                </div>
                            </Stack>
                        </div>
                    )}
                </Card.Section>
            </Card>
            {/* <Welcome></Welcome> */}

            <TableModel
                openedTable={openedTable}
                closeTable={() => {
                    closeTable();
                    getProgress();
                }}
                tableFile={tableFile}
                title={{
                    tableTitle,
                    downloadTitle,
                }}
                isPay={isPay}
            />

            <UploadModel
                opened={openedUpload}
                close={closeUpload}
                type={uploadType}
                getProgress={getProgress}
            />

            <PayModel
                opened={openedPay}
                close={closePay}
                type={payType}
                getProgress={getProgress}
            />
        </ScrollArea>
    );
};

export default ApplyPage;
