import React, { useRef } from "react";
import { useLocation } from "react-router-dom";

// 引入所有组件

import { useTableStore } from "@/components/apply/store/tableStore";
import TableModel from "@/components/apply/model/tableModel";
import Header from "@/components/apply/static/components/header";

import Financial_30 from "@/components/apply/financial/financial_30";
import Financial_31 from "@/components/apply/financial/financial_31";
import Financial_32 from "@/components/apply/financial/financial_32";
const FinancialStatementsPage = () => {
    const headerRef = useRef<{ handleTranslate: () => Promise<void> }>(null);

    const { openedTable, tableFile, closeTable, tableTitle, downloadTitle } =
        useTableStore();
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const key = params.get("key");
    const id = params.get("id");
    const title = params.get("title");

    let content: React.ReactNode = <div>FinancialStatementsPage</div>;

    // 状态提升
    const [name, setName] = React.useState("");
    const [name_en, setName_en] = React.useState("");

    // 翻译字段声明
    const translatableFields = [{ zh: "name", en: "name_en" }];

    // getValues 返回当前字段状态（对象）
    const getValues = () => ({ name, name_en });

    // reset 用于更新状态（传入部分字段）
    const reset = (data: Partial<{ name: string; name_en: string }>) => {
        if (data.name !== undefined) setName(data.name);
        if (data.name_en !== undefined) setName_en(data.name_en);
    };

    switch (id) {
        case "30":
            content = (
                <Financial_30
                    title={title}
                    name={name}
                    setName={setName}
                    name_en={name_en}
                    setName_en={setName_en}
                    headerRef={headerRef}
                />
            );
            break;
        case "31":
            content = <Financial_31 title={title} />;
            break;
        case "32":
            content = <Financial_32 title={title} />;
            break;
    }

    return (
        <div className="tw-bg-white tw-rounded-[10px] tw-max-h-full  tw-flex tw-flex-col">
            <Header
                ref={headerRef}
                title={title}
                {...(id === "30"
                    ? { translatableFields, getValues, reset }
                    : {})}
            />
            <div className="tw-flex-1 tw-overflow-y-auto">{content}</div>

            <TableModel
                openedTable={openedTable}
                closeTable={() => {
                    closeTable();
                }}
                tableFile={tableFile}
                title={{
                    tableTitle,
                    downloadTitle,
                }}
            />
        </div>
    );
};

export default FinancialStatementsPage;
