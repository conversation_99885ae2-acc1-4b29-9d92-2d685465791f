import { CnaButton } from "@code.8cent/react/components";
import CountDownTimer, {
    CountDownTimerRef,
} from "@/components/member/business/CountDownTimer";
import { Group, Title, Stack, LoadingOverlay } from "@mantine/core";
import { CaretLeft } from "@phosphor-icons/react";
import ResultModal from "@/components/member/business/modals/Result";
import useModalStore from "@/store/modal";
import CertificateModal from "@/components/member/business/modals/Certificate";
import ExamForm, { ExamFormRef } from "@/components/member/business/ExamForm";
import api from "@/apis";
import useBusinessStore, { setBusinessSection } from "@/store/business";
import { useState, useRef } from "react";
import ExamFormV2 from "@/components/member/business/ExamFormV2";

const Exam = () => {
    const openModal = useModalStore.use.open();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const countDownRef = useRef<CountDownTimerRef | null>(null);
    const examFormRef = useRef<ExamFormRef | null>(null);
    const [certificateNum, setCertificateNum] = useState("");

    const openConfirm = useModalStore.use.openConfirm();

    const handleBack = () => {
        openConfirm({
            title: "是否确定要返回？",
            message: "返回即退出当前考试，已作答题目将不会保存。",
            onConfirm: async () => {
                await api.exam.withdrawProcess(
                    "partner_franchise_handbook_license"
                );
                setBusinessSection("unlicensed-intro");
            },
        });
    };

    const handleTimeUp = () => {
        // 倒计时结束后的处理逻辑
        openModal("businessResult", {
            result: "fail",
            score: 0,
            correct: 0,
            wrong: 0,
            remark: "考试时间到，系统自动判定为不及格",
        });
    };

    // 处理考试答题答案提交
    const handleAnswersSubmit = async (answers: TBusinessExamResultParams) => {
        setIsSubmitting(true);

        try {
            const res = await api.business.submitExamResult(answers);
            // 计算正确和错误数量
            let correct = 0;
            let wrong = 0;
            let wrongDetail: {
                name: string;
                list: string[];
            }[] = [
                {
                    name: "单选题",
                    list: [],
                },
                {
                    name: "多选题",
                    list: [],
                },
                {
                    name: "判断题",
                    list: [],
                },
                {
                    name: "答辩题",
                    list: [],
                },
            ];

            // 每个类型独立的索引计数器
            const typeIndexMap: { [key: number]: number } = {};

            res.detail.detail.forEach((item) => {
                // 获取当前题目的类型索引，如果不存在则初始化为0
                if (!(item.question.type in typeIndexMap)) {
                    typeIndexMap[item.question.type] = 0;
                }
                let index = typeIndexMap[item.question.type];

                if (item.question.type !== 2) {
                    if (item.profileScore > 0) {
                        correct++;
                    } else {
                        wrong++;
                        // 格式化答案
                        let itemAnswer = item.question.option
                            .map((item, i) => {
                                item.option_name = `${String.fromCharCode(
                                    65 + i
                                )}.${item.option_name}`;
                                return item;
                            })
                            .filter((item) => item.is_answer === 1)
                            .map((item) => item.option_name)
                            .join("；");
                        let wronglistItem = `${index + 1}.${
                            item.question.question
                        } 标准答案：${itemAnswer}`;

                        wrongDetail
                            .find((w) => w.name === item.question.type_remark)
                            ?.list.push(wronglistItem);
                    }
                    // 增加当前类型的索引计数
                    typeIndexMap[item.question.type]++;
                } else {
                    // 答辩题 答辩题的评分标准是 0-15分
                    if (item.profileScore < 15) {
                        wrong++;
                        wrongDetail
                            .find((w) => w.name === "答辩题")
                            ?.list.push(
                                `1.${item.question.question} <br /> ${item.comment}`
                            );
                    } else {
                        correct++;
                    }
                }
            });

            if (res.pass === 1) {
                // 显示成功弹窗
                openModal("businessResult", {
                    result: "success",
                    score: res.total_score,
                    correct,
                    wrong,
                    wrongDetail,
                });
                setCertificateNum(res.certificate_num);
            } else {
                // 显示失败弹窗
                openModal("businessResult", {
                    result: "fail",
                    score: res.total_score,
                    correct,
                    wrong,
                    wrongDetail,
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const confirmSubmit = (answers: TBusinessExamResultParams) => {
        openConfirm({
            title: "是否确定提交试卷？",
            message: "提交后系统将自动评分，无法修改，请检查无误后再提交。",
            onConfirm: () => {
                handleAnswersSubmit(answers);
            },
        });
    };

    return (
        <Stack className="tw-h-full">
            <LoadingOverlay visible={isSubmitting} />

            <Group className="tw-w-full tw-relative">
                <CnaButton
                    leftSection={<CaretLeft />}
                    variant="transparent"
                    className="tw-border-none"
                    size="md"
                    fw={700}
                    onClick={handleBack}
                >
                    返回
                </CnaButton>
                <Title
                    order={2}
                    className="tw-absolute tw-left-1/2 tw-transform -tw-translate-x-1/2 tw-text-base md:tw-text-2xl"
                >
                    合伙人加盟手册考试
                </Title>
                <CountDownTimer
                    ref={countDownRef}
                    initialSeconds={3600}
                    onComplete={handleTimeUp}
                    className="tw-absolute tw-right-0 tw-hidden md:tw-block"
                    prefix="离答题结束还有 "
                />
            </Group>

            {/* <ExamForm
                ref={examFormRef}
                onSubmit={confirmSubmit}
            /> */}
            <ExamFormV2
                ref={examFormRef}
                onSubmit={confirmSubmit}
            />

            <ResultModal
                onBack={async () => {
                    // 撤回考试进程
                    await api.exam.withdrawProcess(
                        "partner_franchise_handbook_license"
                    );
                    setBusinessSection("unlicensed-intro");
                }}
                onRetry={async () => {
                    // 添加考试进程
                    await api.exam.addProcess(
                        "partner_franchise_handbook_license"
                    );
                    countDownRef.current?.resetAndStart(3600);
                    examFormRef.current?.resetForm();
                }}
                onSuccess={() => {
                    openModal("businessCertificate", {
                        certNo: certificateNum,
                    });
                }}
            />
            <CertificateModal
                title="合伙人加盟手册牌照"
                nextText="进入业务管理"
                onNext={() => {
                    setBusinessSection("licensed-list");
                }}
            />
        </Stack>
    );
};

export default Exam;
