import { Text, Tabs } from "@mantine/core";
import { <PERSON> } from "react-router-dom";
import { AuthFormCard } from "@code.8cent/react/components";
import AuthenticationLayout from "@code.8cent/react/layouts/AuthenticationLayout";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { useTitle } from "ahooks";
import EmailLoginForm from "./components/EmailLoginForm";
import PhoneLoginForm from "./components/PhoneLoginForm";

const LoginPage = () => {
    const { lang } = useSettingStore();
    
    useTitle(`${t("login.title", lang)} | ${window.app_title}`, {
        restoreOnUnmount: true,
    });

    return (
        <AuthenticationLayout>
            <AuthFormCard
                className="tw-w-[600px]"
                title={t("login.title", lang)}
            >
                <Tabs
                    defaultValue="phone"
                    classNames={{
                        list: "tw-mb-5",
                    }}
                >
                    <Tabs.List>
                        <Tabs.Tab value="phone">{t("login.type.phone", lang)}</Tabs.Tab>
                        <Tabs.Tab value="email">{t("login.type.email", lang)}</Tabs.Tab>
                    </Tabs.List>
                    <Tabs.Panel value="phone">
                        <PhoneLoginForm />
                    </Tabs.Panel>
                    <Tabs.Panel value="email">
                        <EmailLoginForm />
                    </Tabs.Panel>
                </Tabs>
                <div className="tw-flex tw-justify-center !tw-text-basic-6">
                    <Text
                        component={Link}
                        to={{ pathname: "/account/forget-password" }}
                        size="sm"
                        className="tw-text-neutral-600 hover:tw-text-neutral-950"
                    >
                        {t("login.forget_password", lang)}
                    </Text>
                </div>
            </AuthFormCard>
        </AuthenticationLayout>
    );
};

export default LoginPage;
