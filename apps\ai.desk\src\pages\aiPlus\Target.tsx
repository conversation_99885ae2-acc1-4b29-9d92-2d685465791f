import useRegisterStore from "@/store/register";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import { cnaRequest } from "@code.8cent/utils";
import { useMemoizedFn, useRequest } from "ahooks";
import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import noty from "@code.8cent/react/noty";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { initUserData } from "@/utils/initUserData";
import { INTERVIEW_STATUS, INTERVIEW_STATUS_TEXT } from "@/hoc/withRouteGuard";
import useWizardStore from "@code.8cent/store/wizard";
import api from "@/apis";

// 处理 AiPlus target 页面
const AiPlusTargetPage = () => {
    const location = useLocation();

    const navigate = useNavigate();

    const { lang } = useSettingStore();

    const { setState: setWizardState, setRegisterSetting } = useWizardStore();

    const { setStepV3, setInterviewStatus, setRegisterInfoValue } =
        useRegisterStore();

    // 退出登录
    const handleLogout = useMemoizedFn(async () => {
        await cnaRequest("/api/v1/login/logout", "POST");
        await window.localForage.removeItem("cna-token");
        sessionStorage.clear();
        navigate("/account/login", { replace: true });
    });

    // 登录
    const { run: handleLogin, loading: loggingIn } = useRequest(
        async (token) => {
            const { result, error } = await cnaRequest<{
                token: string;
                profile_status: number;
                profile_id: number;
                register_setting: string[];
                pay_token?: string;
                is_new?: 0 | 1;
            }>("/api/v1/login", "POST", { token });

            if (error) {
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;

                await window.localForage.setItem("cna-token", data.token);

                // 初始化用户数据（通知列表等）
                await initUserData();

                if (data.is_new === 1) {
                    await window.localForage.setItem("is_new", "1");
                    navigate("/account/welcome", { replace: true });
                    return;
                }

                // 判断用户状态 data.profile_status 1 => 待付款，2 => 待审核
                // 如果待付款就跳到 register, step-v3 => payment-qr
                if (data.profile_status === 1) {
                    if (data.pay_token) {
                        setRegisterInfoValue("token", data.pay_token);

                        // 创建进程
                        const processRes = await api.exam.addProcess(
                            "profile_register",
                            data.pay_token
                        );

                        // 保存业务进程编号
                        await window.localForage.setItem(
                            "register_payment_process_no",
                            processRes.data?.process_order_no
                        );
                    }

                    setStepV3("payment-qr");
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果待审核，先判断 register_setting 是否有 qualification 或者 business_case,
                // 有则跳转register，step-v3 => qualification or business_case
                if (data.profile_status === 2) {
                    if (data.register_setting.length > 0) {
                        await window.localForage.setItem(
                            "register-setting",
                            data.register_setting
                        );
                        setRegisterSetting(data.register_setting);
                        setWizardState(0);
                    }

                    if (data.register_setting.includes("qualification")) {
                        setStepV3("qualification");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    if (data.register_setting.includes("business_case")) {
                        setStepV3("business_case");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    // 检查面试状态
                    const { result: interviewResult, error: interviewError } =
                        await cnaRequest("/api/v1/interview/status", "GET");

                    if (interviewError) {
                        console.error(
                            "Failed to fetch interview status:",
                            interviewError
                        );
                        setStepV3("interview-status");
                        setInterviewStatus("pending");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    const status =
                        interviewResult?.data?.status ??
                        INTERVIEW_STATUS.PENDING;
                    const statusText =
                        INTERVIEW_STATUS_TEXT[status] ?? "pending";

                    // 面试成功，并且已经展示过成功状态的，跳到 wizard
                    const hasShownInterviewSuccess =
                        await window.localForage.getItem(
                            `hasShownInterviewSuccess_${data.profile_id}`
                        );
                    if (
                        status === INTERVIEW_STATUS.SUCCESS &&
                        hasShownInterviewSuccess
                    ) {
                        if (data.register_setting.length > 0) {
                            navigate("/account/wizard", { replace: true });
                            return;
                        }

                        navigate("/account/welcome-video", { replace: true });
                        return;
                    }

                    setStepV3("interview-status");
                    setInterviewStatus(statusText);
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 面试失败情况
                if (data.profile_status === 4) {
                    setStepV3("interview-status");
                    setInterviewStatus("failed");
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果不需要面试，检查 register_setting, 不为空则调到wizard, 为空则跳到 welcome-video
                if (data.register_setting?.length > 0) {
                    await window.localForage.setItem(
                        "register-setting",
                        data.register_setting
                    );
                    setRegisterSetting(data.register_setting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                } else {
                    await window.localForage.setItem(
                        `hasShownInterviewSuccess_${data.profile_id}`,
                        true
                    );
                    navigate("/account/welcome-video", { replace: true });
                }
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        // AiPlus 返回的参数
        const token = queryParams.get("token");
        const state = queryParams.get("state");
        // 其他自定义参数
        const type = queryParams.get("type"); // login or register

        // 处理退出登录
        if (state && state === "logout") {
            handleLogout();
            return;
        }

        if (!token) {
            navigate("/account/login", { replace: true });
            return;
        } else {
            // 处理登录和注册逻辑
            if (type === "login") {
                handleLogin(token);
            } else {
                // 注册
                sessionStorage.setItem("refer", queryParams.get("refer") || "");
                sessionStorage.setItem(
                    "occupation_name",
                    queryParams.get("occupation_name") || ""
                );
                sessionStorage.setItem(
                    "occupation_id",
                    queryParams.get("occupation_id") || ""
                );
                sessionStorage.setItem("aiplus_token", token);
                navigate("/account/register", { replace: true });
            }
        }
    }, [location]);

    return <AuthenticationLayout />;
};

export default AiPlusTargetPage;
