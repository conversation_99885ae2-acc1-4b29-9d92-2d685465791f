import ApplyPage from "@/pages/project/ apply";
import React from "react";
import {
    Stepper,
    Button,
    Group,
    StepperProps,
    Stack,
    Card,
    Image,
    Text,
    Modal,
    ScrollArea,
    Center,
    em,
} from "@mantine/core";
import { CaretRight } from "@phosphor-icons/react";
import bgImg from "./svg/img.png";
import { useMediaQuery } from "@mantine/hooks";

const CompanyApply = () => {
    const isMobile = useMediaQuery(`(max-width: ${em(768)})`);

    if (isMobile) {
        return (
            <div className="tw-px-4 tw-pt-5 tw-bg-[#F1F3F5] tw-h-[100vh]">
                <ApplyPage />
            </div>
        );
    }
    const LogoItem = () => {
        return (
            <div className="tw-flex md:tw-flex-col tw-items-center">
                <Image
                    src="/images/C&A-Logo-Icon-White.svg"
                    className="tw-w-[41px] md:tw-w-[108px] md:mb-2.5"
                />
                <Text className="tw-text-center tw-text-white tw-tracking-[3px] tw-text-[20px] md:tw-text-[24px] tw-font-bold max-md:tw-ml-2.5">
                    AI 办公室
                </Text>
            </div>
        );
    };
    return (
        <div className="tw-flex  tw-h-[100vh] tw-w-full">
            <Stack
                justify="space-between"
                className="tw-w-[300px] tw-relative max-md:tw-hidden tw-px-4 tw-py-[50px]"
                style={{
                    backgroundImage: `linear-gradient(270deg, #060D3D 0%, rgba(6, 13, 61, 0.00) 100%), url('/images/member-index/bg_1.png')`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                }}
            >
                <div>
                    <LogoItem />
                    {/* <NavItems /> */}
                </div>
            </Stack>
            <div className="tw-flex-1 tw-flex tw-flex-col tw-bg-[#060D3D] tw-pr-4">
                <Group
                    justify="space-between"
                    align="center"
                    className="tw-h-[56px] md:tw-hidden tw-px-4"
                >
                    <LogoItem />
                    {/* {isMobile && <SettingIcons />} */}
                </Group>

                <div className="tw-bg-[#F1F3F5] tw-w-full tw-h-full tw-rounded-[40px] tw-my-4  tw-px-4  tw-py-4">
                    <ApplyPage />
                </div>
            </div>
        </div>
    );
};

export default CompanyApply;
