import { <PERSON><PERSON><PERSON>utton } from "@code.8cent/react/components";
import {
    Group,
    Title,
    Text,
    Paper,
    Stack,
    Checkbox,
    Radio,
} from "@mantine/core";
import { CaretLeft } from "@phosphor-icons/react";
import useModalStore from "@/store/modal";
import { useNavigate } from "react-router-dom";
import { Split } from "@gfazioli/mantine-split-pane";
import "@gfazioli/mantine-split-pane/styles.css";
import { isEmpty } from "es-toolkit/compat";
import { useEffect, useRef, useState } from "react";
import api from "@/apis";
import TimeUpModal from "@/components/member/exam/TimeUpModal";
import FailModal from "@/components/modals/member/upgrade/Fail";
import SuccessModal from "@/components/modals/member/upgrade/Success";
import DocumentsModal from "@/components/modals/member/upgrade/Documents";

const useTimer = (initialTime: number, onComplete: () => void) => {
    const [timeLeft, setTimeLeft] = useState(initialTime);
    const timerRef = useRef<NodeJS.Timeout>();

    useEffect(() => {
        timerRef.current = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    clearInterval(timerRef.current);
                    onComplete();
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [onComplete]);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${String(minutes).padStart(2, "0")}:${String(
            remainingSeconds
        ).padStart(2, "0")}`;
    };

    return { timeLeft, formatTime, setTimeLeft };
};

const UpgradeExam = () => {
    const openConfirm = useModalStore.use.openConfirm();
    const navigate = useNavigate();
    const [questions, setQuestions] = useState<ExamQuestion[]>([]);
    const [loading, setLoading] = useState(false);
    const [answers, setAnswers] = useState<Record<number, string | string[]>>(
        {}
    );
    const [showTimeUpModal, setShowTimeUpModal] = useState(false);
    const [showFailModal, setShowFailModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showDocumentsModal, setShowDocumentsModal] = useState(false);

    // SessionStorage 键名
    const DOCUMENTS_MODAL_STATE_KEY = 'upgrade_exam_documents_modal_state';

    const { timeLeft, formatTime, setTimeLeft } = useTimer(600, () => {
        setShowTimeUpModal(true);
    });

    const fetchQuestions = async () => {
        setLoading(true);
        try {
            const res = await api.exam.getExamQuestions(1, 5, 55, "random");
            if (res) {
                setQuestions(res);
            }
        } catch (error) {
            console.error("获取问题失败:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // 检查是否需要恢复DocumentsModal状态
        const savedState = sessionStorage.getItem(DOCUMENTS_MODAL_STATE_KEY);
        if (savedState) {
            try {
                const { showDocumentsModal: savedShowDocumentsModal } = JSON.parse(savedState);
                if (savedShowDocumentsModal) {
                    setShowDocumentsModal(true);
                    return; // 如果需要显示DocumentsModal，不执行后续逻辑
                }
            } catch (error) {
                console.error("恢复DocumentsModal状态失败:", error);
                // 清除无效的状态
                sessionStorage.removeItem(DOCUMENTS_MODAL_STATE_KEY);
            }
        }

        // 创建晋级管理进程，失败则跳转至首页
        const createUpgradeManagerProcess = async () => {
            try {
                const res = await api.exam.addProcess(
                    "profile_upgrade_partner_manager"
                );

                if (!res) {
                    navigate("/member/index", { replace: true });
                }
            } catch (error) {
                console.error("创建晋级管理进程失败:", error);
            }
        };
        createUpgradeManagerProcess();
        // 获取考试题目
        fetchQuestions();
    }, []);

    const handleBack = () => {
        openConfirm({
            title: "确定要返回？",
            message: "返回上一界面，当前答题将不会保存。",
            onConfirm: async () => {
                // 撤回进程
                // await api.exam.withdrawProcess(
                //     "profile_upgrade_partner_manager"
                // );
                navigate("/member/upgrade", { replace: true });
            },
        });
    };

    const handleAnswerChange = (
        questionId: number,
        value: string | string[]
    ) => {
        setAnswers((prev) => ({
            ...prev,
            [questionId]: value,
        }));
    };

    const getCompletedCount = () => {
        return Object.entries(answers).filter(([_, value]) => !isEmpty(value))
            .length;
    };

    const handleSubmit = () => {
        openConfirm({
            title: "确定提交试卷？",
            message: "提交后系统将自动评分，无法修改，请检查检查无误后再提交",
            onConfirm: () => {
                let allCorrect = true;
                questions.forEach((question) => {
                    const userAnswer = answers[question.id];
                    const correctAnswers = question.option
                        .map((opt, index) => ({
                            option_key: String.fromCharCode(65 + index),
                            is_answer: opt.is_answer,
                        }))
                        .filter((opt) => opt.is_answer === 1);

                    if (question.type === 0) {
                        // 单选题
                        if (userAnswer !== correctAnswers[0].option_key) {
                            allCorrect = false;
                        }
                    } else if (question.type === 1) {
                        // 多选题
                        const userAnswers = userAnswer as string[];
                        if (
                            userAnswers.length !== correctAnswers.length ||
                            !userAnswers.every((ans) =>
                                correctAnswers.some(
                                    (opt) => opt.option_key === ans
                                )
                            )
                        ) {
                            allCorrect = false;
                        }
                    }
                });

                if (allCorrect) {
                    setShowSuccessModal(true);
                    // 保存DocumentsModal状态到sessionStorage
                    sessionStorage.setItem(DOCUMENTS_MODAL_STATE_KEY, JSON.stringify({
                        showDocumentsModal: true,
                        timestamp: Date.now()
                    }));
                } else {
                    setShowFailModal(true);
                }
            },
        });
    };

    const renderQuestion = (question: ExamQuestion, index: number) => {
        if (question.type === 0) {
            // 单选题
            return (
                <Stack
                    gap={4}
                    key={question.id}
                >
                    <Text fw={700}>
                        {`${index}.${question.question}（${question.type_remark}）`}
                    </Text>
                    <Radio.Group
                        value={answers[question.id] as string}
                        onChange={(value) =>
                            handleAnswerChange(question.id, value)
                        }
                    >
                        <Stack gap={4}>
                            {question.option.map((opt, index) => (
                                <Radio
                                    key={String.fromCharCode(65 + index)}
                                    value={String.fromCharCode(65 + index)}
                                    label={`${String.fromCharCode(
                                        65 + index
                                    )}.${opt.option_name}`}
                                />
                            ))}
                        </Stack>
                    </Radio.Group>
                </Stack>
            );
        } else if (question.type === 1) {
            // 多选题
            return (
                <Stack
                    gap={4}
                    key={question.id}
                >
                    <Text fw={700}>
                        {`${index}.${question.question}（${question.type_remark}）`}
                    </Text>
                    <Checkbox.Group
                        value={answers[question.id] as string[]}
                        onChange={(value) =>
                            handleAnswerChange(question.id, value)
                        }
                    >
                        <Stack gap={4}>
                            {question.option.map((opt, index) => (
                                <Checkbox
                                    key={String.fromCharCode(65 + index)}
                                    value={String.fromCharCode(65 + index)}
                                    label={`${String.fromCharCode(
                                        65 + index
                                    )}.${opt.option_name}`}
                                />
                            ))}
                        </Stack>
                    </Checkbox.Group>
                </Stack>
            );
        }
        return null;
    };

    return (
        <Stack className="tw-h-full">
            <TimeUpModal
                opened={showTimeUpModal}
                onConfirm={() => {
                    setShowTimeUpModal(false);
                    navigate("/member/upgrade", { replace: true });
                }}
            />
            <Group className="tw-w-full tw-relative">
                <CnaButton
                    leftSection={<CaretLeft />}
                    variant="transparent"
                    className="tw-border-none"
                    size="md"
                    fw={700}
                    onClick={handleBack}
                >
                    返回
                </CnaButton>
                <Title
                    order={2}
                    className="tw-absolute tw-left-1/2 tw-transform -tw-translate-x-1/2 tw-text-base md:tw-text-2xl"
                >
                    晋级管理合伙人答题
                </Title>
                <Text className="tw-absolute tw-right-0">答题中</Text>
            </Group>

            <div className="tw-flex-1 tw-min-h-0">
                <Split className="tw-w-full tw-h-full">
                    <Split.Pane
                        initialWidth="70%"
                        minWidth="50%"
                        maxWidth="85%"
                    >
                        <Paper
                            p="md"
                            className="tw-overflow-auto tw-h-full"
                        >
                            <Stack>
                                {loading ? (
                                    <Text>加载中...</Text>
                                ) : (
                                    questions.map((question, index) => {
                                        return renderQuestion(
                                            question,
                                            index + 1
                                        );
                                    })
                                )}
                            </Stack>
                        </Paper>
                    </Split.Pane>
                    <Split.Resizer />
                    <Split.Pane
                        initialWidth="30%"
                        minWidth="15%"
                        maxWidth="50%"
                    >
                        <Paper
                            p="md"
                            className="tw-h-full"
                        >
                            <Stack>
                                <Text>
                                    离答题结束还有 {formatTime(timeLeft)}
                                </Text>

                                <Group>
                                    {questions.map((_, index) => (
                                        <CnaButton
                                            key={index}
                                            radius="xl"
                                            variant={
                                                answers[questions[index]?.id] &&
                                                !isEmpty(
                                                    answers[
                                                        questions[index]?.id
                                                    ]
                                                )
                                                    ? "filled"
                                                    : "outline"
                                            }
                                        >
                                            {index + 1}
                                        </CnaButton>
                                    ))}
                                </Group>
                                <Text>
                                    完成{getCompletedCount()}题/共
                                    {questions.length}题
                                </Text>
                            </Stack>
                        </Paper>
                    </Split.Pane>
                </Split>
            </div>

            <div className="tw-flex-shrink-0 tw-pb-4 md:tw-pb-0">
                <CnaButton
                    className="tw-w-full"
                    disabled={getCompletedCount() !== questions.length}
                    onClick={handleSubmit}
                >
                    完成并提交
                </CnaButton>
            </div>

            <FailModal
                opened={showFailModal}
                onPrev={async () => {
                    // 撤回进程
                    // await api.exam.withdrawProcess(
                    //     "profile_upgrade_partner_manager"
                    // );
                    setShowFailModal(false);
                    navigate("/member/upgrade", { replace: true });
                }}
                onRetry={() => {
                    setShowFailModal(false);
                    // 重置当前页面数据，重置时间，重新获取题目，重置时间为600秒
                    setAnswers({});
                    fetchQuestions();
                    setTimeLeft(600);
                }}
            />
            <SuccessModal
                opened={showSuccessModal}
                onNext={async () => {
                    setShowSuccessModal(false);
                    setShowDocumentsModal(true);
                }}
            />
            <DocumentsModal
                opened={showDocumentsModal}
                toIndex={() => {
                    setShowDocumentsModal(false);
                    // 清除sessionStorage状态
                    sessionStorage.removeItem(DOCUMENTS_MODAL_STATE_KEY);
                    navigate("/member/index", { replace: true });
                }}
            />
        </Stack>
    );
};

export default UpgradeExam;
