import dayjs from "dayjs";

// 文字配置接口
interface TextConfig {
    x: number;
    y: number;
    fontSize: number;
    fontFamily: string;
    color: string;
    fontWeight: string;
}

// 证书生成参数接口
interface CertificateParams {
    name: string;
    certNo: string;
    backgroundImagePath?: string;
    signatureImagePath?: string;
    textConfigs?: Record<string, TextConfig>;
    canvasWidth?: number;
    canvasHeight?: number;
}

// 默认文字配置
const defaultTextConfigs: Record<string, TextConfig> = {
    name: {
        x: 500,
        y: 300,
        fontSize: 48,
        fontFamily: "'Microsoft YaHei', 'SimHei', Arial, sans-serif",
        color: "orange",
        fontWeight: "bold",
    },
    certNo: {
        x: 500,
        y: 440,
        fontSize: 16,
        fontFamily: "'Microsoft YaHei', 'SimHei', Arial, sans-serif",
        color: "#34495E",
        fontWeight: "",
    },
    date: {
        x: 266,
        y: 570,
        fontSize: 16,
        fontFamily: "'Microsoft YaHei', 'SimHei', Arial, sans-serif",
        color: "#34495E",
        fontWeight: "",
    },
    signature: {
        x: 740,
        y: 580,
        fontSize: 16,
        fontFamily: "'Microsoft YaHei', 'SimHei', Arial, sans-serif",
        color: "#34495E",
        fontWeight: "",
    }
};

// 加载图片的通用函数
const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        const img = new window.Image();
        img.crossOrigin = "anonymous";
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error(`图片加载失败: ${src}`));
        img.src = src;
    });
};

// 压缩图片质量
const compressImage = (
    canvas: HTMLCanvasElement,
    quality: number = 0.8
): Promise<Blob> => {
    return new Promise((resolve, reject) => {
        try {
            canvas.toBlob(
                (blob) => {
                    if (blob) {
                        resolve(blob);
                    } else {
                        reject(new Error("图片压缩失败"));
                    }
                },
                "image/jpeg",
                quality
            );
        } catch (err) {
            reject(err);
        }
    });
};

// 检查图片大小并进一步压缩
const optimizeImageSize = async (
    canvas: HTMLCanvasElement,
    blob: Blob,
    maxSize: number = 2 * 1024 * 1024
): Promise<Blob> => {
    if (blob.size <= maxSize) {
        return blob;
    }

    // 计算压缩比例
    let quality = 0.8;
    let compressedBlob = blob;

    while (compressedBlob.size > maxSize && quality > 0.1) {
        quality -= 0.1;
        compressedBlob = await compressImage(canvas, quality);
    }

    return compressedBlob;
};

/**
 * 生成证书图片的纯函数
 * @param params 证书生成参数
 * @returns Promise<Blob> 生成的证书图片 Blob
 */
export const generateCertificateImage = async (params: CertificateParams): Promise<Blob> => {
    const {
        name,
        certNo,
        backgroundImagePath = "/images/station/partner_cert_cn.jpg",
        signatureImagePath = "/images/station/signature.png",
        textConfigs = defaultTextConfigs,
        canvasWidth = 1000,
        canvasHeight = 700,
    } = params;

    // 创建临时 canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error("Canvas 上下文获取失败");
    }

    // 设置画布尺寸
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // 加载背景图片
    const img = await loadImage(backgroundImagePath);

    // 绘制背景图片
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    const date = dayjs().format("YYYY年MM月DD日");

    // 绘制姓名
    const nameConfig = textConfigs.name;
    ctx.fillStyle = nameConfig.color;
    ctx.font = `${nameConfig.fontWeight} ${nameConfig.fontSize}px ${nameConfig.fontFamily}`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    // 添加文字阴影效果
    ctx.shadowColor = "rgba(0, 0, 0, 0.1)";
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    ctx.fillText(name, nameConfig.x, nameConfig.y);

    // 绘制日期
    const dateConfig = textConfigs.date;
    ctx.fillStyle = dateConfig.color;
    ctx.font = `${dateConfig.fontWeight} ${dateConfig.fontSize}px ${dateConfig.fontFamily}`;
    ctx.fillText(date, dateConfig.x, dateConfig.y);

    // 绘制证书号
    const certNoConfig = textConfigs.certNo;
    ctx.fillStyle = certNoConfig.color;
    ctx.font = `${certNoConfig.fontWeight} ${certNoConfig.fontSize}px ${certNoConfig.fontFamily}`;
    ctx.fillText(certNo, certNoConfig.x, certNoConfig.y);

    // 绘制签名
    const signatureConfig = textConfigs.signature;
    ctx.fillStyle = signatureConfig.color;
    ctx.font = `${signatureConfig.fontWeight} ${signatureConfig.fontSize}px ${signatureConfig.fontFamily}`;
    ctx.fillText("签名", signatureConfig.x, signatureConfig.y);

    // 清除阴影效果
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 加载并绘制签名图片
    const signatureImg = await loadImage(signatureImagePath);

    // 设定签名图位置和大小
    const sigWidth = 260;
    const sigHeight = 120;
    const sigX = 620;
    const sigY = 470;
    ctx.drawImage(signatureImg, sigX, sigY, sigWidth, sigHeight);

    // 压缩并优化图片大小
    const blob = await compressImage(canvas, 0.8);
    const optimizedBlob = await optimizeImageSize(canvas, blob, 2 * 1024 * 1024);

    console.log("原始图片大小:", blob.size / 1024 / 1024, "MB");
    console.log("优化后图片大小:", optimizedBlob.size / 1024 / 1024, "MB");

    return optimizedBlob;
};

/**
 * 生成证书图片并返回 data URL
 * @param params 证书生成参数
 * @returns Promise<string> 生成的证书图片 data URL
 */
export const generateCertificateDataUrl = async (params: CertificateParams): Promise<string> => {
    const {
        canvasWidth = 1000,
        canvasHeight = 700,
    } = params;

    // 创建临时 canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error("Canvas 上下文获取失败");
    }

    // 设置画布尺寸
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // 加载背景图片
    const img = await loadImage(params.backgroundImagePath || "/images/station/partner_cert_cn.jpg");

    // 绘制背景图片
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    const date = dayjs().format("YYYY年MM月DD日");
    const textConfigs = params.textConfigs || defaultTextConfigs;

    // 绘制姓名
    const nameConfig = textConfigs.name;
    ctx.fillStyle = nameConfig.color;
    ctx.font = `${nameConfig.fontWeight} ${nameConfig.fontSize}px ${nameConfig.fontFamily}`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    // 添加文字阴影效果
    ctx.shadowColor = "rgba(0, 0, 0, 0.1)";
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    ctx.fillText(params.name, nameConfig.x, nameConfig.y);

    // 绘制日期
    const dateConfig = textConfigs.date;
    ctx.fillStyle = dateConfig.color;
    ctx.font = `${dateConfig.fontWeight} ${dateConfig.fontSize}px ${dateConfig.fontFamily}`;
    ctx.fillText(date, dateConfig.x, dateConfig.y);

    // 绘制证书号
    const certNoConfig = textConfigs.certNo;
    ctx.fillStyle = certNoConfig.color;
    ctx.font = `${certNoConfig.fontWeight} ${certNoConfig.fontSize}px ${certNoConfig.fontFamily}`;
    ctx.fillText(params.certNo, certNoConfig.x, certNoConfig.y);

    // 绘制签名
    const signatureConfig = textConfigs.signature;
    ctx.fillStyle = signatureConfig.color;
    ctx.font = `${signatureConfig.fontWeight} ${signatureConfig.fontSize}px ${signatureConfig.fontFamily}`;
    ctx.fillText("签名", signatureConfig.x, signatureConfig.y);

    // 清除阴影效果
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 加载并绘制签名图片
    const signatureImg = await loadImage(params.signatureImagePath || "/images/station/signature.png");

    // 设定签名图位置和大小
    const sigWidth = 260;
    const sigHeight = 120;
    const sigX = 620;
    const sigY = 470;
    ctx.drawImage(signatureImg, sigX, sigY, sigWidth, sigHeight);

    // 返回 data URL
    return canvas.toDataURL("image/jpeg", 0.95);
};

// 导出类型
export type { TextConfig, CertificateParams };
