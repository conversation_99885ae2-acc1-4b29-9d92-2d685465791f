import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { CnaButton, PhoneInput } from "@code.8cent/react/components";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import { useRequest } from "ahooks";
import { useGetCodeCountdown } from "@code.8cent/react/VerifyModal";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import { TextInput } from "@mantine/core";
import { ArrowClockwise } from "@phosphor-icons/react";
import { Link, useNavigate } from "react-router-dom";
import useWizardStore from "@code.8cent/store/wizard";
import { INTERVIEW_STATUS, INTERVIEW_STATUS_TEXT } from "@/hoc/withRouteGuard";
import { initUserData } from "@/utils/initUserData";
import useRegisterStore from "@/store/register";

type PhoneLoginFormInput = {
    phone: string;
    prefixID: string;
    captcha: string;
    code: string;
};

const phoneLoginSchema = z.object({
    phone: z.string().regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    prefixID: z.string().min(1, "form.country.code"),
    captcha: z.string().min(1),
    code: z.string().min(1),
});

const PhoneLoginForm = () => {
    const { countryID } = useSettingStore();

    const navigate = useNavigate();

    const { countdown, startCountdown } = useGetCodeCountdown();

    const { filteredCountryDatas } = useDataStore();

    const { lang } = useSettingStore();

    const { setRegisterSetting, setState: setWizardState } = useWizardStore();
    const { setStepV3, setInterviewStatus } = useRegisterStore();

    const { getValues, control, handleSubmit, trigger } =
        useForm<PhoneLoginFormInput>({
            defaultValues: {
                phone: "",
                prefixID: countryID,
                captcha: "",
                code: "",
            },
            resolver: zodResolver(phoneLoginSchema),
        });

    const {
        run: getCaptchaCode,
        data: captchaRes,
        loading: gettingCode,
    } = useRequest(async () => {
        let { result, error } = await cnaRequest<{ key: string; img: string }>(
            "/api/v1/captcha",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    });

    const { run: getPhoneCode, loading: requestingPhoneCode } = useRequest(
        async () => {
            let phoneOk = await trigger("phone");

            if (phoneOk === true) {
                const cleanPhone = getValues("phone").replace(/\s/g, '')
                let { error } = await cnaRequest(
                    "/api/v1/login/sendCode",
                    "POST",
                    {
                        phone: cleanPhone,
                    }
                );

                if (!error) {
                    startCountdown(60);
                } else {
                    noty.error(error.message);
                }
            }
        },
        {
            manual: true,
        }
    );

    const { run: phoneLogin, loading: loggingIn } = useRequest(
        async (data: PhoneLoginFormInput) => {
            const cleanPhone = data.phone.replace(/\s/g, '');
            const { result, error } = await cnaRequest<{
                token: string;
                profile_status: number;
                profile_id: number;
                register_setting: string[];
            }>("/api/v1/login", "POST", {
                phone: cleanPhone,
                code: data.code,
                captcha_code: data.captcha,
                captcha_key: captchaRes?.key,
            });

            if (error) {
                getCaptchaCode();
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;

                await window.localForage.setItem("cna-token", data.token);

                // 初始化用户数据（通知列表等）
                await initUserData();

                // 判断用户状态 data.profile_status 1 => 待付款，2 => 待审核
                // 如果待付款就跳到 register, step-v3 => payment-qr
                if (data.profile_status === 1) {
                    setStepV3("payment-qr");
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果待审核，先判断 register_setting 是否有 qualification 或者 business_case,
                // 有则跳转register，step-v3 => qualification or business_case
                if (data.profile_status === 2) {
                    if (data.register_setting.includes("qualification")) {
                        setStepV3("qualification");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    if (data.register_setting.includes("business_case")) {
                        setStepV3("business_case");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    // 检查面试状态
                    const { result: interviewResult, error: interviewError } =
                        await cnaRequest("/api/v1/interview/status", "GET");

                    if (interviewError) {
                        console.error(
                            "Failed to fetch interview status:",
                            interviewError
                        );
                        setStepV3("interview-status");
                        setInterviewStatus("pending");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    const status =
                        interviewResult?.data?.status ??
                        INTERVIEW_STATUS.PENDING;
                    const statusText =
                        INTERVIEW_STATUS_TEXT[status] ?? "pending";

                    // 面试成功，并且已经展示过成功状态的，跳到 wizard
                    const hasShownInterviewSuccess =
                        await window.localForage.getItem(
                            `hasShownInterviewSuccess_${data.profile_id}`
                        );
                    if (
                        status === INTERVIEW_STATUS.SUCCESS &&
                        hasShownInterviewSuccess
                    ) {
                        navigate("/account/wizard", { replace: true });
                        return;
                    }

                    setStepV3("interview-status");
                    setInterviewStatus(statusText);
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果不需要面试，检查 register_setting, 不为空则调到wizard, 为空则跳到 welcome-video
                if (data.register_setting?.length > 0) {
                    await window.localForage.setItem(
                        "register-setting",
                        data.register_setting
                    );
                    setRegisterSetting(data.register_setting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                } else {
                    await window.localForage.setItem(
                        `hasShownInterviewSuccess_${data.profile_id}`,
                        true
                    );
                    navigate("/account/welcome-video", { replace: true });
                }
            }
        },
        {
            manual: true,
        }
    );

    return (
        <form
            onSubmit={handleSubmit(phoneLogin, (error) => console.log(error))}
        >
            <Controller
                control={control}
                name="phone"
                render={({ field, fieldState }) => (
                    <PhoneInput
                        prefixFlagKey={"countryISOCode2"}
                        prefixLabelKey={"countryCode"}
                        prefixValueKey={"countryID"}
                        label={t("project.company_form.label.phone", lang)}
                        wrapperProps={{
                            className: "tw-mb-3",
                            withAsterisk: true,
                        }}
                        prefixProps={{
                            readOnly: true,
                            w: 78,
                            rightSectionWidth: 0,
                            value: getValues("prefixID"),
                        }}
                        inputProps={{
                            ...field,
                            onKeyDown: (e) => {
                                if (e.key === ' ') e.preventDefault();
                            },
                            placeholder: t("login.placeholder.phone", lang),
                            rightSectionWidth: 100,
                            classNames: {
                                section:
                                    "!tw-left-auto !tw-right-2 tw-absolute",
                                input: "tw-pr-[100px] tw-rounded-l-none",
                            },
                            rightSection: (
                                <div className="tw-h-full tw-flex tw-items-center tw-justify-center">
                                    {countdown > 0 ? (
                                        <CnaButton
                                            variant="transparent"
                                            color="dark.5"
                                            className="tw-cursor-not-allowed tw-whitespace-nowrap"
                                        >
                                            {`${Math.floor(
                                                countdown / 1000
                                            )} ${t(
                                                "validate.after.second",
                                                lang
                                            )}`}
                                        </CnaButton>
                                    ) : (
                                        <CnaButton
                                            variant="transparent"
                                            color="dark.5"
                                            onClick={getPhoneCode}
                                            className="!tw-bg-transparent tw-whitespace-nowrap"
                                            loading={requestingPhoneCode}
                                        >
                                            {t(
                                                "forget_password.btn.send_validate_code",
                                                lang
                                            )}
                                        </CnaButton>
                                    )}
                                </div>
                            ),
                            error: fieldState.error
                                ? t(fieldState.error?.message, lang)
                                : false,
                        }}
                        data={filteredCountryDatas()}
                    />
                )}
            />
            <Controller
                control={control}
                name="code"
                render={({ field, fieldState: { error } }) => (
                    <TextInput
                        className="tw-mb-3"
                        error={error ? true : false}
                        label={t("validation.code", lang)}
                        placeholder={t("validation.enter.code", lang)}
                        autoComplete="off"
                        withAsterisk
                        {...field}
                    />
                )}
            />

            <Controller
                name="captcha"
                control={control}
                render={({ field, fieldState }) => (
                    <TextInput
                        classNames={{
                            // input: "tw-pr-[160px]",
                            section:
                                "[&:first-of-type]:!tw-left-auto [&:first-of-type]:tw-absolute [&:last-of-type]:tw-right-0",
                        }}
                        label={t("login.label.captcha", lang)}
                        placeholder={t("login.placeholder.captcha", lang)}
                        className="tw-mb-5"
                        leftSectionWidth={110}
                        rightSectionWidth={36}
                        leftSectionProps={{
                            className: "tw-justify-end",
                        }}
                        leftSection={
                            captchaRes && (
                                <img
                                    src={captchaRes.img}
                                    className="tw-block tw-h-[32px]"
                                    alt="reCaptcha"
                                />
                            )
                        }
                        rightSection={
                            <div className="tw-h-full tw-flex tw-items-center tw-justify-center">
                                <CnaButton
                                    variant="transparent"
                                    color={"dark.3"}
                                    onClick={getCaptchaCode}
                                    className="tw-font-normal tw-p-0"
                                    disabled={gettingCode}
                                >
                                    <ArrowClockwise
                                        className={
                                            gettingCode ? "tw-animate-spin" : ""
                                        }
                                        size={24}
                                    />
                                </CnaButton>
                            </div>
                        }
                        withAsterisk
                        {...field}
                        onChange={(e) => {
                            field.onChange(e.target.value.toUpperCase());
                        }}
                        error={fieldState.error ? true : false}
                    />
                )}
            />
            <div className="tw-flex tw-my-3 tw-space-x-3">
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="outline"
                        color="basic"
                        component={Link}
                        // to="/account/register"
                        to="/account/welcome"
                    >
                        {t("form.sign.up", lang)}
                    </CnaButton>
                </div>
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="filled"
                        color="basic"
                        // loading={loggingIn}
                        type="submit"
                    >
                        {t("login.btn.login", lang)}
                    </CnaButton>
                </div>
            </div>
        </form>
    );
};

export default PhoneLoginForm;
