import { useRouteError, ErrorResponse } from "react-router-dom";

const RouteError: React.FC<{ className: string }> = ({ className }) => {
    const error = useRouteError() as ErrorResponse & {
        message: string;
        stack: any;
    };

    console.log("router-error: ", error);

    return (
        <div className={className}>
            <main className="tw-grid tw-min-h-full tw-place-items-center tw-bg-white tw-px-6 tw-py-24 sm:tw-py-32 lg:tw-px-8">
                <div className="tw-text-center">
                    <p className="tw-text-base tw-font-semibold tw-text-basic-5">
                        {error.status}
                    </p>
                    <h1  style={{ letterSpacing: "-0.025em" }} className="tw-mt-4 tw-text-3xl tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-5xl">
                        {error.statusText || "Sorry, something went wrong."}
                    </h1>
                    <p className="tw-mt-6 tw-text-base tw-leading-7 tw-text-gray-600">
                        {error.message}
                    </p>
                </div>
            </main>
        </div>
    );
};

export default RouteError;
