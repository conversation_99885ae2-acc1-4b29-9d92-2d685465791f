import React from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import Nav from "./components/Nav";
import { Box, Group, Image, Paper, Stack, Text, Title } from "@mantine/core";
import {
    User,
    LockSimple,
    NavigationArrow,
    BellRinging,
} from "@phosphor-icons/react";
const LeftNavItem = [
    { name: "账号常规", link: "/settings/user", icon: <User /> },
    { name: "安全与登录", link: "/settings/security", icon: <LockSimple /> },
    { name: "语言与地区", link: "/settings/region", icon: <NavigationArrow /> },
    {
        name: "消息与通知",
        link: "/settings/notifications",
        icon: <BellRinging />,
    },
    {
        name: "账号管理员",
        link: "/settings/manager",
        icon: <BellRinging />,
    },
    {
        name: "模版和架构",
        link: "/settings/tabs",
        icon: <BellRinging />,
    },
    {
        name: "支付",
        link: "/settings/payment",
        icon: <BellRinging />,
    },
    {
        name: "活动日志",
        link: "/settings/logs",
        icon: <BellRinging />,
    },
    {
        name: "帮助与支持",
        link: "/settings/support",
        icon: <BellRinging />,
    },
];

const SettingsLayout = () => {
   const navigate =useNavigate()
   const { pathname } = useLocation();
   
    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw] tw-flex-col md:tw-flex-row">
            <Nav />
            <div className="tw-w-full">
                <div className="tw-flex  tw-h-[calc(100vh-58px)] tw-mt-[58px]">
                    <div className="md:tw-flex md:tw-w-[240px] tw-border-r tw-bg-[#070d3a] tw-hidden tw-flex-col tw-w-0 tw-transition-all">
                        <div className="tw-flex-1 tw-py-3 tw-overflow-y-auto">
                            {LeftNavItem.map((item, index) => {
                                return (
                                    <div
                                        className={`tw-flex tw-text-white tw-rounded-md tw-m-2 tw-px-4 tw-py-2 tw-items-center tw-justify-start${
                                            pathname == item.link
                                                ? " tw-font-bold tw-bg-[#2757c3]"
                                                : ""
                                        } tw-cursor-pointer`}
                                        key={index}
                                        onClick={()=>{
                                            navigate(item.link)
                                        }}
                                    >
                                        {item.icon}
                                        <Text className="tw-flex-1 tw-text-base tw-text-left tw-ml-2">
                                            {item.name}
                                        </Text>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                        <Stack
                            className="tw-p-20 tw-bg-[#f3f6fb]  tw-overflow-y-auto tw-h-full tw-w-full "
                            align="center"
                        >
                            <Outlet />
                        </Stack>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SettingsLayout;
