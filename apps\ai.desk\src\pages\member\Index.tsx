import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import {
    SimpleGrid,
    Image,
    Card,
    Text,
    Group,
    Stack,
    ScrollArea,
} from "@mantine/core";
import { ArrowRight } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import useProfileStore from "@/store/profile";

const getItems = (roleLevel?: number, has_manager_power?: number) => {
    const isManager = roleLevel === 2 || has_manager_power === 1;
    return [
        {
            title: "业务管理",
            subTitle: "帮助您跟进项目进度，在服务企业中获得收益分成",
            icon: "/images/station/index-icon-case.svg",
            icon_dark: "/images/station/index-icon-case-dark.svg",
            link: "/member/business",
        },
        {
            title: "出入款管理",
            subTitle: "查看付款记录和接收业务服务费，帮助合伙人掌握财务状况",
            icon: "/images/station/index-icon-money.svg",
            icon_dark: "/images/station/index-icon-money-dark.svg",
            link: "/member/billing",
        },
        {
            title: isManager ? "管理合伙人" : "晋升管理合伙人",
            subTitle: "晋级为管理合伙人后，推荐更多合伙人加盟获得津贴",
            icon: "/images/station/index-icon-user.svg",
            icon_dark: "/images/station/index-icon-user-dark.svg",
            link: isManager ? "/member/associates" : "/member/upgrade",
        },
        {
            title: "个人简介",
            subTitle: "合伙人个人资料 ，包括业务案例、资格证书、牌照认证",
            icon: "/images/station/index-icon-list.svg",
            icon_dark: "/images/station/index-icon-list-dark.svg",
            link: "/member/summary",
        },
        {
            title: "资料文档",
            subTitle: "集中存储企业文档与参考资料，支持合伙人高效工作",
            icon: "/images/station/index-icon-file.svg",
            icon_dark: "/images/station/index-icon-file-dark.svg",
            link: "/member/document",
        },
    ];
};

const ItemCard = ({ item }: { item: ReturnType<typeof getItems>[number] }) => {
    const navigate = useNavigate();
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div
            className="tw-relative tw-cursor-pointer"
            onClick={() => {
                navigate(item.link, { replace: true });
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <Card
                radius="lg"
                p={{ base: "lg", md: "xl" }}
                className="tw-max-h-[150px] md:tw-max-h-[200px]"
                style={{
                    background: isHovered
                        ? "linear-gradient(181deg, #425097 -4.69%, #3141A6 84.04%, #3B69DD 107.33%)"
                        : "",
                }}
            >
                <Stack justify="space-between">
                    <Stack>
                        <Text
                            size="lg"
                            fw={700}
                            style={{
                                color: isHovered ? "white" : "#292F53",
                            }}
                        >
                            {item.title}
                        </Text>
                        <Text
                            c="dimmed"
                            size="sm"
                            mb="md"
                            className="tw-max-w-[70%] tw-break-words tw-whitespace-normal"
                        >
                            {item.subTitle}
                        </Text>
                    </Stack>

                    <Group
                        justify="flex-start"
                        className="tw-hidden md:tw-flex"
                    >
                        <CnaButton
                            variant="transparent"
                            size="sm"
                            radius="lg"
                            style={{
                                backgroundColor: isHovered
                                    ? "white"
                                    : "#EAEEFF",
                            }}
                        >
                            <ArrowRight
                                color="#19288F"
                                weight="bold"
                            />
                        </CnaButton>
                    </Group>
                </Stack>
            </Card>
            <Image
                src={isHovered ? item.icon_dark : item.icon}
                className="tw-absolute tw-bottom-0 tw-right-0 tw-max-w-[120px] md:tw-max-w-[200px] tw-pointer-events-none"
                draggable={false}
            />
        </div>
    );
};

const MemberIndex = () => {
    const profile = useProfileStore();
    const items = getItems(profile.role_level, profile.has_manager_power);

    return (
        <div className="tw-h-full tw-flex tw-flex-col">
            <PageHeader />
            {/* <ScrollArea className="tw-h-[calc(100vh-100px)] md:tw-h-[calc(100vh-250px)]"> */}
            <div className="tw-flex-1 tw-overflow-y-auto tw-mt-[40px] tw-overflow-x-hidden md:tw-px-8" style={{ WebkitOverflowScrolling: 'touch' }}>
                <SimpleGrid
                    cols={{ base: 1, sm: 2, md: 2, lg: 2, xl: 2 }}
                    // mt={40}
                    spacing={{ base: 20 }}
                    verticalSpacing={{ base: 24, md: 32 }}
                    // pb={40}
                    >
                    {items.map((item) => (
                        <ItemCard
                        key={item.title}
                        item={item}
                        />
                    ))}
                </SimpleGrid>
            </div>
            {/* </ScrollArea> */}
        </div>
    );
};

export default MemberIndex;
