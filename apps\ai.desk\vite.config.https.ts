import { defineConfig, normalizePath } from "vite";
import react from "@vitejs/plugin-react-swc";
import { resolve, join, dirname } from "node:path";
import { analyzer } from "vite-bundle-analyzer";
import { createRequire } from "node:module";
import svgr from "vite-plugin-svgr";
import { viteStaticCopy } from "vite-plugin-static-copy";

const require = createRequire(import.meta.url);

const cMapsDir = normalizePath(join(dirname(require.resolve("pdfjs-dist/package.json")), "cmaps"));
const standardFontsDir = normalizePath(
    join(dirname(require.resolve("pdfjs-dist/package.json")), "standard_fonts")
);

// https://vitejs.dev/config/
export default defineConfig({
    server: {
        port: 443,
        https: {
            key: `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
            cert: `-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----`,
        },
    },
    envPrefix: "APP_",
    plugins: [
        react(),
        svgr(),
        viteStaticCopy({
            targets: [
                { src: cMapsDir, dest: "" },
                { src: standardFontsDir, dest: "" },
            ],
        }),
        analyzer({
            analyzerMode: "static",
        }),
    ],
    build: {
        sourcemap: false,
        minify: "esbuild", // 使用 esbuild 进行代码压缩 (minify)
        outDir: "../../dist/apps/ai.desk",
        emptyOutDir: true,
        reportCompressedSize: true,
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes("node_modules")) {
                        if (id.includes("pdfjs-dist")) {
                            // 将 pdfjs-dist 单独打包成 pdfjs.[hash].js
                            return "pdfjs";
                        }

                        if (id.includes("react-pdf")) {
                            return "react-pdf";
                        }

                        // 将其他第三方库 (lib 代码) 单独打包到 vendor.[hash].js
                        return "vendor";
                    }
                },
            },
        },
    },
    esbuild: {
        drop: ["console"],
    },
    resolve: {
        alias: {
            "@": resolve(__dirname, "src"),
        },
    },
});
