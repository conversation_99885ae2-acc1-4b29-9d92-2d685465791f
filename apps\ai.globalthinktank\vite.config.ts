import { defineConfig, normalizePath } from "vite";
import react from "@vitejs/plugin-react-swc";
import { resolve, join, dirname } from "node:path";
import { analyzer } from "vite-bundle-analyzer";
import { createRequire } from "node:module";
import svgr from "vite-plugin-svgr";
import { viteStaticCopy } from "vite-plugin-static-copy";

const require = createRequire(import.meta.url);

const cMapsDir = normalizePath(
    join(dirname(require.resolve("pdfjs-dist/package.json")), "cmaps")
);
const standardFontsDir = normalizePath(
    join(dirname(require.resolve("pdfjs-dist/package.json")), "standard_fonts")
);

// https://vitejs.dev/config/
export default defineConfig({
    envPrefix: "APP_",
    plugins: [
        react(),
        svgr(),
        viteStaticCopy({
            targets: [
                { src: cMapsDir, dest: "" },
                { src: standardFontsDir, dest: "" },
            ],
        }),
        analyzer({
            analyzerMode: "static",
        }),
    ],
    build: {
        sourcemap: false,
        minify: "esbuild", // 使用 esbuild 进行代码压缩 (minify)
        outDir: "../../dist/apps/ai.globalthinktank",
        emptyOutDir: true,
        reportCompressedSize: true,
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes("node_modules")) {
                        if (id.includes("pdfjs-dist")) {
                            // 将 pdfjs-dist 单独打包成 pdfjs.[hash].js
                            return "pdfjs";
                        }

                        if(id.includes("react-pdf")){
                            return "react-pdf";
                        }

                        // 将其他第三方库 (lib 代码) 单独打包到 vendor.[hash].js
                        return "vendor";
                    }
                },
            },
        },
    },
    esbuild: {
        drop: ["console"]
    },
    resolve: {
        alias: {
            "@": resolve(__dirname, "src"),
        },
    },
});
