import AssociateFlowCard from "@/components/layouts/AssociateFlowCard";
import AssociateFlowLayout from "@/components/layouts/AssociateFlowLayout";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { Group, Paper, Stack, Title } from "@mantine/core";
import { CaretLeft } from "@phosphor-icons/react";
import { useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect, useMemo, useCallback } from "react";
import ExamPdfViewer from "@/components/member/business/ExamPdfViewer";
import { getFileWithCache } from "@code.8cent/utils";
import {
    getFileByUrl,
    FileLoadingProgress,
} from "@code.8cent/react/FileViewer";
import api from "@/apis";

const Company = () => {
    const navigate = useNavigate();
    const location = useLocation();

    // PDF 拉到底部为已读
    const [isReadFile, setIsReadFile] = useState(false);
    const [document, setDocument] = useState<File | undefined>();
    const [isLoading, setIsLoading] = useState(false);

    // 下载进度状态
    const [downloadProgress, setDownloadProgress] = useState({
        loaded: 0,
        total: 0,
        rate: 0,
    });

    useEffect(() => {
        loadCompanyProfile();
    }, []);

    const loadCompanyProfile = async () => {
        setIsLoading(true);
        setDownloadProgress({ loaded: 0, total: 0, rate: 0 });

        try {
            // 使用缓存获取企业简介文档
            const cachedFile = await getFileWithCache(
                "company_profile", // 缓存键
                "", // 这里先设为空，后面会通过API获取完整URL
                async (url: string) => {
                    // 获取文档token
                    const token = await api.team.gerneateDocToken({ type: 1 });
                    if (token) {
                        const fullUrl = `${window.api_base_url}/api/v1/team/downloadDoc/${token}`;
                        return await getFileByUrl(
                            fullUrl,
                            (progressEvent: any) => {
                                // 更新下载进度
                                const { loaded, total } = progressEvent;
                                const rate = progressEvent.rate || 0;
                                setDownloadProgress({
                                    loaded: loaded || 0,
                                    total: total || 0,
                                    rate: rate,
                                });
                            }
                        );
                    }
                    return null;
                },
                {
                    expireTime: 24 * 60 * 60 * 1000, // 24小时缓存
                    cachePrefix: "company_profile_",
                }
            );

            if (cachedFile) {
                setDocument(cachedFile);
            }
            setIsLoading(false);
        } catch (error) {
            console.error("加载企业简介失败:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleReadComplete = useCallback(() => {
        setIsReadFile(true);
        console.log("PDF已阅读完成");
    }, []);

    const fileViewerElement = useMemo(
        () => (
            <div className="tw-w-full tw-h-[500px]">
                {document && (
                    <ExamPdfViewer
                        file={document}
                        onReachedBottom={handleReadComplete}
                    />
                )}
            </div>
        ),
        [document, handleReadComplete]
    );

    const handleBack = () => {
        const queryParams = new URLSearchParams(location.search);
        const refer = queryParams.get("refer");
        const url = refer
            ? "/account/fields?refer=" + refer
            : "/account/fields";
        navigate(url, { replace: true });
    };

    const handleJoin = async () => {
        // 登录流程，如果是新用户，则跳转到注册流程
        const is_new = await window.localForage.getItem("is_new");
        if (is_new === "1") {
            // 获取refer参数
            const queryParams = new URLSearchParams(location.search);
            const refer = queryParams.get("refer");
            sessionStorage.setItem("refer", refer || "");
            navigate("/account/register", { replace: true });
            return;
        }
        // 跳转到 AiPlus 处理
        // 自定义参数
        const reffer = sessionStorage.getItem("refer") || "";
        const occupation_name = await window.localForage.getItem("occupation_name") || ""; // 职业名称
        const occupation_id = await window.localForage.getItem("occupation_id") || ""; // 职业ID

        const url = `${window.aiplus_app_url}?appId=${window.aiplus_app_id}&login_type=personal&type=register&refer=${reffer}&occupation_name=${occupation_name}&occupation_id=${occupation_id}`;
        window.location.href = url;
    };

    return (
        <AssociateFlowLayout>
            <AssociateFlowCard className="tw-w-[1000px] tw-items-center">
                <Stack className="tw-w-full">
                    <Group className="tw-w-full tw-relative">
                        <CnaButton
                            leftSection={<CaretLeft />}
                            variant="transparent"
                            className="tw-border-none"
                            size="md"
                            fw={700}
                            onClick={handleBack}
                        >
                            返回
                        </CnaButton>
                        <Title
                            order={2}
                            className="tw-absolute tw-left-1/2 tw-transform -tw-translate-x-1/2 tw-text-base md:tw-text-2xl"
                        >
                            C&A 企业简介
                        </Title>
                    </Group>
                    <Paper
                        p="md"
                        className="tw-relative"
                    >
                        {/* 使用 FileLoadingProgress 显示下载进度 */}
                        <FileLoadingProgress
                            visible={isLoading}
                            loaded={downloadProgress.loaded}
                            total={downloadProgress.total}
                            rate={downloadProgress.rate}
                        />

                        {/* 加载 C&A 企业简介 pdf 文件 */}
                        {fileViewerElement}

                        <CnaButton
                            className="tw-w-full tw-mt-4"
                            onClick={handleJoin}
                            disabled={!isReadFile}
                        >
                            我要加盟
                        </CnaButton>
                    </Paper>
                </Stack>
            </AssociateFlowCard>
        </AssociateFlowLayout>
    );
};

export default Company;
