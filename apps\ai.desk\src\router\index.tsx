import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON> } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import RootLayout from "@/components/layouts/RootLayout";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import LoginPage from "@/pages/account/login/Index";
import LoginPageV2 from "@/pages/account/login/IndexV2";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
import withRouteGuard from "@/hoc/withRouteGuard";
import AccountRegisterPageV3 from "@/pages/account/RegisterV3";
import AccountRegisterPayment from "@/pages/account/RegisterPayment";
import AccountDocuments from "@/pages/account/Documents";
import TeamWelcomePage from "@/pages/account/WelcomeV2";
import WelcomeVideo from "@/pages/account/WelcomeVideo";
import OccupationSelectPage from "@/pages/account/OccupationSelect";
import DashboardLayout2 from "@/components/layouts/DashboardLayout2";
import IndexLayout from "@/components/layouts/IndexLayout";
import AIAssistant from "@/components/ai";
import MemberIndex from "@/pages/member/Index";
import Interview from "@/pages/member/Interview";
import Associates from "@/pages/member/associate/Index";
import SettingPage from "@code.8cent/react/pages/SettingPage";
import NotificationIndex from "@/pages/member/notification/Index";
import NotificationDetail from "@/pages/member/notification/Detail";
import AccountWizardPage from "@/pages/wizard/Index";
import Profile from "@/pages/member/Profile";
import Summary from "@/pages/member/Summary";
import Document from "@/pages/member/Document";
import Billing from "@/pages/member/Billing";
import ExamIndex from "@/pages/member/exam/Index";
import Upgrade from "@/pages/member/Upgrade";
import UpgradeExam from "@/pages/member/UpgradeExam";
import BusinessIndex from "@/pages/member/business/Index";
import TeamHubPage from "@/pages/team/Hub";
import CommunityIndex from "@/pages/member/community/Index";
import CommunityDetail from "@/pages/member/community/Detail";
import Company from "@/pages/account/Company";
import AiPlusTargetPage from "@/pages/aiPlus/Target";
import AiPlusTargetPageV2 from "@/pages/aiPlus/TargetV2";
import DynamicTablePage from "@/pages/project/dynamicTable";
import FinancialStatementsPage from "@/pages/project/financialStatements";
import StaticTablePage from "@/pages/project/staticTable";
import ApplyPage from "@/pages/project/ apply";
import CompanyLayout from "@/components/apply/companyLayout";
import Welcome from "@/components/apply/welcome";

const GuardedRootLayout = withRouteGuard(RootLayout, {
    publicRoutes: [
        "/account/*",
        "/team",
        "/team/*",
        "/applyWelcome",
        "/intro",
        "/ai-plus",
        "/ai-plus/*",
    ],
});

const router = createBrowserRouter([
    {
        path: "/",
        element: <GuardedRootLayout />,
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        children: [
            {
                path: "team",
                element: <TeamHubPage />,
            },
            {
                path: "ai-plus",
                element: <AiPlusTargetPageV2 />,
            },
            {
                path: "intro",
                element: (
                    <div className="tw-h-screen tw-w-screen">
                        <iframe
                            className="tw-h-full tw-w-full"
                            src="/intro/index.html"
                        />
                    </div>
                ),
            },
            {
                path: "account/welcome",
                element: <TeamWelcomePage />,
            },
            { path: "applyWelcome", element: <Welcome /> },

            {
                path: "account/fields",
                element: <OccupationSelectPage />,
            },
            {
                path: "account/company",
                element: <Company />,
            },
            {
                path: "account/login",
                element: <LoginPage />,
                // element: <LoginPageV2 />,
            },
            {
                path: "account/forget-password",
                element: <ForgetPasswordPage />,
            },
            {
                path: "account/register",
                element: <AccountRegisterPageV3 />,
            },
            {
                path: "account/register-payment",
                element: <AccountRegisterPayment />,
            },
            {
                path: "account/welcome-video",
                element: <WelcomeVideo />,
            },
            {
                path: "account/documents",
                element: <AccountDocuments />,
            },
            {
                path: "account/wizard",
                element: <AccountWizardPage />,
            },
            {
                path: "index",
                element: <DashboardLayout2 />,
                children: [
                    {
                        path: "assistant",
                        element: <AIAssistant />,
                    },
                ],
            },

            {
                path: "company",
                element: <CompanyLayout />,
                children: [
                    { path: "apply", element: <ApplyPage /> },
                    { path: "dynamicTable", element: <DynamicTablePage /> },
                    {
                        path: "financialTable",
                        element: <FinancialStatementsPage />,
                    },
                    { path: "staticTable", element: <StaticTablePage /> },
                ],
            },
            {
                path: "member",
                element: <DashboardLayout2 />,
                children: [
                    { path: "apply", element: <ApplyPage /> },
                    { path: "dynamicTable", element: <DynamicTablePage /> },
                    {
                        path: "financialTable",
                        element: <FinancialStatementsPage />,
                    },
                    { path: "staticTable", element: <StaticTablePage /> },
                    {
                        path: "index",
                        element: <MemberIndex />,
                    },
                    {
                        path: "associates",
                        element: <Associates />,
                    },
                    {
                        path: "interview",
                        element: <Interview />,
                    },
                    {
                        path: "associates/profile",
                        element: <Profile />,
                    },
                    {
                        path: "summary",
                        element: <Summary />,
                    },
                    {
                        path: "document",
                        element: <Document />,
                    },
                    {
                        path: "billing",
                        element: <Billing />,
                    },
                    {
                        path: "settings",
                        element: <SettingPage />,
                    },
                    {
                        path: "notifications",
                        element: <NotificationIndex />,
                    },
                    {
                        path: "notifications/detail",
                        element: <NotificationDetail />,
                    },
                    {
                        path: "upgrade",
                        element: <Upgrade />,
                    },
                    {
                        path: "upgrade/exam",
                        element: <UpgradeExam />,
                    },
                    {
                        path: "business",
                        element: <BusinessIndex />,
                    },
                    {
                        path: "community",
                        element: <CommunityIndex />,
                    },
                    {
                        path: "community/detail",
                        element: <CommunityDetail />,
                    },
                ],
            },
            {
                path: "member/exam",
                element: <ExamIndex />,
            },
            {
                path: "member2",
                element: <DashboardLayout />,
                children: [
                    {
                        path: "*",
                        errorElement: (
                            <RouteError className="tw-h-[100%] tw-w-[100%]" />
                        ),
                        loader: () => {
                            throw json(
                                {},
                                { status: 404, statusText: "Page Not Found" }
                            );
                        },
                    },
                ],
            },
        ],
    },
    {
        path: "*",
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        loader: () => {
            throw json({}, { status: 404, statusText: "Page Not Found" });
        },
    },
]);

export default router;
