import { useLocation, useNavigate } from "react-router-dom";
import { useLockFn, useMount } from "ahooks";
import { ComponentType, FC, useEffect } from "react";
import useWizardStore from "@code.8cent/store/wizard";
import { cnaRequest } from "@code.8cent/utils";

// 判断当前路径是否匹配 PUBLIC_ROUTES 中的路径（支持动态参数）
const isPublicRoute = (pathname: string, PublicRoutes: string[]) => {
    let isPublic = false;

    PublicRoutes.forEach((route) => {
        const isExcluded = route.startsWith("!");
        const normalizedRoute = isExcluded ? route.slice(1) : route;

        const routeSegments = normalizedRoute.split("/");
        const pathSegments = pathname.split("/");

        // 路径长度不一致时，跳过该规则
        if (routeSegments.length !== pathSegments.length) {
            return;
        }

        // 逐段检查路径是否匹配
        const match = routeSegments.every(
            (segment, i) => segment === "*" || segment === pathSegments[i]
        );

        if (match) {
            // 如果匹配到以 ! 开头的路径，则说明这是非公共路由
            isPublic = !isExcluded;
        }
    });

    return isPublic;
};

// Register V3 面试状态检查函数
const checkInterviewStatus = async () => {
    const { result: interviewStatusResult, error: interviewStatusError } =
        await cnaRequest("/api/v1/interview/interviewStatus", "POST");

    const interviewStatus = {
        hasInterview: false,
        step: "interview",
    };

    const INTERVIEW_STATUS = {
        SCHEDULED: 0,
        FAILED: -1,
        ABSENT: -2,
        REVIEWING: 2,
        PASS: 1,
    };

    const MAX_FAILURE_COUNT = 3;

    if (!interviewStatusError && interviewStatusResult) {
        sessionStorage.setItem(
            "interview-status",
            JSON.stringify(interviewStatusResult.data)
        );

        const {
            status,
            absent_count = 0,
            fail_count = 0,
        } = interviewStatusResult.data || {};

        if (interviewStatusResult.code === 402) {
            interviewStatus.hasInterview = true;
            interviewStatus.step = "interview";
        }

        if (
            absent_count >= MAX_FAILURE_COUNT ||
            fail_count >= MAX_FAILURE_COUNT
        ) {
            sessionStorage.setItem("step-v3", "interview-result");
            return {
                ...interviewStatus,
                shouldRedirect: true,
                redirectPath: "/account/register",
            };
        }

        if (status === INTERVIEW_STATUS.SCHEDULED) {
            interviewStatus.hasInterview = true;
            interviewStatus.step = "interview-info";
        } else if (
            [INTERVIEW_STATUS.FAILED, INTERVIEW_STATUS.ABSENT].includes(status)
        ) {
            interviewStatus.hasInterview = true;
            interviewStatus.step = "interview-result";
        }
    }

    return {
        ...interviewStatus,
        shouldRedirect: interviewStatus.hasInterview,
        redirectPath: "/account/register",
    };
};

type RouteGuardOptions = {
    publicRoutes?: string[];
};
// 定义 HOC 函数
const withRouteGuard = <P extends object>(
    Component: ComponentType<P>,
    options: RouteGuardOptions = {}
): FC<P> => {
    // 定义包装后的组件
    const WrappedComponent: FC<P> = (props) => {
        const navigate = useNavigate();

        const { pathname } = useLocation();
        const { setRegisterSetting, setState: setWizardState } =
            useWizardStore();

        const checkAuth = useLockFn(async () => {
            const token = await window.localForage.getItem("cna-token");

            // 获取注册设置
            const registerSetting = token
                ? ((await window.localForage.getItem(
                      "register-setting"
                  )) as string[])
                : null;

            if (pathname === "/") {
                if (token) {
                    if (registerSetting?.length) {
                        if (registerSetting.includes("qualification")) {
                            sessionStorage.setItem("step-v3", "qualification");
                            navigate("/account/register", {
                                replace: true,
                            });
                            return;
                        }
                        if (registerSetting.includes("business_case")) {
                            sessionStorage.setItem("step-v3", "business_case");
                            navigate("/account/register", {
                                replace: true,
                            });
                            return;
                        }

                        const interviewResult = await checkInterviewStatus();
                        if (interviewResult.shouldRedirect) {
                            sessionStorage.setItem(
                                "step-v3",
                                interviewResult.step
                            );
                            navigate(interviewResult.redirectPath, {
                                replace: true,
                            });
                            return;
                        }

                        setRegisterSetting(registerSetting);
                        setWizardState(0);
                        navigate("/account/wizard", { replace: true });
                        return;
                    }

                    const interviewResult = await checkInterviewStatus();
                    if (interviewResult.shouldRedirect) {
                        sessionStorage.setItem("step-v3", interviewResult.step);
                        navigate(interviewResult.redirectPath, {
                            replace: true,
                        });
                        return;
                    }

                    navigate("/index/dashboard", { replace: true });
                } else {
                    navigate("/account/login", { replace: true });
                }
                return;
            }

            if (!isPublicRoute(pathname, options?.publicRoutes ?? [])) {
                if (!token) {
                    navigate("/account/login", { replace: true });
                    return;
                }

                // 对非公共路由也检查 register-setting
                if (registerSetting?.length && pathname !== "/account/wizard") {
                    if (registerSetting.includes("qualification")) {
                        sessionStorage.setItem("step-v3", "qualification");
                        navigate("/account/register", {
                            replace: true,
                        });
                        return;
                    }
                    if (registerSetting.includes("business_case")) {
                        sessionStorage.setItem("step-v3", "business_case");
                        navigate("/account/register", {
                            replace: true,
                        });
                        return;
                    }

                    const interviewResult = await checkInterviewStatus();
                    if (interviewResult.shouldRedirect) {
                        sessionStorage.setItem("step-v3", interviewResult.step);
                        navigate(interviewResult.redirectPath, {
                            replace: true,
                        });
                        return;
                    }

                    setRegisterSetting(registerSetting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                    return;
                }
            }
        });

        useEffect(() => {
            checkAuth();
        }, [pathname]);

        useMount(() => {
            checkAuth();
        });

        return <Component {...props} />;
    };

    // 设置显示名称
    WrappedComponent.displayName = `withRouteGuard(${
        Component.displayName || Component.name || "Component"
    })`;

    return WrappedComponent;
};

export default withRouteGuard;
