import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import {
    Paper,
    TextInput,
    Table,
    Group,
    ScrollArea,
    Center,
    Stack,
    Text,
} from "@mantine/core";
import {
    FileText,
    MagnifyingGlass,
    Folder,
    Folder<PERSON>pen,
    CircleNotch,
    Eye,
} from "@phosphor-icons/react";
import {
    useRequest,
    useSetState,
    useMount,
    useUnmount,
    useMemoizedFn,
} from "ahooks";
import { cnaRequest } from "@code.8cent/utils";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import api from "@/apis";
import { useEffect, useState } from "react";

const Document = () => {
    const [inited, setInited] = useState(false);
    const { openFileView } = useFileViewer();

    const [documentState, setDocumentState] = useSetState({
        docHasMore: false,
        folderHasMore: false,
        docPage: 1,
        folderPage: 1,
        documents: [] as DocumentItem[],
        folders: [] as FolderItem[],
        folderOpenedId: -1,
    });

    const { run: getFolders } = useRequest(
        async (page_param: number = 1) => {
            try {
                const pageSize = 5;
                const folders_res = await cnaRequest<FolderResponse>(
                    "/api/v1/documentFolders/index",
                    "GET",
                    {
                        page_size: pageSize,
                        page: page_param,
                    }
                );

                const folders = (folders_res?.result?.data as any)?.item ?? [];

                if (folders.length < pageSize) {
                    setDocumentState({
                        folderHasMore: false,
                    });
                } else {
                    setDocumentState({
                        folderHasMore: true,
                    });
                }

                if (page_param === 1) {
                    setDocumentState({ folders });
                } else {
                    setDocumentState((prev) => ({
                        folders: [...prev.folders, ...folders],
                    }));
                }
            } catch (err) {
                console.error("Failed to fetch folders:", err);
            }
        },
        {
            ready: inited,
        }
    );

    const { run: getDocumentList, loading } = useRequest(
        async (page_param: number = 1, folder_id: number) => {
            try {
                const pageSize = 5;
                const docs_res = await cnaRequest<DocumentResponse>(
                    `/api/v1/documentFolders/files/${folder_id}`,
                    "GET",
                    {
                        page: page_param,
                        page_size: pageSize,
                    }
                );

                const docs = (docs_res?.result?.data as any)?.items ?? [];

                if (docs.length < pageSize) {
                    setDocumentState({ docHasMore: false });
                } else {
                    setDocumentState({ docHasMore: true });
                }

                if (page_param === 1) {
                    setDocumentState({ documents: docs });
                } else {
                    setDocumentState((prev) => ({
                        documents: [...prev.documents, ...docs],
                    }));
                }
            } catch (error) {
                console.error("Failed to fetch documents:", error);
            }
        },
        {
            manual: true,
            ready: inited,
        }
    );

    const getMoreDocuments = useMemoizedFn(async () => {
        if (documentState.docHasMore) {
            const new_page = documentState.docPage + 1;
            getDocumentList(new_page, documentState.folderOpenedId);
            setDocumentState({ docPage: new_page });
        }
    });

    const getMoreFolders = useMemoizedFn(async () => {
        if (documentState.folderHasMore) {
            const new_page = documentState.folderPage + 1;
            getFolders(new_page);
            setDocumentState({ folderPage: new_page });
        }
    });

    const handleFile = useMemoizedFn(
        async (fileId: number, type: "view" | "download") => {
            let token = await api.user.getDocumentToken(fileId);

            if (!token) {
                return;
            }

            if (type === "view") {
                openFileView(
                    `${window.api_base_url}/api/v1/document/preview/${token}`
                );
            } else if (type === "download") {
                let download_url = `${window.api_base_url}/api/v1/document/download/${token}`;
                let a = document.createElement("a");
                a.setAttribute("download", "");
                a.setAttribute("href", download_url);
                a.setAttribute("target", "_blank");
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        }
    );

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        setInited(false);
    });

    useEffect(() => {
        const id = documentState.folderOpenedId;
        setDocumentState({
            docPage: 1,
            documents: [],
        });

        if (id !== -1) {
            getDocumentList(1, id);
        }
    }, [documentState.folderOpenedId]);

    return (
        <>
            <PageHeader
                title="资料文档"
                subTitle="本栏目集中存储企业文档与参考资料，助力深入了解业务动态与市场趋势，获取必要信息。支持合伙人高效工作，确保重要内容可查。"
            />
            <Paper
                mt="xl"
                p="lg"
                radius="lg"
            >
                {/* todo 加上搜索的话，页面交互数据和接口都要调整，暂时隐藏了 */}
                {/* <TextInput
                    placeholder="搜索名称、类型"
                    rightSection={<MagnifyingGlass />}
                    className="tw-mb-6"
                /> */}
                <ScrollArea h={580}>
                    <div onScroll={getMoreFolders}>
                        <Table
                            withRowBorders={false}
                            highlightOnHover
                            className="custom-table"
                        >
                            <Table.Thead>
                                <Table.Tr>
                                    <Table.Th>文件夹</Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody>
                                {!documentState.folders.length && (
                                    <Table.Tr>
                                        <Table.Td colSpan={1}>
                                            <Group
                                                justify="center"
                                                className="tw-mt-20"
                                            >
                                                暂无数据
                                            </Group>
                                        </Table.Td>
                                    </Table.Tr>
                                )}

                                {documentState.folders.map(
                                    (folder, folder_index) => (
                                        <Table.Tr key={folder_index}>
                                            <Table.Td>
                                                <Group
                                                    className="tw-cursor-pointer"
                                                    onClick={() => {
                                                        if (
                                                            folder.id ===
                                                            documentState.folderOpenedId
                                                        ) {
                                                            setDocumentState({
                                                                folderOpenedId:
                                                                    -1,
                                                            });
                                                        } else {
                                                            setDocumentState({
                                                                folderOpenedId:
                                                                    folder.id,
                                                            });
                                                        }
                                                    }}
                                                >
                                                    {documentState.folderOpenedId ===
                                                    folder.id ? (
                                                        <FolderOpen size={24} />
                                                    ) : (
                                                        <Folder size={24} />
                                                    )}
                                                    <Text>
                                                        {folder.foldersNameZH}
                                                    </Text>
                                                </Group>

                                                {documentState.folderOpenedId ===
                                                    folder.id && (
                                                    <div
                                                        onScroll={
                                                            getMoreDocuments
                                                        }
                                                        className="tw-mt-3"
                                                    >
                                                        {loading ? (
                                                            <Center className="tw-min-h-[80px]">
                                                                <Stack align="center">
                                                                    <CircleNotch
                                                                        size={
                                                                            32
                                                                        }
                                                                        className="tw-text-basic-3 tw-animate-spin"
                                                                    />
                                                                </Stack>
                                                            </Center>
                                                        ) : (
                                                            documentState.documents.map(
                                                                (document) => (
                                                                    <Group
                                                                        key={
                                                                            document.documentID
                                                                        }
                                                                        className="tw-ml-10 tw-my-3 tw-cursor-pointer tw-text-nowrap"
                                                                        onClick={() => {
                                                                            handleFile(
                                                                                document.documentID,
                                                                                "view"
                                                                            );
                                                                        }}
                                                                        wrap="nowrap"
                                                                    >
                                                                        <FileText
                                                                            size={
                                                                                24
                                                                            }
                                                                        />
                                                                        <Text className="tw-flex-1">
                                                                            {
                                                                                document.documentTitle
                                                                            }
                                                                        </Text>
                                                                        <Text>
                                                                            {
                                                                                document.documentVersion
                                                                            }
                                                                        </Text>
                                                                        <CnaButton
                                                                            leftSection={
                                                                                <Eye />
                                                                            }
                                                                            variant="outline"
                                                                            size="compact-sm"
                                                                            onClick={(
                                                                                e
                                                                            ) => {
                                                                                e.stopPropagation();
                                                                                handleFile(
                                                                                    document.documentID,
                                                                                    "view"
                                                                                );
                                                                            }}
                                                                        >
                                                                            查看
                                                                        </CnaButton>
                                                                    </Group>
                                                                )
                                                            )
                                                        )}
                                                    </div>
                                                )}
                                            </Table.Td>
                                        </Table.Tr>
                                    )
                                )}
                            </Table.Tbody>
                        </Table>
                    </div>
                </ScrollArea>
            </Paper>
        </>
    );
};

export default Document;
