import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type DigitalHumanState = {
    progress: number;
    inited: boolean;
    uploaded: {
        pic: boolean;
        pic1: boolean;
        pic2: boolean;
        audio: boolean;
        video: boolean;
    };
};

const useDigitalHumanStore = create<DigitalHumanState>()(
    devtools(
        (set) => ({
            progress: 1,
            inited: false,
            uploaded: {
                pic: false,
                pic1: false,
                pic2: false,
                audio: false,
                video: false,
            },
        }),
        {
            name: "digital-human",
        }
    )
);

export const setDigitalHumanProgress = (progress: number) => {
    useDigitalHumanStore.setState({ progress });
};

export const setDigitalHumanInited = (inited: boolean) => {
    useDigitalHumanStore.setState({ inited });
};

export const setDigitalHumanUploaded = (
    key: keyof DigitalHumanState["uploaded"],
    value: boolean
) => {
    useDigitalHumanStore.setState((state) => ({
        uploaded: {
            ...state.uploaded,
            [key]: value,
        },
    }));
};

export default useDigitalHumanStore;
