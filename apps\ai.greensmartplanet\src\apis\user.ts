import cnaRequest from "@code.8cent/utils/cnaRequest";
import noty from "@code.8cent/react/noty";

const user = {
    updatePassword: async (oldPassword: string, newPassword: string) => {
        let updatePasswordRes = await cnaRequest(
            "/api/v1/password/update",
            "POST",
            {
                oldPassword,
                newPassword,
            }
        );

        if (updatePasswordRes.error) {
            noty.error(updatePasswordRes.error.message);
            return false;
        } else {
            return true;
        }
    },
    resetPassword: async (params: {
        code: string;
        email: string;
        password: string;
    }) => {
        const resetPasswordRes = await cnaRequest(
            "/api/v1/password/forgotPsw/reset",
            "POST",
            params
        );

        if (resetPasswordRes.error) {
            return false;
        } else {
            return true;
        }
    },
    getUserProfile: async () => {
        let profileRes = await cnaRequest<UserProfileResponse>(
            "/api/v1/user/profile",
            "POST"
        );

        const { result, error } = profileRes;

        if (result) {
            return result.data;
        } else {
            return null;
        }
    },
    updateUserProfile: async (
        profile: Partial<
            UserProfileResponse & {
                professional: UserProfileResponse["userProfessional"];
                skill: UserProfileResponse["userSkill"];
                experience: UserProfileResponse["userExperience"];
            }
        >
    ) => {
        let updateProfileRes = await cnaRequest(
            "/api/v1/user/updateProfile",
            "POST",
            profile
        );

        const { result, error } = updateProfileRes;

        if (result) {
            return true;
        } else {
            return false;
        }
    },
    /**
     * Updates the user's avatar.
     *
     * @param {File} avatar The avatar file selected by the user.
     * @returns Returns the updated avatar URL, or null if the update fails.
     */
    updateAvatar: async (avatar: File) => {
        const formData = new FormData();

        formData.append("avatar", avatar);

        let updateAvatarRes = await cnaRequest<string>(
            "/api/v1/user/updateAvatar",
            "POST",
            formData
        );

        const { result, error } = updateAvatarRes;

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },
    updateLanguageRegionSetting: async (langRegion: {
        settingCurrency?: string;
        settingTimeFormat?: string;
        settingTimezone?: string;
        settingDateFormat?: string;
        settingLanguage?: string;
        profileNationalityID?:string
    }) => {
        const { result, error } = await cnaRequest(
            "/api/v1/setting/languageArea/update",
            "POST",
            JSON.stringify(langRegion)
        );

        if (error) {
            return false;
        } else {
            return true;
        }
    },
};

export default user;
