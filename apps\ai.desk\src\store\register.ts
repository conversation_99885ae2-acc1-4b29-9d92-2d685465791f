import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";
import {
    RegisterStep,
    InterviewResultStatus,
} from "@/pages/account/RegisterV3/types";

export type RegisterInfoState = {
    email?: string;
    phone?: string;
    prefixID?: string;
    token?: string;
    refer?: string;
    nationalityID?: string;
    stepV3?: RegisterStep; // 注册流程 V3 步骤
    interviewStatus?: InterviewResultStatus; // 面试状态
};

type RegisterInfoAction = {
    setRegisterInfoValue: (
        key: keyof RegisterInfoState,
        value: number | string | boolean
    ) => void;
    setRegisterInfo: (user: RegisterInfoState) => void;
    setStepV3: (step: RegisterStep) => void;
    setInterviewStatus: (status: InterviewResultStatus) => void;
};

type RegisterInfoStateAndAction = RegisterInfoState & RegisterInfoAction;

const baseRegisterStore = create<RegisterInfoStateAndAction>()(
    devtools(
        (set) => ({
            email: "",
            phone: "",
            prefixID: "",
            token: "",
            refer: "",
            nationalityID: "",
            stepV3:
                (sessionStorage.getItem("step-v3") as RegisterStep) ||
                "basic-info",
            interviewStatus:
                (sessionStorage.getItem(
                    "interview-status"
                ) as InterviewResultStatus) || "pending",
            setRegisterInfoValue(key, value) {
                set((state) => ({
                    ...state,
                    [key]: value,
                }));
            },

            setRegisterInfo(user) {
                set((state) => ({
                    ...state,
                    ...user,
                }));
            },

            setStepV3(step) {
                set((state) => ({
                    ...state,
                    stepV3: step,
                }));

                // 同步设置 sessionStorage 中的 step-v3
                sessionStorage.setItem("step-v3", step);
            },

            setInterviewStatus(status) {
                set((state) => ({
                    ...state,
                    interviewStatus: status,
                }));

                // 同步设置 sessionStorage 中的 interview-status
                sessionStorage.setItem("interview-status", status);
            },
        }),
        {
            name: "register-store",
        }
    )
);

const useRegisterStore = createSelectors(baseRegisterStore);

export default useRegisterStore;
