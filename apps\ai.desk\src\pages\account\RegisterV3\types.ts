export type RegisterInput = {
    // 基本信息
    name: string;
    englishName?: string;
    gender: string;
    birthDate: string;
    phone: string;
    email: string;
    prefixID: string;

    // 推荐人信息
    referrer?: string;

    // 职业信息
    occupation?: string;
    position?: string;
    work_year?: string;

    // 地址信息
    addressCountry: string;
    addressPostcode: string;
    addressStreet: string;
    addressUnit: string;
    addressCityId: string;
    addressStateId: string;
    addressDistrictId: string;

    // 专业资质
    professionalCert: string;
    professionalLicense: string;

    // 业务案例
    businessCase: string;
    businessDescription: string;

    // 预约面试
    interviewDate: string;
    interviewTime: string;

    // 协议同意
    agreementChecked: boolean;

    // 学历信息
    edu?: string;
    eduSubject?: string;
    eduDatetime?: Array<string>;
};

export type ValidateStatus = {
    phone: boolean;
    email: boolean;
};

export type RegisterStep =
    | "basic-info"
    | "payment-qr"
    | "qualification"
    | "business_case"
    | "interview-status";

export type InterviewResultStatus =
    | "pending"
    | "reviewing"
    | "success"
    | "failed";
