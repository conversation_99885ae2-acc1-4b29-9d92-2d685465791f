import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import { Button, Grid, Group, Image, Paper, Stack, Text } from "@mantine/core";
import { ArrowRight, CaretLeft } from "@phosphor-icons/react";
import useBusinessStore, {
    setBusinessSection,

} from "@/store/business";
interface LicenseCardProps {
    title: string;
    description: string;
    gradientFrom: string;
    gradientTo: string;
    backgroundImage: string;
    onClick?: () => void;
}

const LicenseCard = ({
    title,
    description,
    gradientFrom,
    gradientTo,
    backgroundImage,
    onClick,

}: LicenseCardProps) => (
    <Paper
        p="xl"
        radius="lg"
        className="tw-cursor-pointer tw-h-[289px] tw-relative"
        style={{
            background: `linear-gradient(to right, ${gradientFrom}, ${gradientTo})`,
        }}
        onClick={onClick}
    >
        <Stack
            justify="space-between"
            className="tw-h-full"
        >
            <Stack gap={4}>
                <Text size="xl" c="white" fw={600}  >
                    {title}
                </Text>
                <Text size="sm" c="white"  >
                    {description}
                </Text>
            </Stack>
            <Group justify="flex-start">
                <CnaButton
                    variant="transparent"
                    size="sm"
                    radius="xl"
                    className="tw-bg-[#EAEEFF]"
                >
                    <ArrowRight
                        color="#19288F"
                        weight="bold"
                    />
                </CnaButton>
            </Group>
        </Stack>

        <Image
            src={backgroundImage}
            w={600}
            className="tw-absolute tw-bottom-0 tw-right-0"
        />
    </Paper>
);

const licenseData = [
    {
        title: "绿智地球超级AI平台",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的",
        gradientFrom: "#6cc6ca",
        gradientTo: "#99dfdc",
        backgroundImage: "/images/station/earth.svg",
    },
    {
        title: "绿智地球AIpark",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的",
        gradientFrom: "#6cc6ca",
        gradientTo: "#99dfdc",
        backgroundImage: "/images/station/earth.svg",
    },
    {
        title: "绿智地球AI生活馆",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#6cc6ca",
        gradientTo: "#99dfdc",
        backgroundImage: "/images/station/earth.svg",
    },
    {
        title: "绿智地球九大经济行为",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#6cc6ca",
        gradientTo: "#99dfdc",
        backgroundImage: "/images/station/earth.svg",
    },
];

const BasicLicensedList = () => {
    const handleCardClick = (index: number) => {
        // 处理卡片点击事件
        console.log(`点击了第 ${index + 1} 个许可证卡片`);
    };

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="绿智地球"
                subTitle="绿智地球介绍绿智地球介绍绿智地球介绍绿智地球介绍绿智地球介绍"
            />
            <div className="tw-w-20 tw-flex tw-items-center tw-gap-2 text-2xl tw-font-bold tw-cursor-pointer"
                onClick={() => setBusinessSection("basic-licensed-list")}>
                <CaretLeft size={24} /> 返回
            </div>

            <Grid gutter="xs">

                {
                    licenseData.map((license, index) => (
                        <Grid.Col span={6} key={index}>
                            <LicenseCard
                                {...license}
                                onClick={() => handleCardClick(index)}
                            />
                        </Grid.Col>
                    ))
                }
            </Grid>
        </Stack >
    );
};

export default BasicLicensedList;
