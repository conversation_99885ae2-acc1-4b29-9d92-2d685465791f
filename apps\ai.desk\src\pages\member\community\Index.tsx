import PageHeader from "@/components/member/PageHeader";
import { <PERSON><PERSON><PERSON><PERSON>on, ProfileAvatar } from "@code.8cent/react/components";
import {
    Stack,
    Group,
    TextInput,
    Select,
    ScrollArea,
    Text,
    Pagination,
    SimpleGrid,
    em,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { CaretDown, MagnifyingGlass } from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { useRequest } from "ahooks";
import api from "@/apis";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import CountrySelect from "@code.8cent/react/components/CountrySelect";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import { cnaRequest } from "@code.8cent/utils";
import { useNavigate } from "react-router-dom";

const searchMemberSchema = z.object({
    keyword: z.string().optional(),
    nationality_id: z.string().optional(),
    profession_id: z.string().optional(),
});

type SearchParamInput = Omit<TCommunitySearchParams, "page_size" | "page">;

const initialSearchParams: SearchParamInput = {
    nationality_id: "44",
    keyword: "",
    profession_id: "",
};

const CommunityItem = ({ associate }: { associate: CommunityUserProfile }) => {
    const navigate = useNavigate();

    return (
        <Stack
            className="tw-rounded-lg tw-border tw-border-[#353066]"
            gap={0}
            p="md"
        >
            <Group gap="lg">
                <ProfileAvatar
                    src={
                        associate.profileAvatar !==
                        "/storage/images/avatar/image-user.svg"
                            ? `${window.api_base_url}${associate.profileAvatar}`
                            : "/images/station/default-avatar.svg"
                    }
                    className="tw-w-[80px] tw-h-[80px] md:tw-w-[100px] md:tw-h-[100px] md:tw-m-4"
                    style={{
                        userSelect: "none",
                        WebkitUserSelect: "none",
                        MozUserSelect: "none",
                        msUserSelect: "none",
                        pointerEvents: "none",
                    }}
                />
                <Stack
                    gap={0}
                    className="tw-flex-1"
                >
                    <Group
                        justify="flex-end"
                        className="tw-w-full"
                    >
                        <CnaButton
                            variant="transparent"
                            c="#19288F"
                            onClick={() => {
                                navigate("/member/community/detail", {
                                    replace: true,
                                    state: { profileId: associate.profileID },
                                });
                            }}
                        >
                            查看详情
                        </CnaButton>
                    </Group>
                    <Group gap={0}>
                        <Text c="dimmed">姓名：</Text>
                        <Text>{associate.profileName || "未填写"}</Text>
                    </Group>
                    <Group gap={0}>
                        <Text c="dimmed">编号：</Text>
                        <Text>{associate.profilePartnerCode || "未填写"}</Text>
                    </Group>
                    <Group gap={0}>
                        <Text c="dimmed">地区：</Text>
                        <Text>
                            {associate.nationality?.countryZH || "未填写"}
                        </Text>
                    </Group>
                    <Group gap={0}>
                        <Text c="dimmed">职业：</Text>
                        <Text>{associate.position || "未填写"}</Text>
                    </Group>
                    <Group gap={0}>
                        <Text c="dimmed">所属行业：</Text>
                        <Text>{associate.profession?.nameZH || "未填写"}</Text>
                    </Group>
                </Stack>
            </Group>
        </Stack>
    );
};

const CommunityIndex = () => {
    const lang = useSettingStore.use.lang();
    const filteredCountryDatas = useDataStore.use.filteredCountryDatas();

    // 移动端检测
    const isMobile = useMediaQuery(`(max-width: ${em(767)})`);

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalRecords, setTotalRecords] = useState(0);
    const [associates, setAssociates] = useState<CommunityUserProfile[]>([]);
    const [professions, setProfessions] = useState<any[]>([]);

    const {
        handleSubmit,
        register,
        formState: { errors },
        getValues,
        setValue,
        reset,
    } = useForm<SearchParamInput>({
        resolver: zodResolver(searchMemberSchema),
        defaultValues: initialSearchParams,
    });

    const { run: searchMember, loading } = useRequest(
        async (params: TCommunitySearchParams) => {
            const result = await api.community.search(params);

            if (result) {
                setAssociates(result.items || []);
                setTotalPages(result.paginate.totalPage || 1);
                setTotalRecords(result.paginate.totalRecord || 0);
            }
        },
        { manual: true }
    );

    const handleSearch = handleSubmit(
        (data) => {
            setCurrentPage(1);
            searchMember({
                ...data,
                page: 1,
                page_size: 6,
            });
        },
        (error) => {
            console.log(error);
        }
    );

    const handleReset = () => {
        setCurrentPage(1);
        reset();
        searchMember({
            ...initialSearchParams,
            page: 1,
            page_size: 6,
        });
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        searchMember({
            ...getValues(),
            page,
            page_size: 6,
        });
    };

    const { run: fetchProfessions } = useRequest(
        async () => {
            const { error, result } = await cnaRequest(
                "/api/v1/config/professionList",
                "GET",
                { keyword: "" }
            );
            if (!error && result) {
                setProfessions(result.data);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        fetchProfessions();
        searchMember({
            ...initialSearchParams,
            page: 1,
            page_size: 6,
        });
    }, []);

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="合伙人社区"
                subTitle="本栏目整合全球资源共享 AI 平台，促进各地合伙人交流合作，助力业务落地、开拓海外市场、撮合彼此间企业客户的生意对接。"
            />
            <Stack
                p="lg"
                className="tw-bg-white tw-rounded-xl tw-h-[calc(100vh-100px)]"
                mt="lg"
                justify="space-between"
            >
                <Group
                    justify="space-between"
                    wrap="wrap"
                    gap="md"
                >
                    <TextInput
                        className="tw-w-[200px] tw-flex-1 tw-min-w-[200px]"
                        placeholder="搜索姓名"
                        rightSection={<MagnifyingGlass />}
                        {...register("keyword")}
                        error={errors.keyword ? true : false}
                    />
                    <CountrySelect<CountryDataItem>
                        className="tw-w-[200px] tw-flex-1 tw-min-w-[200px]"
                        data={filteredCountryDatas()}
                        flagKey="countryISOCode2"
                        labelKey={`country${lang}` as keyof CountryDataItem}
                        valueKey="countryID"
                        value={getValues("nationality_id") as string}
                        allowDeselect={false}
                        searchable
                        placeholder="按地区"
                        onChange={(value) => {
                            setValue("nationality_id", value, {
                                shouldValidate: true,
                            });
                        }}
                        rightSection={<CaretDown />}
                    />
                    <Select
                        className="tw-w-[200px] tw-flex-1 tw-min-w-[200px]"
                        placeholder="专业属性"
                        rightSection={<CaretDown />}
                        data={professions.map((profession) => ({
                            value: String(profession.id),
                            label: profession.nameZH,
                        }))}
                        value={getValues("profession_id") || null}
                        searchable
                        onChange={(value) => {
                            setValue("profession_id", value || "", {
                                shouldValidate: true,
                            });
                        }}
                    />
                    <Group
                        justify="flex-end"
                        className="tw-w-full md:tw-w-auto"
                    >
                        <CnaButton
                            variant="outline"
                            radius="xl"
                            onClick={handleReset}
                            loading={loading}
                        >
                            重置
                        </CnaButton>
                        <CnaButton
                            radius="xl"
                            onClick={handleSearch}
                            loading={loading}
                        >
                            搜索
                        </CnaButton>
                    </Group>
                </Group>

                <ScrollArea h={{ base: 500, md: 560 }}>
                    <SimpleGrid cols={{ base: 1, md: 2 }}>
                        {associates.map((associate, index) => (
                            <CommunityItem
                                key={associate.profileID || index}
                                associate={associate}
                            />
                        ))}
                    </SimpleGrid>
                    {!associates.length && !loading && (
                        <Text
                            ta="center"
                            className="tw-mt-32"
                        >
                            暂无数据
                        </Text>
                    )}
                </ScrollArea>
                <Group justify="space-between">
                    <Text>共{totalRecords}条记录</Text>
                    <Pagination
                        total={totalPages}
                        value={currentPage}
                        onChange={handlePageChange}
                        siblings={isMobile ? 0 : 1}
                    />
                </Group>
            </Stack>
        </Stack>
    );
};

export default CommunityIndex;
