import PageHeader from "@/components/member/PageHeader";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from "@mantine/core";
import IntroComponent from "@/components/member/business/Intro";
import IntroModal from "@/components/member/business/modals/Intro";
import useModalStore from "@/store/modal";
import useBusinessStore, { setBusinessSection } from "@/store/business";
import business from "@/apis/business";
import { useRequest } from "ahooks";
import { useEffect } from "react";
import noty from "@code.8cent/react/noty";
import api from "@/apis";

const Intro = () => {
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();
    const businessSection = useBusinessStore.use.businessSection();

    // 获取业务信息
    const {
        run: getBusinessInfo,
        data: businessInfo,
        loading,
    } = useRequest(
        async () => {
            try { 
                const res = await business.getBusinessList(
                    "partner_franchise_handbook_license"
                );
                console.log('获取业务列表getBusinessInfo', res);
                const introRes = await business.getCourseInfo(res?.[0]?.id);

                return {
                    ...res?.[0],
                    ...introRes,
                };
            } catch (error) {
                noty.error(error.message);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        getBusinessInfo();
    }, []);

    const handleStartLearn = () => {
        setBusinessSection("unlicensed-courses");
    };

    const handleStartExam = async () => {
        // 添加考试进程
        await api.exam.addProcess("partner_franchise_handbook_license");
        setBusinessSection("unlicensed-exam");
    };

    return (
        <Stack>
            <PageHeader title="业务管理" subTitle="本栏目用于获取相关业务牌照认证，协助跟进项目进度，助力合伙人深化对业务的认知与理解" />

            <LoadingOverlay visible={loading} />

            {!loading && (
                <IntroComponent
                    title={businessInfo?.project_name}
                    // price={`${businessInfo?.pay_currency}${businessInfo?.price}`}
                    intro={businessInfo?.intro}
                    general={businessInfo?.outline}
                    onStartLearn={handleStartLearn}
                    onStartExam={async () => {
                        const canExam = await business.checkCanExam("56");
                        if (!canExam) {
                            openConfirm({
                                title: "无法考试",
                                message: "您尚有学习未完成，无法开始考试",
                                onConfirmText: "前往学习",
                                onConfirm: () => {
                                    setBusinessSection("unlicensed-courses");
                                }
                            });
                            return false;
                        }
                        // 显示考试信息弹窗
                        openModal("businessIntro");
                    }}
                />
            )}

            <IntroModal
                title={businessInfo?.project_name}
                onStartExam={handleStartExam}
            />
        </Stack>
    );
};

export default Intro;
