import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type OfficeState = {
    applicationStatus: number;

};

const useOfficeStore = create<OfficeState>()(
    devtools(
        (set) => ({
            applicationStatus: 0,
        }),
        {
            name: "office-store",
        }
    )
);

export const setOfficeApplicationStatus = (status: number) => {
    useOfficeStore.setState({ applicationStatus: status });
};

export default useOfficeStore;
