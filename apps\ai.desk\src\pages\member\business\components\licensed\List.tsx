import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import { Group, Image, Paper, Stack, Text } from "@mantine/core";
import { ArrowRight } from "@phosphor-icons/react";
import useBusinessStore, { setBusinessSection } from "@/store/business";
interface LicenseCardProps {
    icon: string;
    title: string;
    description: string;
    gradientFrom: string;
    gradientTo: string;
    backgroundImage: string;
    onClick?: () => void;
}

const LicenseCard = ({
    icon,
    title,
    description,
    gradientFrom,
    gradientTo,
    backgroundImage,
    onClick,
}: LicenseCardProps) => (
    <Paper
        p="xl"
        radius="lg"
        className=" tw-h-[289px] tw-relative"
        style={{
            background: `linear-gradient(to right, ${gradientFrom}, ${gradientTo})`,
        }}
        onClick={onClick}
    >
        <Stack
            justify="space-between"
            className="tw-h-full"
        >
            <Image
                src={icon}
                className="tw-w-8 tw-h-8"
                draggable={false}
            />
            <Stack gap={0}>
                <Text
                    size="xl"
                    c="white"
                    fw={600}
                >
                    {title}
                </Text>
                <Text
                    size="xs"
                    c="white"
                >
                    {description}
                </Text>
            </Stack>
            <Group justify="flex-start">
                <CnaButton
                    variant="transparent"
                    size="sm"
                    radius="xl"
                    className="tw-bg-[#EAEEFF]"
                >
                    <ArrowRight
                        color="#19288F"
                        weight="bold"
                    />
                </CnaButton>
            </Group>
        </Stack>

        <Image
            src={backgroundImage}
            w={600}
            className="tw-absolute tw-bottom-0 tw-right-0"
            draggable={false}
        />
    </Paper>
);

const licenseData = [
    {
        icon: "/images/station/basic-v.svg",
        title: "基础业务认证牌照",
        description:
            "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#353066",
        gradientTo: "#425097",
        backgroundImage: "/images/station/basic-v-section.svg",
        onClick: () => {
            setBusinessSection("basic-licensed-list");
        },
    },
    {
        icon: "/images/station/upgrade-v.svg",
        title: "专案认证牌照",
        description:
            "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#2C78D7",
        gradientTo: "#21B4F9",
        backgroundImage: "/images/station/upgrade-v-section.svg",
        onClick: () => {
            setBusinessSection("professional-licensed-pay");
        },
    },
];

const LicensedList = () => {
    const handleCardClick = (index: number) => {
        // 处理卡片点击事件
        console.log(`点击了第 ${index + 1} 个许可证卡片`);
    };

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="业务管理"
                subTitle="本栏目用于获取相关业务牌照认证，协助进项目进度"
            />
            <Stack
                gap="xl"
                mt={{ base: "md", md: "xl", sm: "sm" }}
                className="tw-h-[85vh] tw-overflow-auto"
            >
                {licenseData.map((license, index) => (
                    <LicenseCard
                        key={index}
                        {...license}
                        onClick={license.onClick}
                    />
                ))}
            </Stack>
        </Stack>
    );
};

export default LicensedList;
