import { useEffect } from "react";
import { useMount } from "ahooks";
import useUserStore from "@/store/user";
import useWizardStore from "@code.8cent/store/wizard";

/**
 * 用户数据初始化Hook
 * 在应用启动时恢复用户状态，避免刷新页面后数据丢失
 */
export const useUserInitialization = () => {
    const userStore = useUserStore();
    const wizardStore = useWizardStore();

    // 初始化用户数据
    const initializeUserData = async () => {
        try {
            // 从本地存储恢复用户状态
            await userStore.syncFromStorage();

            // 如果用户已登录，恢复wizard状态
            if (userStore.isLoggedIn && userStore.profile) {
                const { register_setting } = userStore.profile;
                if (register_setting?.length > 0) {
                    wizardStore.setRegisterSetting(register_setting);
                    wizardStore.setState(0);
                }
            }
        } catch (error) {
            console.error("初始化用户数据失败:", error);
        }
    };

    // 组件挂载时初始化
    useMount(() => {
        initializeUserData();
    });

    // 监听用户登录状态变化，同步数据到本地存储
    useEffect(() => {
        if (userStore.isLoggedIn) {
            userStore.syncToStorage();
        }
    }, [userStore.isLoggedIn, userStore.profile]);

    return {
        isInitialized: userStore.isLoggedIn,
        isLoading: userStore.isLoading,
        user: userStore.profile,
    };
};

export default useUserInitialization;
