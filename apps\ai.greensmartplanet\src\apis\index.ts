import cnaRequest from "@code.8cent/utils/cnaRequest";
import config from "./config";
import user from "./user";
import register from "./register";
import setting from "./setting";

const api = {
    test: async () => {
        //https://laravel.api.cna.greensmartplanet.cn/api/v1/test
        let res = await cnaRequest<TestResponse>("/api/v1/test", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    version: async () => {
        let res = await cnaRequest<string>("/version", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    user,
    config,
    register,
    setting,
};

export default api;
