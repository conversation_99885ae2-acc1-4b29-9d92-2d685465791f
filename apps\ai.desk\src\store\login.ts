import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type LoginState = {
    officeCode: null | string;
};

const baseLoginStore = create<LoginState>()(
    devtools(
        (set) => ({
            officeCode: null,
        }),
        {
            name: "login-store",
        }
    )
);

export const updateLoginOfficeCode = (code: string | null) => {
    baseLoginStore.setState({ officeCode: code });
};

const useLoginStore = createSelectors(baseLoginStore);

export default useLoginStore;
