"use client";
// import { useRouter } from "next/navigation"
import { DynamicForm } from "../../components/apply/dynamic/dynamic-form";
import { useEffect, useState, useMemo, useRef } from "react";
import type { TableConfig } from "../../components/apply/dynamic/form-types";
import { ArrowLeft } from "@phosphor-icons/react";
import { useNavigate, useLocation } from "react-router-dom";
import api from "@/apis";
import TableModel from "@/components/apply/model/tableModel";
import Header from "@/components/apply/static/components/header";
import { useRequest } from "ahooks";
import { useTableStore } from "@/components/apply/store/tableStore";

export default function DynamicTablePage() {
    const {
        openedTable,
        tableFile,
        closeTable,
        setTableFile,
        openTable,
        tableTitle,
        downloadTitle,
    } = useTableStore();
    const location = useLocation();
    const navigate = useNavigate();
    const headerRef = useRef<{ handleTranslate: () => Promise<void> }>(null);

    const query = new URLSearchParams(location.search);
    const [getValuesFn, setGetValuesFn] = useState<(() => any) | null>(null);
    const [resetFn, setResetFn] = useState<((data: any) => void) | null>(null);
    const raw = query.get("data");
    let decodedData = null;
    if (raw) {
        try {
            decodedData = JSON.parse(decodeURIComponent(atob(raw)));
            console.log("decodedData", decodedData);
        } catch (e) {
            console.error("解码失败", e);
        }
    }
    //   const router = useRouter()
    const [tableConfig, setTableConfig] = useState<TableConfig | null>(null);
    const [loading, setLoading] = useState(true);

    const { run: getTableParams } = useRequest(
        async (key) => {
            const res = await api.apply.getInvestigationExtra_table(key);

            setTableConfig(res);
            setLoading(false);
            // append(initialFormValues);
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (decodedData?.key) {
            getTableParams(decodedData.key);
        }
    }, [decodedData?.key]);

    const handleGoBack = () => {
        // router.back()
        navigate(-1);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-screen">
                加载中...
            </div>
        );
    }

    if (!tableConfig) {
        return (
            <div className="flex justify-center items-center h-screen">
                加载表单配置失败
            </div>
        );
    }
    const translatableFields = tableConfig?.table_header
        ?.filter(
            (header) =>
                (header.type === "text" || header.type === "longtext") &&
                header._en
        ) // 只取 text 类型且有英文字段的
        .map((header) => ({
            zh: header.key,
            en: header._en.key,
        }));

    console.log("translatableFields---", decodedData.id);

    return (
        <div className="tw-bg-white tw-rounded-[10px] tw-max-h-full  tw-flex tw-flex-col">
            {getValuesFn && resetFn && (
                <Header
                    title={tableConfig.table_title}
                    translatableFields={
                        decodedData?.id == 48 ? null : translatableFields
                    }
                    getValues={getValuesFn}
                    reset={resetFn}
                    ref={headerRef}
                />
            )}
            <div className="tw-flex-1 tw-overflow-y-auto">
                <div className="tw-py-5 ">
                    <div className="tw-mb-5 tw-text-[14px] tw-px-4">
                        {tableConfig.table_comment}
                    </div>

                    {tableConfig.table_comment_en && (
                        <div className="tw-mb-5 tw-text-[14px] tw-px-4">
                            {tableConfig.table_comment_en}
                        </div>
                    )}
                    <DynamicForm
                        tableConfig={tableConfig}
                        decodedData={decodedData}
                        onFormReady={({ getValues, reset }) => {
                            setGetValuesFn(() => getValues);
                            setResetFn(() => reset);
                        }}
                        headerRef={headerRef}
                    />
                </div>
            </div>

            <TableModel
                openedTable={openedTable}
                closeTable={() => {
                    closeTable();
                }}
                tableFile={tableFile}
                title={{
                    tableTitle,
                    downloadTitle,
                }}
            />
        </div>
    );
}
