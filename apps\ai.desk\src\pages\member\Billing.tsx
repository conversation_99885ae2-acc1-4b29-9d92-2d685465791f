import PageHeader from "@/components/member/PageHeader";
import ExportBillings from "@/components/modals/member/ExportBillings";
import { CnaButton } from "@code.8cent/react/components";
import {
    Grid,
    Group,
    Image,
    LoadingOverlay,
    Pagination,
    Paper,
    ScrollArea,
    Stack,
    Table,
    Text,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { CalendarBlank } from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import api from "@/apis";
import dayjs from "dayjs";

interface SectionProps {
    title: string;
    amount: number;
    percentage?: number;
    imageSrc: string;
}

// 修改Section组件，调整文字位置使其在框内显示
const Section = ({ title, amount, percentage, imageSrc }: SectionProps) => {
    return (
        <div style={{ position: "relative" }}>
            {/* 修改这里的定位和样式，确保文字在框内 */}
            <div className="tw-relative">
                <Image
                    src={imageSrc}
                    className="tw-max-w-[350px] tw-w-full"
                    draggable={false}
                />
                <div className="tw-absolute tw-top-0 tw-left-0 tw-w-full tw-h-full tw-flex tw-flex-col tw-justify-center tw-pl-8">
                    <Text
                        fw={500}
                        className="tw-text-white tw-text-md md:tw-text-lg"
                    >
                        {title}
                    </Text>
                    <Text
                        fw={600}
                        className="tw-text-white tw-text-2xl md:tw-text-3xl"
                    >
                       ¥{amount}
                    </Text>
                    {percentage !== undefined && (
                        <Text className="tw-text-white tw-text-xs md:tw-text-sm">
                            同比昨日增长{percentage}%
                        </Text>
                    )}
                </div>
            </div>
        </div>
    );
};

const TYPE_MAP = {
    1: {
        text: "支出",
        color: "#FF9500",
    },
    2: {
        text: "业务收入",
        color: "#0648DE",
    },
    3: {
        text: "豁免",
        color: "#222222",
    },
    4: {
        text: "津贴收入",
        color: "#0648DE",
    },
    5: {
        text: "抵扣",
        color: "#0648DE",
    },
};

const STATUS_MAP = {
    1: {
        text: "已支付",
        color: "#222222",
    },
    2: {
        text: "已豁免",
        color: "#222222",
    },
    3: {
        text: "未到账",
        color: "#DA0101",
    },
    4: {
        text: "已到账",
        color: "#222222",
    },
};

const Billing = () => {
    const [exportBillingsOpened, setExportBillingsOpened] = useState(false);

    const [billList, setBillList] = useState<BillListItem[]>([]);
    const [billListTotal, setBillListTotal] = useState<number>(0);
    const [billListTotalPage, setBillListTotalPage] = useState<number>(0);
    const [statics, setStatics] = useState<BillTotalResponse>({
        income: 0,
        expense: 0,
        total: 0,
        yesterday: {
            incomeIncreasePercentage: 0,
            expenseIncreasePercentage: 0,
        },
        deductible: 0,
    });

    const [billListPage, setBillListPage] = useState<number>(1);
    const [billType, setBillType] = useState<number | undefined>(undefined);

    // 添加loading状态
    const [billListLoading, setBillListLoading] = useState<boolean>(false);
    const [staticsLoading, setStaticsLoading] = useState<boolean>(false);

    // 在组件顶部添加一个新的状态
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
        null,
        null,
    ]);

    const fetchBillList = () => {
        if ((dateRange[0] && !dateRange[1]) || (!dateRange[0] && dateRange[1])) {
            return;
        }

        setBillListLoading(true);
        api.bill
            .list({
                page: billListPage,
                page_size: 10, // 默认10条，暂不支持修改
                type: billType,
                start_time: dateRange[0]
                    ? dayjs(dateRange[0])
                          .startOf("day")
                          .format("YYYY-MM-DD HH:mm:ss")
                    : "",
                end_time: dateRange[1]
                    ? dayjs(dateRange[1])
                          .endOf("day")
                          .format("YYYY-MM-DD HH:mm:ss")
                    : "",
            })
            .then((res) => {
                setBillList(res.data);
                setBillListTotal(res.total);
                setBillListTotalPage(res.last_page);
            })
            .finally(() => {
                setBillListLoading(false);
            });
    };

    useEffect(() => {
        // 获取出入款列表
        fetchBillList();

        // 获取出入款统计
        setStaticsLoading(true);
        api.bill
            .total()
            .then((res) => {
                setStatics(res);
            })
            .finally(() => {
                setStaticsLoading(false);
            });
    }, []);

    useEffect(() => {
        fetchBillList();
    }, [billListPage, billType, dateRange]);

    const handleBillTypeChange = (type: number | undefined) => {
        setBillType(type);
        setBillListPage(1);
        // 清空日期选择器的值
        setDateRange([null, null]);
    };

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="出入款记录"
                subTitle="本栏目用于查看付款记录和接收专业服务费，提供支付明细和费用更新，确保合伙人及时了解财务变动。"
            />

            <div className="tw-h-full tw-overflow-y-auto md:tw-overflow-y-hidden tw-overflow-x-hidden">
                <Grid mt={30}>
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                        <div style={{ position: "relative" }}>
                            <LoadingOverlay visible={staticsLoading} />
                            <Section
                                title="流水总额"
                                amount={statics.total}
                                imageSrc="/images/station/bill-section1.svg"
                            />
                        </div>
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                        <div style={{ position: "relative" }}>
                            <LoadingOverlay visible={staticsLoading} />
                            <Section
                                title="今月收入总额"
                                amount={statics.income}
                                percentage={
                                    statics.yesterday.incomeIncreasePercentage
                                }
                                imageSrc="/images/station/bill-section2.svg"
                            />
                        </div>
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                        <div style={{ position: "relative" }}>
                            <LoadingOverlay visible={staticsLoading} />
                            <Section
                                title="今月支出总额"
                                amount={statics.expense}
                                percentage={
                                    statics.yesterday.expenseIncreasePercentage
                                }
                                imageSrc="/images/station/bill-section3.svg"
                            />
                        </div>
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                        <div style={{ position: "relative" }}>
                            <LoadingOverlay visible={staticsLoading} />
                            <Section
                                title="待抵扣金额"
                                amount={statics.deductible}
                                imageSrc="/images/station/bill-section4.svg"
                            />
                        </div>
                    </Grid.Col>
                </Grid>
                <Paper
                    p={{ base: "xs", md: "lg" }}
                    radius="lg"
                    style={{ position: "relative" }}
                >
                    <LoadingOverlay visible={billListLoading} />
                    <Group justify="space-between">
                        <Group>
                            <CnaButton
                                variant={billType === 1 ? "filled" : "outline"}
                                size="sm"
                                radius="lg"
                                onClick={() => {
                                    handleBillTypeChange(
                                        billType === 1 ? undefined : 1
                                    );
                                }}
                            >
                                支出记录
                            </CnaButton>
                            <CnaButton
                                variant={billType === 2 ? "filled" : "outline"}
                                size="sm"
                                radius="lg"
                                onClick={() => {
                                    handleBillTypeChange(
                                        billType === 2 ? undefined : 2
                                    );
                                }}
                            >
                                收入记录
                            </CnaButton>
                            <CnaButton
                                variant={billType === 3 ? "filled" : "outline"}
                                size="sm"
                                radius="lg"
                                onClick={() => {
                                    handleBillTypeChange(
                                        billType === 3 ? undefined : 3
                                    );
                                }}
                            >
                                豁免记录
                            </CnaButton>
                            <DatePickerInput
                                placeholder="选择日期范围"
                                className="tw-w-60"
                                rightSection={<CalendarBlank size={16} />}
                                valueFormat="YYYY-MM-DD"
                                type="range"
                                value={dateRange}
                                onChange={(date) => {
                                    setDateRange(date);
                                }}
                            />
                        </Group>
                        {/* 功能未实现，按钮先注释 */}
                        {/* <CnaButton
                        size="sm"
                        radius="lg"
                        onClick={() => setExportBillingsOpened(true)}
                    >
                        导出表格
                    </CnaButton> */}
                    </Group>

                    <ScrollArea
                        h={350}
                        mt="md"
                    >
                        <Table
                            withRowBorders={false}
                            highlightOnHover
                            className="custom-table tw-whitespace-nowrap"
                        >
                            <Table.Thead>
                                <Table.Tr>
                                    <Table.Th>交易时间</Table.Th>
                                    <Table.Th>收支类型</Table.Th>
                                    <Table.Th>交易金额</Table.Th>
                                    <Table.Th>交易状态</Table.Th>
                                    <Table.Th>款项类型</Table.Th>
                                    <Table.Th>业务编号</Table.Th>
                                    <Table.Th>业务类型</Table.Th>
                                    <Table.Th>备注</Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody>
                                {billList.map((item) => (
                                    <Table.Tr key={item.id}>
                                        <Table.Td>{item.createtime}</Table.Td>
                                        <Table.Td>
                                            <Text c={TYPE_MAP[item.type].color}>
                                                {TYPE_MAP[item.type].text}
                                            </Text>
                                        </Table.Td>
                                        <Table.Td>{item.amount}</Table.Td>
                                        <Table.Td>
                                            <Text
                                                c={
                                                    STATUS_MAP[item.status]
                                                        .color
                                                }
                                            >
                                                {STATUS_MAP[item.status].text}
                                            </Text>
                                        </Table.Td>
                                        <Table.Td>
                                            {item.divideTypeName}
                                        </Table.Td>
                                        <Table.Td>
                                            {item.paymentNumber}
                                        </Table.Td>
                                        <Table.Td>{item.detail_name}</Table.Td>
                                        <Table.Td>
                                            {item.remark ? (
                                                <span className="tw-text-[#DA0101]">
                                                    {item.remark}
                                                </span>
                                            ) : (
                                                "-"
                                            )}
                                        </Table.Td>
                                    </Table.Tr>
                                ))}
                                {billList.length === 0 && (
                                    <Table.Tr>
                                        <Table.Td
                                            colSpan={8}
                                            className="tw-text-center"
                                        >
                                            暂无数据
                                        </Table.Td>
                                    </Table.Tr>
                                )}
                            </Table.Tbody>
                        </Table>
                    </ScrollArea>
                    <Group justify="space-between">
                        <Text>共{billListTotal}条</Text>
                        <Pagination
                            mt="md"
                            total={billListTotalPage}
                            value={billListPage}
                            onChange={(page) => setBillListPage(page)}
                        />
                    </Group>
                </Paper>
            </div>

            <ExportBillings
                opened={exportBillingsOpened}
                setOpened={setExportBillingsOpened}
            />
        </Stack>
    );
};

export default Billing;
