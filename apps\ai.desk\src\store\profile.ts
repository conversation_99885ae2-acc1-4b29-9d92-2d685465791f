import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

type ProfileState = Partial<UserProfileResponse> & {
    inited: boolean;
};

type ProfileAction = {
    setProfileValue: (
        key: keyof UserProfileResponse,
        value: UserProfileResponse[keyof UserProfileResponse]
    ) => void;
    setProfile: (user: UserProfileResponse) => void;
    setInited: (val: boolean) => void;
};

type ProfileStateAndAction = ProfileState & ProfileAction;

const baseProfileStore = create<ProfileStateAndAction>()(
    devtools(
        (set) => ({
            inited: false,
            profileName: "",
            profileLastName: "",
            profileGivenName: "",
            profileAvatar: "",
            profileEmail: "",
            profileAddressUnit: "",
            profileAddressStreet: "",
            profileAddressCity: "",
            profileAddressState: "",
            profileAddressPostcode: "",
            profileAddressCountry: "",
            profileContact: "",
            userExperience: [],
            userProfessional: [],
            userSkill: [],
            // 省市区ID
            profileAddressDistrictId: 0,
            profileAddressCityId: 0,
            profileAddressStateId: 0,
            job: "",
            setInited(inited) {
                set({
                    inited,
                });
            },
            setProfileValue(key, value) {
                set((state) => ({
                    ...state,
                    [key]: value,
                }));
            },

            setProfile(user) {
                set((state) => ({
                    ...state,
                    ...user,
                }));
            },
        }),
        {
            name: "user-store",
        }
    )
);

const useProfileStore = createSelectors(baseProfileStore);

export default useProfileStore;
