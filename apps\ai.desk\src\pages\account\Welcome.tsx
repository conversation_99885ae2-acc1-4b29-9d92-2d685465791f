import { Box, Image, Stack, Text } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import useRegisterStore from "@/store/register";
import { cnaRequest } from "@code.8cent/utils";

// 定义职位文本常量
const POSITION_TEXTS = {
    "-1": {
        title: "营运总裁",
        department: "C&A 中国总公司",
    },
    "11": {
        title: "战略总监",
        department: "C&A 营运基地",
    },
    "10": {
        title: "策划总监",
        department: "C&A 战略办事处",
    },
    "9": {
        title: "行政总监",
        department: "C&A 策划办事处",
    },
    "8": {
        title: "市场总监",
        department: "C&A 行政办事处",
    },
    "7": {
        title: "执行总监",
        department: "C&A 市场办事处",
    },
    "6": {
        title: "监督主任",
        department: "C&A 执行办事处",
    },
    "5": {
        title: "咨询主任",
        department: "C&A 监督办事处",
    },
    "4": {
        title: "指导主任",
        department: "C&A 咨询办事处",
    },
    "3": {
        title: "业务主任",
        department: "C&A 指导办事处",
    },
    "2": {
        title: "中国区域合伙人",
        department: "C&A 业务办事处",
    },
} as const;

const TeamWelcomePage = () => {
    const navigate = useNavigate();

    const { refer } = useRegisterStore();

    // 存储推荐人refer 到 sessionStorage，防止刷新页面后refer丢失
    const [sessionRefer] = useState(() => {
        // 其次从 sessionStorage 获取
        const savedRefer = sessionStorage.getItem('refer');

        // 如果 refer 存在，保存到 sessionStorage
        if (refer) {
            sessionStorage.setItem('refer', refer);
            return refer;
        }

        // 返回已保存的 refer
        return savedRefer || '';
    });

    // 获取推荐人信息
    const { run: getReffererByToken, data: refferer } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<ReffererInfo>(
                "/api/v1/team/getRecommendInfo",
                "POST",
                {
                    token: sessionRefer,
                }
            );

            if (!error) {
                return result.data;
            } else {
                return null;
            }
        },
        {
            manual: false,
            ready: sessionRefer?.length > 0,
        }
    );

    const toDocuments = useMemoizedFn(() => {
        navigate("/account/documents");
    });

    const [titleText, setTitleText] = useState("欢迎您加入 “全球合伙人大联盟” 成为 C&A 中国区域合伙人，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，然后跟随系统指示完成合伙人加盟流程，设置您的 “AI 个人办公室”，开通 AI 账号，开启业务工作。");

    // 根据不同的referral_id设置不同的titleText
    useEffect(() => {
        // 推荐人三三制等级
        const teamRank = refferer?.is_team && refferer?.team_rank || null;

        if (!teamRank || !POSITION_TEXTS[teamRank as keyof typeof POSITION_TEXTS]) {
            return; // 使用默认文本
        }

        const position = POSITION_TEXTS[teamRank as keyof typeof POSITION_TEXTS];

        if (teamRank == "2") {
            setTitleText(
                `恭贺您受 C&A 业务办事处的推荐加入成为 C&A ${position.title}，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，跟随系统指示完成合伙人加盟流程，设置您的 "AI 个人办公室"，开通 AI 账号，开启业务工作。`
            );
        } else {
            const department = position.department;
            const title = position.title;

            setTitleText(
                `恭贺您受 ${department}的推荐出任 "${title}" 职位，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，跟随系统指示完成合伙人加盟流程，并在您的专属 "AI 办事处" 获取 "${title}" 手册，开启工作完成职务指标。`
            );
        }
    }, [refferer]);

    return (
        <div className="tw-relative boot-bg">
            <Image
                className="sm:tw-absolute tw-hidden sm:tw-block tw-z-10 tw-left-10 tw-top-6 tw-w-5"
                src="/images/C&A-Logo-Icon-White.svg"
                alt=""
                w={80}
            />
            <Image
                className="sm:tw-absolute sm:tw-hidden  tw-z-10 tw-left-10 tw-top-6 tw-w-5 tw-mx-auto"
                src="/images/C&A-Logo-Full-White.svg"
                alt=""
                w={220}
            />
            <Box className="team-page-container tw-relative tw-flex tw-items-center tw-px-2 !tw-overflow-y-auto md:tw-px-0 tw-py-2 md:tw-py-0 ">
                <div className="team-page-footer"></div>
                <Image
                    src="/images/team/ai.jpg"
                    alt="Person"
                    className="tw-relative -tw-bottom-[12px] tw-self-end
                    tw-left-0 tw-w-auto tw-h-[600px] tw-z-20
                    -tw-ml-40 -tw-mr-10 tw-hidden custom:tw-block"
                />
                <Stack
                    className="tw-tracking-[3px] tw-text-justify tw-relative tw-z-10
                 tw-p-8 tw-bg-basic-8 tw-bg-opacity-60 tw-flex-1
                   max-custom:tw-mx-auto custom:tw-mr-10 tw-border tw-min-h-[500px] tw-max-w-[800px] md:tw-gap-6 tw-gap-3"
                >
                    {/* 暂时注释， 推荐合伙人后续可能要加上 */}
                    {/* <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        恭贺您受推荐加入C&A全球合伙人大联盟，申请成为C&A中国区域联盟合伙人。
                    </Text> */}
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        亲爱的朋友，
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        C&A 启动 “全球合伙人大联盟”，打造全球最强大的专业人士资源共享 AI 赋能平台，邀请中国区域专业人士加盟，共享 C&A 强大的品牌 IP 和独家代理的 “绿智地球” 业务，加入全球最顶尖的智库型专业人士圈层，对接世界各地的专业资源和商业机会。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        C&A 提供 “AI 个人办公室”、“AI 小助理” 和 “AI 国际咨询资源库”，赋能合伙人于国际咨询和跨国商业服务的业务能力。配置 “全球资源共享 AI 平台” 和 “全球语言切换 AI 洽谈室” 促进世界各地合伙人之间的交流和合作，相互支援落地业务，共享客户人脉资源，开拓海外市场新契机，撮合彼此间企业客户的生意对接。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        C&A 独家代理的 “绿智地球” 业务和独创的 “战略沙盘” 工具，赋能合伙人协助中国企业运用 AI 以最短的时间和最低的成本跳出内卷，领跑绿色智慧新赛道，抢占海外无人区国际市场，辅导中国企业以 “知识经济” 借力 “资本市场” 的创新模式开展全球战略布局，抓住时代风口成为 “绿色智慧城市” 特定行业或细分领域的霸主。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        {titleText}
                    </Text>
                    {/* <Text className="tw-text-right tw-mt-auto tw-text-md md:tw-text-lg tw-text-white">
                        <Text
                            span
                            className="tw-cursor-pointer tw-font-bold tw-text-xl hover:tw-text-secondary-5"
                            onClick={toDocuments}
                        >
                           继续 {">>"}
                        </Text>
                    </Text> */}
                    <div className="tw-flex tw-justify-end">
                        <div
                            className="dynamicButton tw-px-8"
                            onClick={toDocuments}
                        >
                            <div>继续 {">>"}</div>
                        </div>
                    </div>
                </Stack>
            </Box>
            <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2025
            </div>
        </div>
    );
};

export default TeamWelcomePage;
