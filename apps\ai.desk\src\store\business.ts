import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type BusinessSection =
    | "unlicensed-intro"
    | "unlicensed-courses"
    | "unlicensed-learn"
    | "unlicensed-exam"
    | "unlicensed-overdue-pay" // 管理合伙人(逾期支付)
    | "licensed-list"
    | "basic-licensed-list" //基础业务认证牌照
    | "green-earth" //绿智地球
    | "professional-licensed-pay"; //专案认证牌照(付款)

// 晋升管理合伙人考试部分
export type UpgradeExamSection =
    | "upgrade-exam-statement"
    | "upgrade-exam-apply-options"
    | "upgrade-exam-payment"
    | "upgrade-exam-start"
    | "upgrade-exam-answering"
    | "upgrade-exam-documents"
    | "upgrade-exam-success"
    | "upgrade-exam-fail";

export type BusinessState = {
    // 当前业务部分
    businessSection: BusinessSection;
    upgradeExamSection: UpgradeExamSection;

    // 课程相关状态
    courseId: number | null;
    serviceId: number | null;
    courseList: TCourseItem[] | null;
    currentCourse: TCourseItem | null;

    // 学习进度相关
    learningProgress: {
        currentPage: number;
        totalPages: number;
        progress: number;
        timer: number;
        isTimerRunning: boolean;
    };

    // UI 状态
    isLoading: boolean;
    error: string | null;
    showAITeacher: boolean;
};

const baseBusinessStore = create<BusinessState>()(
    devtools(
        (set, get) => ({
            businessSection: "unlicensed-intro",
            upgradeExamSection: "upgrade-exam-statement",
            courseId: null,
            serviceId: null,
            courseList: null,
            currentCourse: null,
            learningProgress: {
                currentPage: 1,
                totalPages: 0,
                progress: 0,
                timer: 0,
                isTimerRunning: false,
            },
            isLoading: false,
            error: null,
            showAITeacher: false,
        }),
        {
            name: "business-store",
        }
    )
);

// 更新业务部分的 action
export const setBusinessSection = (section: BusinessSection) => {
    baseBusinessStore.setState({ businessSection: section });
};

// 更新晋升管理合伙人考试部分的 action
export const setUpgradeExamSection = (section: UpgradeExamSection) => {
    baseBusinessStore.setState({ upgradeExamSection: section });
    // 同步设置sessionStorage, 使刷新页面后也可继续
    sessionStorage.setItem("upgradeExamSection", section);
};

// 设置课程信息
export const setCourseInfo = (courseId: number, serviceId: number) => {
    baseBusinessStore.setState({ courseId, serviceId });
};

// 设置课程列表
export const setCourseList = (courseList: TCourseItem[]) => {
    baseBusinessStore.setState({ courseList });
};

// 设置当前课程
export const setCurrentCourse = (course: TCourseItem) => {
    baseBusinessStore.setState({ currentCourse: course });
};

// 更新学习进度
export const updateLearningProgress = (
    updates: Partial<BusinessState["learningProgress"]>
) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            ...updates,
        },
    }));
};

// 设置学习进度
export const setLearningProgress = (
    progress: BusinessState["learningProgress"]
) => {
    baseBusinessStore.setState({ learningProgress: progress });
};

// 更新当前页面
export const setCurrentPage = (page: number) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            currentPage: page,
        },
    }));
};

// 更新总页数
export const setTotalPages = (totalPages: number) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            totalPages,
        },
    }));
};

// 更新进度百分比
export const setProgress = (progress: number) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            progress,
        },
    }));
};

// 计时器相关
export const setTimer = (timer: number) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            timer,
        },
    }));
};

export const setIsTimerRunning = (isRunning: boolean) => {
    baseBusinessStore.setState((state) => ({
        learningProgress: {
            ...state.learningProgress,
            isTimerRunning: isRunning,
        },
    }));
};

// 设置加载状态
export const setBusinessLoading = (loading: boolean) => {
    baseBusinessStore.setState({ isLoading: loading });
};

// 设置错误信息
export const setBusinessError = (error: string | null) => {
    baseBusinessStore.setState({ error });
};

// 切换 AI 教师显示状态
export const toggleAITeacher = () => {
    baseBusinessStore.setState((state) => ({
        showAITeacher: !state.showAITeacher,
    }));
};

export const setShowAITeacher = (show: boolean) => {
    baseBusinessStore.setState({ showAITeacher: show });
};

// 重置业务状态
export const resetBusinessState = () => {
    baseBusinessStore.setState({
        businessSection: "unlicensed-intro",
        upgradeExamSection: "upgrade-exam-statement",
        courseId: null,
        serviceId: null,
        courseList: null,
        currentCourse: null,
        learningProgress: {
            currentPage: 1,
            totalPages: 0,
            progress: 0,
            timer: 0,
            isTimerRunning: false,
        },
        isLoading: false,
        error: null,
        showAITeacher: false,
    });
};

// 重置学习进度
export const resetLearningProgress = () => {
    baseBusinessStore.setState({
        learningProgress: {
            currentPage: 1,
            totalPages: 0,
            progress: 0,
            timer: 0,
            isTimerRunning: false,
        },
    });
};

const useBusinessStore = createSelectors(baseBusinessStore);

export default useBusinessStore;
