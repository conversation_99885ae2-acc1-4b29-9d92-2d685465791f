import { <PERSON>na<PERSON>utton } from "@code.8cent/react/components";
import {
    Stack,
    Group,
    Text,
    Paper,
    SimpleGrid,
    Divider,
    ScrollArea,
    Image,
} from "@mantine/core";
import { CaretLeft, FilePdf } from "@phosphor-icons/react";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import api from "@/apis";
import { useFileViewer } from "@code.8cent/react/FileViewer";

const Profile = () => {
    const navigate = useNavigate();

    const { openFileView } = useFileViewer();

    const location = useLocation();
    const profileId = location.state?.profileId;

    const [memberInfo, setMemberInfo] = useState<MemberProfileResponse | null>(
        null
    );

    useEffect(() => {
        if (profileId) {
            // 获取用户信息
            api.user.getUser(profileId).then((res) => {
                setMemberInfo(res);
            });
        }
    }, [profileId]);

    const handleBack = () => {
        navigate("/member/associates");
    };

    const isPdfFile = (filename: string) => {
        return filename.toLowerCase().endsWith(".pdf");
    };

    const renderFile = (item: string, alt: string) => {
        if (isPdfFile(item)) {
            return (
                <Group
                    key={item}
                    className="tw-border tw-border-red-400 tw-rounded-md tw-h-24 tw-w-20 tw-flex tw-items-center tw-justify-center tw-cursor-pointer"
                    onClick={() =>
                        openFileView(
                            `${window.api_base_url}/api/v1/file/resource?path=${item}&type=remote`
                        )
                    }
                >
                    <FilePdf
                        size={40}
                        weight="fill"
                        color="red"
                    />
                </Group>
            );
        }
        return (
            <Image
                key={item}
                src={`https://doc.corporate-advisory.cn/${item}`}
                alt={alt}
                w={160}
                className="tw-cursor-pointer"
                draggable={false}
                onClick={() =>
                    openFileView(
                        `${window.api_base_url}/api/v1/file/resource?path=${item}&type=remote`
                    )
                }
            />
        );
    };

    return (
        <Stack>
            <Group justify="space-between">
                <CnaButton
                    leftSection={<CaretLeft />}
                    variant="default"
                    className="tw-bg-[#F6F6F6] tw-border-none"
                    size="md"
                    fw={700}
                    onClick={handleBack}
                >
                    返回
                </CnaButton>
                <Text fw={700}>查看资料</Text>
            </Group>

            <ScrollArea h="calc(100vh - 200px)">
                <Stack>
                    <Paper
                        p="lg"
                        radius="lg"
                    >
                        <Text
                            fw={700}
                            mb="md"
                        >
                            基本信息
                        </Text>
                        <SimpleGrid
                            cols={2}
                            mb="md"
                        >
                            <Stack gap="xs">
                                <Text>推荐人姓名（如有）：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.parentInfo?.profileName}
                                </Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>专业属性：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profession}
                                </Text>
                            </Stack>
                        </SimpleGrid>
                        <SimpleGrid
                            cols={2}
                            mb="md"
                        >
                            <Stack gap="xs">
                                <Text>当前职位：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.position}
                                </Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>从业年限：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.work_year}
                                </Text>
                            </Stack>
                        </SimpleGrid>
                        <SimpleGrid
                            cols={2}
                            mb="md"
                        >
                            <Stack gap="xs">
                                <Text>手机号码：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileContact}
                                </Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>邮箱：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileEmail}
                                </Text>
                            </Stack>
                        </SimpleGrid>
                        <Divider
                            variant="dotted"
                            my="md"
                        />
                        <SimpleGrid
                            cols={2}
                            mb="md"
                        >
                            <Stack gap="xs">
                                <Text>中文名：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileName}
                                </Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>英文名：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileEnglishName}
                                </Text>
                            </Stack>
                        </SimpleGrid>
                        <SimpleGrid
                            cols={2}
                            mb="md"
                        >
                            <Stack gap="xs">
                                <Text>性别：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileGender === "F"
                                        ? "女"
                                        : "男"}
                                </Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>出生日期：</Text>
                                <Text c="dimmed">
                                    {memberInfo?.userInfo?.profileBirthDate}
                                </Text>
                            </Stack>
                        </SimpleGrid>
                        <Divider
                            variant="dotted"
                            my="md"
                        />
                        <SimpleGrid cols={2}>
                            <Stack gap="xs">
                                <Text>现住地址：</Text>
                                <Text c="dimmed">{`${memberInfo?.userInfo?.division}`}</Text>
                            </Stack>
                            <Stack gap="xs">
                                <Text>&nbsp;</Text>
                                <Text c="dimmed">{`${
                                    memberInfo?.userInfo
                                        ?.profileAddressStreet ?? ""
                                } ${
                                    memberInfo?.userInfo?.profileAddressUnit ??
                                    ""
                                }`}</Text>
                            </Stack>
                        </SimpleGrid>
                    </Paper>

                    <Paper
                        p="lg"
                        radius="lg"
                    >
                        <Text
                            fw={700}
                            mb="md"
                        >
                            专业资质证明
                        </Text>
                        <Stack
                            gap="0"
                            mb="md"
                        >
                            <Text
                                fw={600}
                                size="sm"
                            >
                                最高学历
                            </Text>
                            <Group justify="center">
                                {memberInfo?.education?.map((item) =>
                                    renderFile(item, "最高学历")
                                )}
                            </Group>
                        </Stack>
                        <Stack gap="0">
                            <Text
                                fw={600}
                                size="sm"
                            >
                                专业资格证书（选填）
                            </Text>
                            <Group justify="center">
                                {memberInfo?.certificate
                                    ?.filter((item) => item !== "")
                                    .map((item) =>
                                        renderFile(item, "专业资格证书")
                                    )}
                            </Group>
                        </Stack>
                    </Paper>

                    <Paper
                        p="lg"
                        radius="lg"
                    >
                        <Text
                            fw={700}
                            mb="md"
                        >
                            业务案例说明
                        </Text>
                        {memberInfo?.businessCase?.map((item, index) => (
                            <div key={item.id}>
                                <Stack gap="xs">
                                    <Text size="sm">案例名称：</Text>
                                    <Text
                                        c="dimmed"
                                        size="sm"
                                    >
                                        {item.name}
                                    </Text>

                                    <Text>项目地点：</Text>
                                    <Text
                                        c="dimmed"
                                        size="sm"
                                    >
                                        {item.address}
                                    </Text>
                                    <Text>项目时间：</Text>
                                    <Text
                                        c="dimmed"
                                        size="sm"
                                    >
                                        {item.date}
                                    </Text>
                                    <Text>服务内容：</Text>
                                    <Text
                                        c="dimmed"
                                        size="sm"
                                    >
                                        {item.content}
                                    </Text>
                                    <Text>成果简述：</Text>
                                    <Text
                                        c="dimmed"
                                        size="sm"
                                    >
                                        {item.result}
                                    </Text>
                                </Stack>
                                {index !==
                                    memberInfo?.businessCase?.length - 1 && (
                                    <Divider
                                        variant="dotted"
                                        my="sm"
                                    />
                                )}
                            </div>
                        ))}
                        {memberInfo?.AICase?.length > 0 && (
                            <Divider
                                variant="dotted"
                                my="sm"
                            />
                        )}
                        {memberInfo?.AICase?.map((item, index) => (
                            <Stack
                                key={index}
                                gap="xs"
                                mt="xs"
                            >
                                <Text>AI 工具使用经验：</Text>
                                <Text
                                    c="dimmed"
                                    size="sm"
                                >
                                    {item.content}
                                </Text>
                            </Stack>
                        ))}
                    </Paper>
                </Stack>
            </ScrollArea>
        </Stack>
    );
};

export default Profile;
