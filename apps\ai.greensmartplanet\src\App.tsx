import { MantineProvider } from '@mantine/core';

function App() {
  return (
    <MantineProvider
      theme={{
        components: {
          TextInput: {
            styles: {
              input: {
                height: '42px',
                minHeight: '42px',
              },
              label: {
                color: '#868E96',
                fontWeight: 400,
              }
            },
          },
          Button: {
            styles: {
              root: {
                height: '39px',
                borderRadius: '32px',
              }
            },
          }
        }
      }}
    >
      {/* 你的应用内容 */}
    </MantineProvider>
  );
}

export default App; 