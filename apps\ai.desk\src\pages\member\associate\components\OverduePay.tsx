import { <PERSON>na<PERSON>utton } from "@code.8cent/react/components";
import {
    Divider,
    Stack,
    Text,
    Image,
    ActionIcon,
    Overlay,
    Group,
} from "@mantine/core";
import {
    ArrowCounterClockwise,
    CheckCircle,
    Warning,
} from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { useRequest, useAsyncEffect } from "ahooks";
import api from "@/apis";
import { sleepMs } from "@code.8cent/utils";
import { useNavigate } from "react-router-dom";
import useProfileStore from "@/store/profile";

const OverduePay = () => {
    const navigate = useNavigate();
    const { setProfile } = useProfileStore();

    const [isPaid, setIsPaid] = useState(false);
    const [refreshQrCode, setRefreshQrCode] = useState(false);

    const [processNo, setProcessNo] = useState<string | null>(null);

    // 获取支付二维码
    const { run: generatePayQRCode, data: payQRCode } = useRequest(
        async () => {
            try {
                let qrCode = await api.payment.getQRCode(
                    "profile_upgrade_partner"
                );

                return qrCode;
            } catch (error) {
                return null;
            }
        },
        {
            manual: true,
        }
    );

    const { run: addProcessAndGetQRCode } = useRequest(
        async () => {
            try {
                // 增加进程
                const res = await api.exam.addProcess(
                    "profile_upgrade_partner"
                );
                setProcessNo(res.data.process_order_no);

                // 获取支付二维码
                generatePayQRCode();
            } catch (error) {
                return null;
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        addProcessAndGetQRCode();
    }, []);

    // 检查支付状态
    const { data: payStatus = false, cancel } = useRequest(
        async () => {
            if (!processNo) {
                return false;
            }

            let payStatus = await api.exam.getProcessStatus(processNo);

            if (payStatus.is_finished === 1) {
                setProcessNo(null);
            }

            return Boolean(payStatus.is_finished);
        },
        {
            pollingInterval: 2000,
            pollingWhenHidden: false,
            ready: !!processNo, // 只有当 processNo 存在时才开始轮询
        }
    );

    useAsyncEffect(async () => {
        if (payStatus === true) {
            setIsPaid(true);
            await sleepMs(300);
            cancel();
        }
    }, [payStatus]);

    return (
        <Stack
            gap={0}
            mt="xl"
        >
            <div className="tw-bg-gradient-to-b tw-from-[#425097] tw-to-[#353066] tw-rounded-t-[50px] tw-h-[60px]"></div>
            <Stack
                p="lg"
                className="tw-bg-white tw-rounded-b-xl tw-w-full"
            >
                <Group
                    wrap="nowrap"
                    gap="xs"
                >
                    <Warning
                        color="#F24B4B"
                        size={24}
                    />
                    <Text size="sm">
                        抱歉，由于在30日规定周期内未能成功推荐一位合伙人加盟，您已丧失管理合伙人晋级资格，现需补缴相关费用。
                    </Text>
                </Group>
                <Divider variant="dotted" />
                <Stack gap={0}>
                    <Text>费用包含：</Text>
                    <Text>1.加盟保证金 SGD$5,500</Text>
                    <Text>2.首年加盟基础服务费 SGD$2,400</Text>
                    <Text>3.加盟开户费 SGD$3,500</Text>
                    <Text>4.AI专属账号开通费 SGD$3,500</Text>
                    <Text>5.业务资格证 SGD$2,000</Text>
                </Stack>
                <Stack className="tw-items-center tw-mt-8">
                    <div className="tw-relative">
                        <Image
                            src={payQRCode}
                            w={180}
                            draggable={false}
                        />
                        {isPaid && (
                            <Overlay
                                center
                                color="#F1F3F5"
                                backgroundOpacity={0.8}
                            >
                                <Stack
                                    className="tw-items-center"
                                    gap={0}
                                >
                                    <CheckCircle
                                        size={48}
                                        weight="fill"
                                        className="tw-text-green-500"
                                    />
                                    <Text
                                        fw={600}
                                        size="sm"
                                    >
                                        付款成功
                                    </Text>
                                </Stack>
                            </Overlay>
                        )}
                        {refreshQrCode && (
                            <Overlay
                                center
                                color="#F1F3F5"
                                backgroundOpacity={0.8}
                            >
                                <Stack
                                    className="tw-items-center"
                                    gap={0}
                                >
                                    <ActionIcon
                                        variant="default"
                                        radius="xl"
                                        className="tw-border-none tw-bg-[#F98500] hover:tw-bg-[#F98500]/80"
                                        onClick={() => setRefreshQrCode(false)}
                                        size="xl"
                                    >
                                        <ArrowCounterClockwise
                                            size={48}
                                            className="tw-text-white"
                                        />
                                    </ActionIcon>
                                    <Text
                                        fw={600}
                                        size="sm"
                                    >
                                        二维码已过期
                                    </Text>
                                </Stack>
                            </Overlay>
                        )}
                    </div>
                    <Text
                        fw={600}
                        c="#F98500"
                        className="tw-text-3xl"
                    >
                        SGD$16,900
                    </Text>
                    <Text
                        size="sm"
                        className="tw-mb-8"
                    >
                        请扫码支付（微信、支付宝）
                    </Text>
                    {isPaid && (
                        <CnaButton
                            className="tw-w-full"
                            onClick={async () => {
                                try {
                                    // 刷新profile数据
                                    const userData =
                                        await api.user.getUserProfile();
                                    if (userData) {
                                        setProfile(userData);
                                    }
                                } catch (error) {
                                    console.error(
                                        "刷新profile数据失败:",
                                        error
                                    );
                                }

                                // 跳转到index页面
                                navigate("/member/index", {
                                    replace: true,
                                });
                            }}
                        >
                            完成
                        </CnaButton>
                    )}
                </Stack>
            </Stack>
        </Stack>
    );
};

export default OverduePay;
