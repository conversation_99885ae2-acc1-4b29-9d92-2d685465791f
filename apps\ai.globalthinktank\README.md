# AI.CORPORATE-ADVISORY

This project is a sub-repository within a Turborepo setup, all multiple projects shared packages is located at `../../packages`

### Library

- [Main Framework - React](https://react.dev/)
- [<PERSON><PERSON>](https://mantine.dev/)
- [TailwindCSS](https://tailwindcss.com/)
- [Forms Management - React Hook Form](https://www.react-hook-form.com/)
- [State Management - Zustand](https://github.com/pmndrs/zustand)

### Folders

- `src/@types` Contains all the types defininations of the project, for multiple projects shared types, see `../../packages/shared-types` folder.
- `src/pages` Project specificed pages is located, for multiple projects shared pages, see `../../packages/react/src/pages` folder.
- `src/apis` contains project specificed apis called logic, and files named based on function modules.
- `src/components` contains project components
- router defines in `router/index.tsx`

### Notes
- Prefer using the `CnaButton` shared component for buttons on pages, as it standardizes styles and handling logic for loading, disabled, and other button states. Source code is located in `packages/react/src/components/common/CnaButton.tsx`

- Use `noty` for alert popups, such as success and error notifications. Source code is located in `packages/utils/noty`


