import { Split } from "@gfazioli/mantine-split-pane";
import {
    Paper,
    Textarea,
    Text,
    Stack,
    Group,
    Radio,
    List,
} from "@mantine/core";
import "@gfazioli/mantine-split-pane/styles.css";
import { CaretLeft } from "@phosphor-icons/react";
import { CnaButton } from "@code.8cent/react/components";
import useModalStore from "@/store/modal";
import { useNavigate, useLocation } from "react-router-dom";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import { useState } from "react";
import { useRequest } from "ahooks";

interface InterviewForm {
    personality: string;
    logic: string;
    insight: string;
    innovation: string;
    efficiency: string;
    status: string;
}

const Interview = () => {
    const openConfirm = useModalStore.use.openConfirm();
    const navigate = useNavigate();
    const [isDirty, setIsDirty] = useState(false);
    const [formData, setFormData] = useState<InterviewForm>({
        personality: "",
        logic: "",
        insight: "",
        innovation: "",
        efficiency: "",
        status: "",
    });

    // 获取合伙人ID面试详情
    const { profileId } = useLocation()?.state as { profileId: number };

    // 获取合伙人面试详情
    const { data: interviewDetail, loading } = useRequest(
        async () => {
            const res = await api.interview.getDetail({ user_id: profileId });
            // 如果有comment_draft，则将comment_draft转换为formData
            if (res.comment_draft) {
                setFormData(JSON.parse(res.comment_draft));
            }
            return res.question || [];
        },
        {
            refreshDeps: [profileId],
        }
    );

    const handleBack = () => {
        if (isDirty) {
            openConfirm({
                title: "确定要返回？",
                message: "返回上一界面，当前面试报告将不会保存",
                onConfirm: () => {
                    navigate("/member/associates", { replace: true });
                },
            });
        } else {
            navigate("/member/associates", { replace: true });
        }
    };

    // 处理表单字段变化
    const handleFormChange = (field: keyof InterviewForm, value: string) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
        setIsDirty(true);
    };

    // 检查表单是否有效
    const isFormValid = () => {
        return Object.values(formData).every((value) => value.trim() !== "");
    };

    // 提交面试报告或草稿
    const submitCommentOrDraft = async (isDraft: boolean) => {
        try {
            const params: {
                user_id: number;
                comment_draft?: string;
                comment?: string;
            } = {
                user_id: profileId,
            };

            const commentData = {
                ...formData,
            };

            if (isDraft) {
                params.comment_draft = JSON.stringify(commentData);
            } else {
                params.comment = JSON.stringify(commentData);
            }

            const res = await api.interview.submitComment(params);

            if (!res) {
                noty.error("提交失败");
                return false;
            }

            setIsDirty(false);
            // 保存草稿不用跳转
            if (isDraft) {
                noty.success("保存草稿成功");
                return;
            }
            noty.success("提交成功");
            navigate("/member/associates", { replace: true });
        } catch (error) {
            console.error(error);
            noty.error("提交失败");
        }
    };

    // 提交面试报告
    const handleSubmit = () => {
        if (!isFormValid()) {
            noty.error("请填写完整的面试评价");
            return;
        }

        openConfirm({
            title: "确定提交？",
            message: "面试报告提交后，将不能再修改",
            onConfirm: () => {
                submitCommentOrDraft(false);
            },
        });
    };

    // 保存草稿
    const handleSaveDraft = () => {
        submitCommentOrDraft(true);
    };

    return (
        <Stack>
            <Group justify="space-between">
                <CnaButton
                    leftSection={<CaretLeft />}
                    variant="default"
                    className="tw-bg-[#F6F6F6] tw-border-none"
                    size="md"
                    fw={700}
                    onClick={handleBack}
                >
                    返回
                </CnaButton>
                <Text fw={700}>合伙人面试进行中</Text>
            </Group>

            <Split style={{ height: "90vh" }}>
                <Split.Pane
                    initialWidth="50%"
                    minWidth="50%"
                    maxWidth="60%"
                >
                    <Paper
                        p="md"
                        h="75vh"
                        className="tw-overflow-auto tw-bg-[#F6F6F6]"
                    >
                        <Stack>
                            {interviewDetail &&
                                interviewDetail.map((question, index) => (
                                    <Stack
                                        key={index}
                                        gap={2}
                                    >
                                        <Text
                                            size="sm"
                                            fw={600}
                                        >
                                            {`第${index + 1}题：`}
                                        </Text>
                                        <Text size="sm">
                                            {question.question}
                                        </Text>
                                        {/* 如果有追问问题 question.nexnext_question */}
                                        {question.next_question &&
                                            question.next_question.length >
                                                0 && (
                                                <List size="sm">
                                                    {question.next_question.map(
                                                        (item, index) => (
                                                            <List.Item
                                                                key={index}
                                                            >
                                                                {` - 追问：${item}`}
                                                            </List.Item>
                                                        )
                                                    )}
                                                </List>
                                            )}
                                    </Stack>
                                ))}
                            {interviewDetail &&
                                interviewDetail.length === 0 && (
                                    <Text size="sm">
                                        暂无面试题目，请联系技术员
                                    </Text>
                                )}
                        </Stack>
                    </Paper>
                </Split.Pane>
                <Split.Resizer />
                <Split.Pane
                    initialWidth="50%"
                    minWidth="40%"
                    maxWidth="50%"
                >
                    <Paper
                        p="md"
                        h="75vh"
                        className="tw-overflow-auto tw-bg-[#F6F6F6]"
                    >
                        <Stack gap="xs">
                            <Textarea
                                label="个人气质与态度评价："
                                labelProps={{
                                    size: "lg",
                                    fw: 700,
                                }}
                                placeholder="文字详述说明"
                                radius="lg"
                                autosize
                                minRows={4}
                                maxRows={4}
                                value={formData.personality}
                                onChange={(e) =>
                                    handleFormChange(
                                        "personality",
                                        e.target.value
                                    )
                                }
                                styles={{
                                    input: {
                                        border: "none",
                                    },
                                }}
                            />
                            <Textarea
                                label="逻辑思维能力评价："
                                labelProps={{
                                    size: "lg",
                                    fw: 700,
                                }}
                                placeholder="文字详述说明"
                                radius="lg"
                                autosize
                                minRows={4}
                                maxRows={4}
                                value={formData.logic}
                                onChange={(e) =>
                                    handleFormChange("logic", e.target.value)
                                }
                                styles={{
                                    input: {
                                        border: "none",
                                    },
                                }}
                            />
                            <Textarea
                                label="长远眼光与洞察力评价："
                                labelProps={{
                                    size: "lg",
                                    fw: 700,
                                }}
                                placeholder="文字详述说明"
                                radius="lg"
                                autosize
                                minRows={4}
                                maxRows={4}
                                value={formData.insight}
                                onChange={(e) =>
                                    handleFormChange("insight", e.target.value)
                                }
                                styles={{
                                    input: {
                                        border: "none",
                                    },
                                }}
                            />
                            <Textarea
                                label="学习与创新能力评价："
                                labelProps={{
                                    size: "lg",
                                    fw: 700,
                                }}
                                placeholder="文字详述说明"
                                radius="lg"
                                autosize
                                minRows={4}
                                maxRows={4}
                                value={formData.innovation}
                                onChange={(e) =>
                                    handleFormChange(
                                        "innovation",
                                        e.target.value
                                    )
                                }
                                styles={{
                                    input: {
                                        border: "none",
                                    },
                                }}
                            />
                            <Textarea
                                label="行动与效率评价："
                                labelProps={{
                                    size: "lg",
                                    fw: 700,
                                }}
                                placeholder="文字详述说明"
                                radius="lg"
                                autosize
                                minRows={4}
                                maxRows={4}
                                value={formData.efficiency}
                                onChange={(e) =>
                                    handleFormChange(
                                        "efficiency",
                                        e.target.value
                                    )
                                }
                                styles={{
                                    input: {
                                        border: "none",
                                    },
                                }}
                            />
                            <Group align="center">
                                <Text
                                    size="sm"
                                    fw={700}
                                >
                                    面试结果：
                                </Text>
                                <Radio.Group
                                    name="status"
                                    value={formData.status}
                                    onChange={(value) =>
                                        handleFormChange("status", value)
                                    }
                                >
                                    <Group>
                                        <Radio
                                            value="1"
                                            label="通过"
                                        />
                                        <Radio
                                            value="2"
                                            label="不通过"
                                        />
                                    </Group>
                                </Radio.Group>
                            </Group>
                        </Stack>
                    </Paper>
                    <div className="tw-flex tw-justify-end tw-gap-2 tw-mt-2">
                        <CnaButton
                            variant="outline"
                            radius="lg"
                            onClick={handleSaveDraft}
                            disabled={!isDirty}
                        >
                            保存草稿
                        </CnaButton>
                        <CnaButton
                            radius="lg"
                            onClick={handleSubmit}
                            disabled={!isFormValid()}
                        >
                            提交面试报告
                        </CnaButton>
                    </div>
                </Split.Pane>
            </Split>
        </Stack>
    );
};

export default Interview;
