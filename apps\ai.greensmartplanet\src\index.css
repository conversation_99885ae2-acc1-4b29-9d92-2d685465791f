@layer tailwind {
    @tailwind base;
}
@tailwind components;
@tailwind utilities;

@media print {
    * {
        overflow: visible !important;
    }
}



.custom-tabs {
    gap: 0;
    position: relative;
    border-bottom: 2px solid;
    @apply !tw-border-b-gray-300;
}

.custom-tabs .nav-link {
    padding: 0.5rem 1rem;
    margin-bottom: -2px;
    border-bottom: 2px solid transparent;
    @apply !tw-text-gray-500;
    @apply !tw-font-normal;
    @apply hover:!tw-border-b-gray-800;
    @apply hover:!tw-text-gray-800;
}

.custom-tabs .nav-link.active {
    @apply !tw-border-b-gray-800;
    @apply !tw-text-gray-800;
}

.table-fixed-header {
    overflow-y: auto;
}

.table-fixed-header thead th {
    position: sticky;
    top: 0;
    background: #f8f9fa; /* Background color for the header */
    z-index: 1;
}

.account-form-label {
    @apply tw-text-[16px] tw-font-normal md:tw-mb-1 tw-mb-0;
}

.profile-update-password-btn {
    @apply tw-border tw-bg-transparent tw-border-neutral-300 tw-text-black tw-font-normal;
    @apply hover:tw-border hover:!tw-bg-transparent hover:tw-border-neutral-300 hover:tw-text-black hover:tw-font-normal;
}

.mantine-Button-root {
    @apply tw-font-normal;
}

.profile-form-label {
    @apply tw-mb-2 tw-w-full tw-bg-basic-5 tw-text-gray-50 tw-py-1 tw-px-2;
}

/* TODO: 需要重构 - 优化表格样式的全局设置 */
table th:not(.mantine-DateInput-weekday):not(.mantine-DatePickerInput-weekday) {
    @apply !tw-bg-basic-5 !tw-text-gray-50;
}

.team-page-container {
    @apply tw-h-screen
        tw-w-screen
        tw-overflow-hidden
        tw-bg-[url('/images/auth-layout/page-background.jpg')]
        tw-bg-no-repeat
        tw-bg-cover
        tw-bg-center;
}

.ts-5-a {
    @apply tw-text-[1.5vw] tw-leading-[1.6] tw-tracking-[0.3vw];
}
.gsw-content-title-a {
    font-size: calc(1.525rem + 3.3vw);
    @apply md:tw-text-[4vw] tw-leading-[1.2] tw-tracking-[0.4vw];
}
.gsw-content-text-a {
    @apply tw-text-[1rem]  md:tw-text-[1.75vw] tw-leading-[3.3vw] tw-tracking-[0.4vw];
}
.cover {
    font-weight: 350;
    background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.6) 0%,
        rgba(255, 255, 255, 0) 100%
    );
}

.custom-stepper .mantine-Stepper-stepIcon[data-progress] {
    width: 34px;
    height: 34px;
}
.custom-stepper .mantine-Stepper-separator:not([data-active]) {
    background-color: #fff; /* 或其他你想要的颜色 */
}



@media (max-width: 768px) {
    .mantine-Stepper-step:not([data-progress]) .mantine-Stepper-stepBody {
        display: none;
    }
}

.mantine-Stepper-step:not([data-completed]) .mantine-Stepper-stepIcon {
   border-color: #fff;
}

.mantine-Stepper-step[data-progress] .mantine-Stepper-stepIcon {
    border-color: var(--step-color);
 }

.mantine-Stepper-step:not([data-completed]):not([data-progress]) .mantine-Stepper-stepIcon {
    background-color: #fff;
 }

 