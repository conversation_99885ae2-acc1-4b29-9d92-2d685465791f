import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useLockFn, useMemoizedFn, useMount, useNetwork, useUnmount } from "ahooks";
import { useEffect, useState } from "react";
import useDataStore from "@code.8cent/store/data";
import { Center, Group, Stack, Text, Image } from "@mantine/core";
import { Spinner } from "@phosphor-icons/react";
import useSettingStore from "@code.8cent/store/setting";
import Offline from "@code.8cent/react/components/Offline";
import Bowser from "bowser";

const RootLayout = () => {
    const [inited, setInited] = useState<boolean>(false);

    const { online } = useNetwork();

    const {
        setLanguages,
        setCurrencies,
        setTimezones,
        setTimeFormats,
        setDateFormats,
        setLanguageStructures,
        setCountryDatas,
        setIndustries,
        setCredentialTitles,
        setSkillTitles,
        setAreas,
        setAllLanguages,
    } = useDataStore();

    const { updateSetting } = useSettingStore();

    useMount(() => {

        const browser = Bowser.getParser(window.navigator.userAgent);

        if (browser.getBrowserName(true) === "wechat") {
            if (browser.getPlatformType(true) !== "desktop") {
                updateSetting("isWechat", true);
            }
        }

        if(browser.getOSName(true) === "ios"){
            updateSetting("isIOS", true);
        }

        // 添加Safari浏览器的判断
        if (browser.getBrowserName(true) === "safari") {
            updateSetting("isSafari", true);
        }

        setInited(true);
    });

    if (online === false) {
        return <Offline className="tw-h-screen tw-w-screen" />;
    }

    return inited === true ? (
        <div>
            <Outlet />
        </div>
    ) : (
        <Center className="tw-h-screen tw-w-screen">
            <Stack>
                <Image
                    className="tw-mx-auto"
                    src="/images/C&A-logo-icon-blue.svg"
                    alt=""
                    w={120}
                />
                <Text className="tw-text-center tw-my-6 tw-text-xl">Global Think Tank</Text>
                <Group>
                    <Spinner
                        className="tw-animate-spin"
                        size={32}
                    />
                    <Text>正在初始化配置...</Text>
                </Group>
            </Stack>
        </Center>
    );
};

export default RootLayout;
