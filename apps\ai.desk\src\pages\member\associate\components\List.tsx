import { CnaButton } from "@code.8cent/react/components";
import {
    Paper,
    Table,
    Title,
    Group,
    ScrollArea,
    Text,
    LoadingOverlay,
    Modal,
    Stack,
    Tabs,
} from "@mantine/core";
import {
    BookBookmark,
    CheckCircle,
    Clock,
    XCircle,
} from "@phosphor-icons/react";
import { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useProfileStore from "@/store/profile";

const renderInterviewStatus = (status: number) => {
    switch (status) {
        case 0:
            return (
                <Text
                    fw={600}
                    className="tw-text-[#0648DE]"
                    size="sm"
                >
                    申请面试中
                </Text>
            );
        case 1:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#FF9500]"
                >
                    <Clock
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        面试完成 - 审核中
                    </Text>
                </Group>
            );
        case 2:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#05C126]"
                >
                    <CheckCircle
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        审核通过
                    </Text>
                </Group>
            );
        case 3:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#DA0101]"
                >
                    <XCircle
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        未通过
                    </Text>
                </Group>
            );
        default:
            return null;
    }
};

const List = () => {
    const [associates, setAssociates] = useState<TAssociateItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [inviteModalOpened, setInviteModalOpened] = useState(false);
    const profileInfo = useProfileStore();
    const [isTeamJob, setIsTeamJob] = useState(false);

    const navigate = useNavigate();

    useEffect(() => {
        // TODO: 实现获取合伙人列表的逻辑
        const fetchAssociates = async () => {
            try {
                setLoading(true);
                // 先获取第一页，1000条数据（不做分页）
                const res = await api.associate.list(1, 1000);
                setAssociates(res.data);
            } catch (error) {
                console.error("获取合伙人列表失败:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchAssociates();

        // 是否三三制合伙人
        const excludedJobs = [
            "管理合伙人",
            "联盟合伙人",
            "业务主任",
            "业务合伙人",
        ];
        if (profileInfo.job && !excludedJobs.includes(profileInfo.job)) {
            setIsTeamJob(true);
        }
    }, [profileInfo]);

    const handleInviteAssociate = () => {
        setInviteModalOpened(true);
    };

    const handleViewProfile = (profileId: number) => {
        navigate("/member/associates/profile", {
            replace: true,
            state: {
                profileId,
            },
        });
    };

    // 点击面试按钮，跳转面试页面
    const handleStartInterview = (profileId: number) => {
        navigate("/member/interview", {
            replace: true,
            state: {
                profileId,
            },
        });
    };

    return (
        <div>
            <Modal
                opened={inviteModalOpened}
                onClose={() => setInviteModalOpened(false)}
                title="邀请合伙人"
                centered
                radius="md"
                styles={{
                    header: {
                        borderBottom: "1px solid #E2E5F4",
                    },
                }}
                size="lg"
            >
                <Stack className="tw-p-4">
                    <Text fw={600}>已生成您的合伙人邀请链接</Text>
                    <Text className="tw-underline">
                        {`${location.origin}/team?refer=${profileInfo.profilePartnerCode}`}
                    </Text>
                    <CnaButton
                        className="tw-w-full"
                        onClick={() => {
                            navigator.clipboard.writeText(
                                `${location.origin}/team?refer=${profileInfo.profilePartnerCode}`
                            );
                            noty.success("合伙人邀请链接已复制到剪贴板");
                        }}
                    >
                        复制链接
                    </CnaButton>
                    {isTeamJob && (
                        <>
                            <Text fw={600}>三三制邀请链接</Text>
                            <Text className="tw-underline">
                                {`${location.origin}/team?refer=${profileInfo.profileContact}`}
                            </Text>
                            <CnaButton
                                className="tw-w-full"
                                onClick={() => {
                                    navigator.clipboard.writeText(
                                        `${location.origin}/team?refer=${profileInfo.profileContact}`
                                    );
                                    noty.success(
                                        "三三制邀请链接已复制到剪贴板"
                                    );
                                }}
                            >
                                复制链接
                            </CnaButton>
                        </>
                    )}
                </Stack>
            </Modal>
            <Paper
                className="tw-mt-4 md:tw-mt-8 tw-p-4 md:tw-p-8 tw-h-full"
                radius="lg"
                pos="relative"
            >
                <LoadingOverlay visible={loading} />

                <Tabs defaultValue="associates">
                    <Tabs.List className="tw-relative tw-flex tw-flex-col md:tw-flex-row tw-gap-4 md:tw-gap-0">
                        <div className="tw-flex tw-gap-4">
                            <Tabs.Tab value="associates">
                                <Title order={4}>合伙人</Title>
                            </Tabs.Tab>
                            <Tabs.Tab value="bonus">
                                <Title order={4}>管理津贴</Title>
                            </Tabs.Tab>
                        </div>
                        <Group className="tw-static md:tw-absolute md:tw-right-0 tw-w-full md:tw-w-auto tw-justify-center md:tw-justify-end">
                            {profileInfo.role_level !== 1 &&
                                profileInfo.manager_time > 0 && (
                                    <Text
                                        c={`${
                                            profileInfo.manager_time > 3
                                                ? "#FF9500"
                                                : "#DA0101"
                                        }`}
                                        size="sm"
                                        className="tw-text-center md:tw-text-left"
                                    >
                                        未达标，豁免期限还剩
                                        {profileInfo.manager_time}天
                                    </Text>
                                )}
                            <CnaButton
                                leftSection={<BookBookmark />}
                                radius="xl"
                                variant="outline"
                                className="tw-bg-[linear-gradient(to_right,#E2E5F4_0%,#EBEDF7_20%,transparent_75%)] tw-text-[#19288F] tw-border-[#19288F] tw-w-full md:tw-w-auto"
                                onClick={handleInviteAssociate}
                            >
                                邀请合伙人
                            </CnaButton>
                        </Group>
                    </Tabs.List>

                    <Tabs.Panel value="associates">
                        <ScrollArea
                            h={{ base: "calc(100vh - 280px)", md: "calc(100vh - 400px)" }}
                            mt="lg"
                        >
                            <div className="tw-min-w-[800px] md:tw-min-w-0">
                                <Table
                                    withRowBorders={false}
                                    highlightOnHover
                                    className="custom-table"
                                    stickyHeader
                                >
                                    <Table.Thead>
                                        <Table.Tr>
                                            <Table.Th className="tw-min-w-[80px]">姓名</Table.Th>
                                            <Table.Th className="tw-min-w-[120px]">手机</Table.Th>
                                            <Table.Th className="tw-min-w-[150px]">邮箱</Table.Th>
                                            <Table.Th className="tw-min-w-[120px]">申请时间</Table.Th>
                                            <Table.Th className="tw-min-w-[140px]">状态</Table.Th>
                                            <Table.Th className="tw-min-w-[180px]">操作</Table.Th>
                                        </Table.Tr>
                                    </Table.Thead>
                                    <Table.Tbody>
                                        {associates.map((associate) => (
                                            <Table.Tr key={associate.profileID}>
                                                <Table.Td className="tw-min-w-[80px]">
                                                    {associate.profileName}
                                                </Table.Td>
                                                <Table.Td className="tw-min-w-[120px]">
                                                    {associate.profileContact}
                                                </Table.Td>
                                                <Table.Td className="tw-min-w-[150px] tw-break-all">
                                                    {associate.profileEmail}
                                                </Table.Td>
                                                <Table.Td className="tw-min-w-[120px]">
                                                    {associate.interview_time}
                                                </Table.Td>
                                                <Table.Td className="tw-min-w-[140px]">
                                                    {renderInterviewStatus(
                                                        associate.interview_status
                                                    )}
                                                </Table.Td>
                                                <Table.Td className="tw-min-w-[180px]">
                                                    <Group className="tw-flex-nowrap">
                                                        <CnaButton
                                                            variant="outline"
                                                            radius="xl"
                                                            size="xs"
                                                            className="md:tw-text-sm"
                                                            onClick={() =>
                                                                handleViewProfile(
                                                                    associate.profileID
                                                                )
                                                            }
                                                        >
                                                            查看资料
                                                        </CnaButton>
                                                        {associate.interview_status ===
                                                            0 && (
                                                            <CnaButton
                                                                radius="xl"
                                                                size="xs"
                                                                className="md:tw-text-sm"
                                                                onClick={() =>
                                                                    handleStartInterview(
                                                                        associate.profileID
                                                                    )
                                                                }
                                                            >
                                                                开始面试
                                                            </CnaButton>
                                                        )}
                                                    </Group>
                                                </Table.Td>
                                            </Table.Tr>
                                        ))}
                                        {!associates.length && !loading && (
                                            <Table.Tr>
                                                <Table.Td colSpan={6}>
                                                    <Text
                                                        ta="center"
                                                        c="dimmed"
                                                    >
                                                        暂无数据
                                                    </Text>
                                                </Table.Td>
                                            </Table.Tr>
                                        )}
                                    </Table.Tbody>
                                </Table>
                            </div>
                        </ScrollArea>
                    </Tabs.Panel>
                    <Tabs.Panel value="bonus">
                        <ScrollArea
                            h={{ base: "calc(100vh - 280px)", md: "calc(100vh - 400px)" }}
                            mt="lg"
                        >
                            <div className="tw-min-w-[1000px] md:tw-min-w-0">
                                <Table
                                    withRowBorders={false}
                                    highlightOnHover
                                    className="custom-table"
                                    stickyHeader
                                >
                                    <Table.Thead>
                                        <Table.Tr>
                                            <Table.Th className="tw-min-w-[120px]">业务完成时间</Table.Th>
                                            <Table.Th className="tw-min-w-[80px]">姓名</Table.Th>
                                            <Table.Th className="tw-min-w-[100px]">合伙人类型</Table.Th>
                                            <Table.Th className="tw-min-w-[100px]">津贴类型</Table.Th>
                                            <Table.Th className="tw-min-w-[100px]">业务类型</Table.Th>
                                            <Table.Th className="tw-min-w-[100px]">津贴金额</Table.Th>
                                            <Table.Th className="tw-min-w-[100px]">收款状态</Table.Th>
                                            <Table.Th className="tw-min-w-[120px]">到账日期</Table.Th>
                                        </Table.Tr>
                                    </Table.Thead>
                                    <Table.Tbody>
                                        <Table.Tr>
                                            <Table.Td colSpan={8}>
                                                <Text
                                                    ta="center"
                                                    c="dimmed"
                                                >
                                                    暂无数据
                                                </Text>
                                            </Table.Td>
                                        </Table.Tr>
                                    </Table.Tbody>
                                </Table>
                            </div>
                        </ScrollArea>
                    </Tabs.Panel>
                </Tabs>
            </Paper>
        </div>
    );
};

export default List;
