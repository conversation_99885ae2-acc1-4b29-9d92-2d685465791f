import { t } from "@code.8cent/i18n";
import { AuthFormCard } from "@code.8cent/react/components";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import useSettingStore from "@code.8cent/store/setting";
import { Stack, LoadingOverlay } from "@mantine/core";
import { useEffect, useState } from "react";
import {
    BasicInfoForm,
    PaymentQR,
    ProfessionalCert,
    BusinessCase,
    InterviewStatus,
    BasicInfoFormV2,
    BasicInfoFormV3,
} from "./components";
import { RegisterStep, InterviewResultStatus } from "./types";
import useRegisterStore from "@/store/register";
import api from "@/apis";

const RegisterV3 = () => {
    const { stepV3, setStepV3, interviewStatus, setInterviewStatus } =
        useRegisterStore();
    const [loading, setLoading] = useState(false);

    // 初始化 stepV3 和 interviewStatus
    useEffect(() => {
        const initStep = async () => {
            const step = sessionStorage.getItem("step-v3");
            if (step) {
                setStepV3(step as RegisterStep);
            } else {
                // 如果没有设置步骤，检查是否有 is_new
                const isNew = await window.localForage.getItem("is_new");
                const baseInfo = await window.localForage.getItem("base_info");
                if (isNew || baseInfo === 2) {
                    setStepV3("basic-info");
                }
            }

            // 初始化面试状态
            const status = sessionStorage.getItem("interview-status");
            if (status) {
                setInterviewStatus(status as InterviewResultStatus);
            }

            sessionStorage.setItem("isRegisterV3", "true");
        };
        initStep();
    }, [setStepV3, setInterviewStatus]);

    const { lang } = useSettingStore();

    const getStepContent = () => {
        switch (stepV3) {
            case "basic-info":
                return (
                    <BasicInfoFormV3
                        onNext={async (token) => {
                            try {
                                // 创建进程
                                const processRes = await api.exam.addProcess(
                                    "profile_register",
                                    token
                                );
                                // 保存业务进程编号
                                await window.localForage.setItem(
                                    "register_payment_process_no",
                                    processRes.data?.process_order_no
                                );

                                await window.localForage.removeItem("is_new");
                                await window.localForage.removeItem("base_info");

                                setStepV3("payment-qr");
                            } catch (error) {
                                console.error("添加进程失败：", error);
                            }
                        }}
                    />
                );
            case "payment-qr":
                return <PaymentQR onNext={() => setStepV3("qualification")} />;
            case "qualification":
                return (
                    <ProfessionalCert
                        onPrev={() => setStepV3("payment-qr")}
                        onNext={async () => {
                            setStepV3("business_case");
                            const registerSetting =
                                (await window.localForage.getItem(
                                    "register-setting"
                                )) as string[] | null;

                            let updateRegisterSetting: string[] = Array.isArray(
                                registerSetting
                            )
                                ? registerSetting
                                : [];
                            // 删除 qualification
                            updateRegisterSetting =
                                updateRegisterSetting.filter(
                                    (item) => item !== "qualification"
                                );
                            // 添加 business_case
                            if (
                                !updateRegisterSetting.includes("business_case")
                            ) {
                                updateRegisterSetting.push("business_case");
                            }
                            await window.localForage.setItem(
                                "register-setting",
                                updateRegisterSetting
                            );
                        }}
                    />
                );
            case "business_case":
                return (
                    <BusinessCase
                        onPrev={() => setStepV3("qualification")}
                        onNext={async () => {
                            setStepV3("interview-status");
                            setInterviewStatus("pending");
                            // 删除 business_case 和 qualification
                            const registerSetting =
                                (await window.localForage.getItem(
                                    "register-setting"
                                )) as string[] | null;

                            await window.localForage.setItem(
                                "register-setting",
                                (Array.isArray(registerSetting)
                                    ? registerSetting
                                    : []
                                ).filter(
                                    (item) =>
                                        item !== "business_case" &&
                                        item !== "qualification"
                                )
                            );
                        }}
                    />
                );
            case "interview-status":
                return (
                    <InterviewStatus status={interviewStatus || "pending"} />
                );
            default:
                return null;
        }
    };

    return (
        <AuthenticationLayout>
            <div className="tw-min-h-full tw-flex tw-items-center tw-justify-center">
                <AuthFormCard className="tw-w-[1000px] tw-max-w-full">
                    <Stack>
                        <LoadingOverlay visible={loading} />
                        {getStepContent()}
                    </Stack>
                </AuthFormCard>
            </div>
        </AuthenticationLayout>
    );
};

export default RegisterV3;
