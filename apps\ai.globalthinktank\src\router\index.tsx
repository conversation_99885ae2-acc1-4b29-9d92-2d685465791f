import { createBrowser<PERSON>outer, json, Navigate } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import withRouteGuard from "@code.8cent/react/hoc/withRouteGuard";
import LoginPage from "@code.8cent/react/pages/LoginPage";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
import SettingPage from "@code.8cent/react/pages/SettingPage";
import RootLayout from "@/layouts/RootLayout";

const GuardedRootLayout = withRouteGuard(RootLayout, {
    publicRoutes: ["/account/*", "/team", "/team/*"],
});

const router = createBrowserRouter(
    [
        {
            path: "/",
            element: <GuardedRootLayout />,
            errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
            children: [
                {
                    path: "account/login",
                    element: <LoginPage />,
                },
                {
                    path: "account/forget-password",
                    element: <ForgetPasswordPage />,
                },
            ],
        },
        {
            path: "*",
            errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
            loader: () => {
                throw Response.json(
                    {},
                    { status: 404, statusText: "Page Not Found" }
                );
            },
        },
    ],
    {
        future: {
            v7_relativeSplatPath: true,
        },
    }
);

export default router;
