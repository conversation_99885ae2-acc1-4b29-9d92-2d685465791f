import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useW<PERSON>rdStore from "@code.8cent/store/wizard";
import { cnaRequest } from "@code.8cent/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { TextInput, PasswordInput } from "@mantine/core";
import { ArrowClockwise } from "@phosphor-icons/react";
import { useRequest } from "ahooks";
import { SHA256 } from "crypto-js";
import { useForm, Controller } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { CnaButton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import { z } from "zod";
import { INTERVIEW_STATUS, INTERVIEW_STATUS_TEXT } from "@/hoc/withRouteGuard";
import { initUserData } from "@/utils/initUserData";
import useRegisterStore from "@/store/register";

type EmailLoginFormInput = {
    email: string;
    captcha: string;
    password: string;
};

const emailLoginSchema = z.object({
    email: z.string().email("form.email.incorrect"),
    captcha: z.string().min(1),
    password: z.string().min(1),
});

const initialEmailLoginFormValues = {
    email: "",
    captcha: "",
    password: "",
};

const EmailLoginForm = () => {
    const navigate = useNavigate();

    const { lang } = useSettingStore();

    const { setState: setWizardState, setRegisterSetting } = useWizardStore();
    const { setStepV3, setInterviewStatus } = useRegisterStore();

    const {
        handleSubmit,
        formState: { errors },
        control,
    } = useForm<EmailLoginFormInput>({
        defaultValues: initialEmailLoginFormValues,
        resolver: zodResolver(emailLoginSchema),
    });

    const {
        run: getCaptchaCode,
        data: captchaRes,
        loading: gettingCode,
    } = useRequest(async () => {
        let { result, error } = await cnaRequest<{ key: string; img: string }>(
            "/api/v1/captcha",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    });

    const { run: login, loading: loggingIn } = useRequest(
        async (data) => {
            const cleanEmail = data.email.replace(/\s/g, '');
            const loginEmail = cleanEmail;
            //const loginEmail = data.email;
            const { result, error } = await cnaRequest<{
                token: string;
                profile_status: number;
                profile_id: number;
                register_setting: string[];
            }>("/api/v1/login", "POST", {
                email: cleanEmail,
                password: SHA256(data.password).toString(),
                captcha_code: data.captcha,
                captcha_key: captchaRes?.key,
            });

            if (error) {
                getCaptchaCode();
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;

                await window.localForage.setItem("cna-token", data.token);

                // 初始化用户数据（通知列表等）
                await initUserData();

                // 判断用户状态 data.profile_status 1 => 待付款，2 => 待审核
                // 如果待付款就跳到 register, step-v3 => payment-qr
                if (data.profile_status === 1) {
                    setStepV3("payment-qr");
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果待审核，先判断 register_setting 是否有 qualification 或者 business_case,
                // 有则跳转register，step-v3 => qualification or business_case
                if (data.profile_status === 2) {
                    if (data.register_setting.includes("qualification")) {
                        setStepV3("qualification");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    if (data.register_setting.includes("business_case")) {
                        setStepV3("business_case");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    // 检查面试状态
                    const { result: interviewResult, error: interviewError } =
                        await cnaRequest("/api/v1/interview/status", "GET");

                    if (interviewError) {
                        console.error(
                            "Failed to fetch interview status:",
                            interviewError
                        );
                        setStepV3("interview-status");
                        setInterviewStatus("pending");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    const status =
                        interviewResult?.data?.status ??
                        INTERVIEW_STATUS.PENDING;
                    const statusText =
                        INTERVIEW_STATUS_TEXT[status] ?? "pending";

                    // 面试成功，并且已经展示过成功状态的，跳到 wizard
                    const hasShownInterviewSuccess =
                        await window.localForage.getItem(
                            `hasShownInterviewSuccess_${data.profile_id}`
                        );
                    if (
                        status === INTERVIEW_STATUS.SUCCESS &&
                        hasShownInterviewSuccess
                    ) {
                        navigate("/account/wizard", { replace: true });
                        return;
                    }

                    setStepV3("interview-status");
                    setInterviewStatus(statusText);
                    navigate("/account/register", { replace: true });
                    return;
                }

                // 如果不需要面试，检查 register_setting, 不为空则调到wizard, 为空则跳到 welcome-video
                if (data.register_setting?.length > 0) {
                    await window.localForage.setItem(
                        "register-setting",
                        data.register_setting
                    );
                    setRegisterSetting(data.register_setting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                } else {
                    await window.localForage.setItem(
                        `hasShownInterviewSuccess_${data.profile_id}`,
                        true
                    );
                    if (loginEmail == "<EMAIL>") {
                        navigate("/member/index", { replace: true });
                    } else {
                        navigate("/account/welcome-video", { replace: true });
                    }
                }
            }
        },
        {
            manual: true,
        }
    );

    return (
        <form onSubmit={handleSubmit(login)}>
            <Controller
                name="email"
                control={control}
                render={({ field }) => (
                    <TextInput
                        autoComplete="username"
                        label={t("login.label.email", lang)}
                        placeholder={t("login.placeholder.email", lang)}
                        className="tw-mb-4"
                        withAsterisk
                        {...field}
                        onChange={(e) => {
                            // 去除所有空格
                            const value = e.target.value.replace(/\s/g, '');
                            field.onChange(value);
                        }}
                        onBlur={(e) => {
                            // 去除首尾空格
                            const value = e.target.value.trim();
                            field.onChange(value);
                        }}
                        error={errors.email && t(errors.email.message, lang)}
                    />
                )}
            />

            <Controller
                name="password"
                control={control}
                render={({ field }) => (
                    <PasswordInput
                        autoComplete="current-password"
                        label={t("login.label.password", lang)}
                        placeholder={t("login.placeholder.password", lang)}
                        className="tw-mb-4"
                        withAsterisk
                        {...field}
                        error={errors.password ? true : false}
                    />
                )}
            />

            <Controller
                name="captcha"
                control={control}
                render={({ field, fieldState }) => (
                    <TextInput
                        classNames={{
                            input: "tw-pl-[120px]",
                        }}
                        label={t("login.label.captcha", lang)}
                        placeholder={t("login.placeholder.captcha", lang)}
                        className="tw-mb-5"
                        leftSectionWidth={110}
                        leftSectionProps={{
                            className: "tw-justify-start",
                        }}
                        leftSection={
                            captchaRes && (
                                <img
                                    src={captchaRes.img}
                                    className="tw-block"
                                    alt="reCaptcha"
                                />
                            )
                        }
                        rightSection={
                            <CnaButton
                                variant="transparent"
                                color={"dark.3"}
                                onClick={getCaptchaCode}
                                className="tw-font-normal tw-px-2"
                                disabled={gettingCode}
                            >
                                <ArrowClockwise
                                    className={
                                        gettingCode ? "tw-animate-spin" : ""
                                    }
                                    size={24}
                                />
                            </CnaButton>
                        }
                        withAsterisk
                        {...field}
                        onChange={(e) => {
                            field.onChange(e.target.value.toUpperCase());
                        }}
                        error={fieldState.error ? true : false}
                    />
                )}
            />

            <div className="tw-flex tw-my-3 tw-space-x-3">
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="outline"
                        color="basic"
                        component={Link}
                        // to="/account/register"
                        to="/account/welcome"
                    >
                        {t("form.sign.up", lang)}
                    </CnaButton>
                </div>
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="filled"
                        color="basic"
                        loading={loggingIn}
                        type="submit"
                    >
                        {t("login.btn.login", lang)}
                    </CnaButton>
                </div>
            </div>
        </form>
    );
};

export default EmailLoginForm;
