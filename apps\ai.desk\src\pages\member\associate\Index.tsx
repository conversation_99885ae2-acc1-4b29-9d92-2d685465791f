import PageHeader from "@/components/member/PageHeader";
import { Stack } from "@mantine/core";
import List from "./components/List";
import OverduePay from "./components/OverduePay";
import useProfileStore from "@/store/profile";
import { useEffect, useState } from "react";

const Associates = () => {
    const profileInfo = useProfileStore();
    const [showOverduePay, setShowOverduePay] = useState<boolean | null>(null);

    useEffect(() => {
        if (profileInfo.role_level !== undefined && profileInfo.manager_prower_start_time !== undefined) {
            if (profileInfo.role_level === 0 && profileInfo.manager_prower_start_time) {
                // 显示过期支付
                setShowOverduePay(true);
            } else {
                // 显示合伙人列表
                setShowOverduePay(false);
            }
        }
    }, [profileInfo.manager_time]);

    if (showOverduePay === null) {
        return null;
    }

    return (
        <Stack>
            <PageHeader
                title="管理合伙人"
                subTitle="本栏目用于管理您的合伙人，您可以邀请合伙人并完成面试，同时了解每个合伙人完成业务后您可以获取的津贴分成。"
            />
            {showOverduePay ? <OverduePay /> : <List />}
        </Stack>
    );
};

export default Associates;
