import { <PERSON>na<PERSON>utton } from "@code.8cent/react/components";
import { Box, Checkbox, Group, LoadingOverlay, Stack } from "@mantine/core";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useEffect, useState, createContext, useContext } from "react";
import {
    SignatureData,
    useSignatureModal,
} from "@code.8cent/react/SignatureModal";
import { useEventBus } from "@/utils/eventBus";
import useWizardStore from "@code.8cent/store/wizard";
import { getFileByUrl, PDFViewer } from "@code.8cent/react/FileViewer";
import { useCountDown } from "@code.8cent/react/hooks";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import React from "react";
import { DownloadSimple } from "@phosphor-icons/react";
import useSettingStore from "@code.8cent/store/setting";

// 检测是否为移动端浏览器
const isMobileBrowser = () => {
    const ua = navigator.userAgent.toLowerCase();
    return /mobile|android|iphone|ipad|phone/i.test(ua);
};

type TContractSignCtx = {
    getContractFile: (url: string) => void | null;
    setGetContractFileFn: (fn: Function) => void;
    downloadContract: () => void;
    setDownloadContractFn: (fn: Function) => void;
    contractUrl: string | null;
};

const initialContextValue: TContractSignCtx = {
    getContractFile: null,
    setGetContractFileFn: (fn) => {},
    downloadContract: () => {},
    setDownloadContractFn: (fn) => {},
    contractUrl: null,
};

const SignContractContext =
    createContext<TContractSignCtx>(initialContextValue);

const ContractFile = React.memo(() => {
    const { setGetContractFileFn, setDownloadContractFn, contractUrl } =
        useContext(SignContractContext);

    const { isIOS, isWechat, isSafari } = useSettingStore();

    const {
        run: getContractFile,
        loading: fetching,
        data: contractFile = null,
        mutate,
    } = useRequest(
        async (url: string) => {
            try {
                mutate(null);

                let __file = await getFileByUrl(url);

                if (!__file) {
                    noty.error("获取文件失败");
                }

                return __file;
            } catch (err) {
                console.log(err);
            }
        },
        {
            manual: true,
        }
    );

    const { run: downloadContract } = useRequest(
        async () => {
            try {
                // 如果有直接的文件URL，优先使用直接下载
                if (contractUrl) {
                    // 检测环境
                    const isWechatClient = isWechat;
                    const isMobile = isMobileBrowser();
                    const isSafariBrowser = isSafari;

                    // 微信客户端的特殊处理
                    if (isWechatClient) {
                        // 微信浏览器直接使用 window.open 打开文件URL
                        window.open(contractUrl, "_blank");
                        return;
                    }

                    // 移动端Safari或iOS的特殊处理
                    if ((isMobile && isSafariBrowser) || isIOS === true) {
                        window.location.href = contractUrl;
                        return;
                    }

                    // 其他移动端浏览器
                    if (isMobile) {
                        const newWindow = window.open(contractUrl, "_blank");
                        if (!newWindow) {
                            // 如果新窗口被阻止，回退到直接跳转
                            window.location.href = contractUrl;
                        }
                        return;
                    }

                    // 桌面端浏览器，使用传统的下载方式
                    const link = document.createElement("a");
                    link.href = contractUrl;
                    link.download = `全球合伙人大联盟中国区域合伙人加盟合同.pdf`;
                    link.target = "_blank"; // 在新窗口打开，避免页面跳转
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    return;
                }

                // 如果没有直接URL，回退到原来的Blob方式（兼容性处理）
                if (contractFile instanceof File === true) {
                    // 检测环境
                    const isWechatClient = isWechat;
                    const isMobile = isMobileBrowser();
                    const isSafariBrowser = isSafari;

                    // 微信客户端的特殊处理
                    if (isWechatClient) {
                        // 微信浏览器不支持 blob URL 下载，需要特殊处理
                        try {
                            // 方法1: 尝试使用 window.open
                            const blob = new Blob([contractFile], {
                                type: "application/pdf",
                            });
                            const url = window.URL.createObjectURL(blob);

                            const newWindow = window.open(url, "_blank");

                            setTimeout(() => {
                                window.URL.revokeObjectURL(url);
                            }, 3000);

                            if (!newWindow) {
                                // 如果新窗口被阻止，尝试方法2
                                throw new Error("window.open 被阻止");
                            }
                        } catch (openError) {
                            console.log(
                                "window.open 失败，尝试其他方法:",
                                openError
                            );

                            // 方法2: 尝试使用 location.href
                            try {
                                const blob = new Blob([contractFile], {
                                    type: "application/pdf",
                                });
                                const url = window.URL.createObjectURL(blob);

                                // 显示用户提示
                                noty.error(
                                    "微信浏览器限制下载，请点击右上角菜单选择'在浏览器中打开'"
                                );

                                // 延迟跳转，给用户时间看到提示
                                setTimeout(() => {
                                    window.location.href = url;
                                    setTimeout(() => {
                                        window.URL.revokeObjectURL(url);
                                    }, 2000);
                                }, 2000);
                            } catch (hrefError) {
                                console.log("location.href 失败:", hrefError);
                                noty.error(
                                    "微信浏览器限制下载，请点击右上角菜单选择'在浏览器中打开'"
                                );
                            }
                        }
                        return;
                    }

                    // 移动端Safari或iOS的特殊处理
                    if ((isMobile && isSafariBrowser) || isIOS === true) {
                        const url = window.URL.createObjectURL(contractFile);
                        window.location.href = url;
                        // 延迟释放URL，确保跳转完成
                        setTimeout(() => {
                            window.URL.revokeObjectURL(url);
                        }, 2000);
                        return;
                    }

                    // 其他移动端浏览器
                    if (isMobile) {
                        const url = window.URL.createObjectURL(contractFile);
                        const newWindow = window.open(url, "_blank");
                        if (!newWindow) {
                            // 如果新窗口被阻止，回退到直接跳转
                            window.location.href = url;
                        }
                        // 延迟释放URL
                        setTimeout(() => {
                            window.URL.revokeObjectURL(url);
                        }, 2000);
                        return;
                    }

                    // 桌面端浏览器，使用传统的下载方式
                    const url = window.URL.createObjectURL(contractFile);
                    const link = document.createElement("a");
                    link.href = url;
                    link.download = `全球合伙人大联盟中国区域合伙人加盟合同.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }
            } catch (err) {
                console.log(err);
                noty.error("下载失败，请重试");
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (typeof getContractFile === "function") {
            setGetContractFileFn(() => getContractFile);
        }
    }, [getContractFile]);

    useEffect(() => {
        if (typeof downloadContract === "function") {
            setDownloadContractFn(() => downloadContract);
        }
    }, [downloadContract]);

    return (
        <Box className="tw-h-[400px] tw-max-h-[60vh] tw-relative">
            {fetching ? <LoadingOverlay /> : null}
            {contractFile?.type === "application/pdf" ? (
                <PDFViewer file={contractFile} />
            ) : null}
        </Box>
    );
});

const ContractSignPanel = ({
    disabled,
    setContractUrl,
}: {
    disabled?: boolean;
    setContractUrl: (url: string | null) => void;
}) => {
    const { getContractFile, downloadContract, contractUrl } =
        useContext(SignContractContext);

    const { open } = useSignatureModal();

    const { start, totalSeconds } = useCountDown();

    const { state, setState } = useWizardStore();

    const [signData, setSignData] = useState<SignatureData>([]);

    const [signed, setSigned] = useState(false);

    const [checked, setChecked] = useState(false);

    const [agreementChecked, setAgreementChecked] = useState(false);

    const bus = useEventBus();

    const { run: submitSign } = useRequest(
        async (signature: string) => {
            const { result, error } = await cnaRequest(
                "/api/v1/profileContract/create",
                "POST",
                {
                    signature,
                    type: 3,
                }
            );

            if (error) {
                noty.error(error.message);
            } else {
                setSigned(true);
                noty.success("签署成功");
                setContractUrl(result.data.file_full_url);
                getContractFile(
                    `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data.token}`
                );
                bus.emit("wizard.submit.disabled", false);
            }
        },
        { manual: true }
    );

    const toNext = () => {
        setState(state + 1);
    };

    const { run: getPDFTemplate } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<string>(
                "/api/v1/profileContract/generateContractTemplateToken",
                "POST",
                {
                    type: 3,
                }
            );

            bus.emit("wizard.submit.disabled", true);

            if (!error) {
                getContractFile(
                    `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data}`
                );
            }
        },
        {
            manual: false,
            ready: typeof getContractFile === "function",
        }
    );

    useMount(() => {
        start(15);

        bus.emit("wizard.submit.disabled", true);

        bus.on("wizard.submit.click", toNext);
    });

    useUnmount(() => {
        bus.off("wizard.submit.click", toNext);
    });

    const openSignatureModal = useMemoizedFn(() => {
        open({
            onConfirm: async ({ data, svg, base64 }) => {
                setSignData(data);
                submitSign(svg);
            },
            signatureData: signData,
        });
    });

    return (
        <Stack>
            <Checkbox
                label="我已阅读并同意签署《全球合伙人大联盟中国区域合伙人加盟合同》"
                checked={checked}
                onChange={(event) => {
                    // 只有在未签署时才允许改变复选框状态
                    if (!signed) {
                        setChecked(event.currentTarget.checked);
                    }
                }}
                disabled={signed} // 签署后禁用复选框
            />
            <Checkbox
                label="上述填写的本人姓名、身份证号码、签名均正确无误。本人也将如实填写后续 C&A 中国所需的其他信息。若因个人提供的信息错误而导致的一切损失由本人承担。"
                checked={agreementChecked}
                onChange={(event) => {
                    // 只有在未签署时才允许改变复选框状态
                    if (!signed) {
                        setAgreementChecked(event.currentTarget.checked);
                    }
                }}
                disabled={signed} // 签署后禁用复选框
            />
            <Group justify="center">
                {signed === true ? (
                    <CnaButton
                        leftSection={<DownloadSimple />}
                        onClick={downloadContract}
                    >
                        下载已签署合同
                    </CnaButton>
                ) : (
                    // <CnaButton disabled>已签署合同</CnaButton>
                    <CnaButton
                        onClick={openSignatureModal}
                        disabled={
                            totalSeconds > 0 ||
                            checked !== true ||
                            agreementChecked !== true
                        }
                    >
                        确认以上合同内容，并签名{" "}
                        {totalSeconds > 0 ? `(${totalSeconds})` : ""}
                    </CnaButton>
                )}
            </Group>
        </Stack>
    );
};

const ContractSign = () => {
    const [getContractFile, setGetContractFileFn] = useState(null);

    const [downloadContract, setDownloadContractFn] = useState(null);

    const [contractUrl, setContractUrl] = useState<string | null>(null);

    return (
        <SignContractContext.Provider
            value={{
                getContractFile,
                setGetContractFileFn,
                downloadContract,
                setDownloadContractFn,
                contractUrl,
            }}
        >
            <Stack>
                <ContractFile />
                <ContractSignPanel setContractUrl={setContractUrl} />
            </Stack>
        </SignContractContext.Provider>
    );
};

export default ContractSign;
