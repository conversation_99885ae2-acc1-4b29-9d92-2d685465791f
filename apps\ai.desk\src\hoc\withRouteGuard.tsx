import { useLocation, useNavigate } from "react-router-dom";
import { useLockFn, useMount } from "ahooks";
import { ComponentType, FC, useEffect } from "react";
import useWizardStore from "@code.8cent/store/wizard";
import { cnaRequest } from "@code.8cent/utils";
import useUserStore from "@/store/user";
import api from "@/apis";

// 判断当前路径是否匹配 PUBLIC_ROUTES 中的路径（支持动态参数）
const isPublicRoute = (pathname: string, PublicRoutes: string[]) => {
    let isPublic = false;

    PublicRoutes.forEach((route) => {
        const isExcluded = route.startsWith("!");
        const normalizedRoute = isExcluded ? route.slice(1) : route;

        const routeSegments = normalizedRoute.split("/");
        const pathSegments = pathname.split("/");

        // 路径长度不一致时，跳过该规则
        if (routeSegments.length !== pathSegments.length) {
            return;
        }

        // 逐段检查路径是否匹配
        const match = routeSegments.every(
            (segment, i) => segment === "*" || segment === pathSegments[i]
        );

        if (match) {
            // 如果匹配到以 ! 开头的路径，则说明这是非公共路由
            isPublic = !isExcluded;
        }
    });

    return isPublic;
};

// 面试状态常量
export const INTERVIEW_STATUS = {
    PENDING: 0,
    REVIEWING: 1,
    SUCCESS: 2,
    FAILED: 3,
} as const;

export const INTERVIEW_STATUS_TEXT = {
    [INTERVIEW_STATUS.PENDING]: "pending",
    [INTERVIEW_STATUS.REVIEWING]: "reviewing",
    [INTERVIEW_STATUS.SUCCESS]: "success",
    [INTERVIEW_STATUS.FAILED]: "failed",
} as const;

export type InterviewStatus =
    (typeof INTERVIEW_STATUS)[keyof typeof INTERVIEW_STATUS];
export type InterviewStatusText =
    (typeof INTERVIEW_STATUS_TEXT)[InterviewStatus];

interface InterviewStatusResponse {
    shouldRedirect: boolean;
    status_text: InterviewStatusText;
    redirectPath: string;
}

// Register V3 面试状态检查函数
const checkInterviewStatus = async (
    setInterviewStatus: (status: InterviewStatusText) => void
): Promise<InterviewStatusResponse> => {
    try {
        const { result: interviewStatusResult, error: interviewStatusError } =
            await cnaRequest("/api/v1/interview/status", "GET");

        if (interviewStatusError) {
            console.error(
                "Failed to fetch interview status:",
                interviewStatusError
            );
            return {
                shouldRedirect: false,
                status_text: "pending",
                redirectPath: "/account/register",
            };
        }

        const status =
            interviewStatusResult?.data?.status ?? INTERVIEW_STATUS.PENDING;
        const statusText = INTERVIEW_STATUS_TEXT[status] ?? "pending";

        setInterviewStatus(statusText);

        return {
            // 没有看过成功状态的，都需要跳转
            shouldRedirect: !(await window.localForage.getItem(
                `hasShownInterviewSuccess_${interviewStatusResult?.data?.user_id}`
            )),
            status_text: statusText,
            redirectPath: "/account/register",
        };
    } catch (error) {
        console.error("Unexpected error in checkInterviewStatus:", error);
        return {
            shouldRedirect: false,
            status_text: "pending",
            redirectPath: "/account/register",
        };
    }
};

type RouteGuardOptions = {
    publicRoutes?: string[];
};

// 处理注册设置和面试状态检查
const handleRegistrationAndInterview = async (
    registerSetting: string[] | null,
    setStepV3: (step: string) => void,
    setInterviewStatus: (status: InterviewStatusText) => void,
    setRegisterSetting: (settings: string[]) => void,
    setWizardState: (state: number) => void,
    navigate: (path: string, options?: { replace: boolean }) => void
) => {
    if (registerSetting?.length) {
        if (registerSetting.includes("qualification")) {
            setStepV3("qualification");
            navigate("/account/register", { replace: true });
            return true;
        }
        if (registerSetting.includes("business_case")) {
            setStepV3("business_case");
            navigate("/account/register", { replace: true });
            return true;
        }

        const interviewResult = await checkInterviewStatus(setInterviewStatus);
        if (interviewResult.shouldRedirect) {
            setInterviewStatus(interviewResult.status_text);
            setStepV3("interview-status");
            navigate(interviewResult.redirectPath, { replace: true });
            return true;
        }

        setRegisterSetting(registerSetting);
        setWizardState(0);
        navigate("/account/wizard", { replace: true });
        return true;
    }

    const interviewResult = await checkInterviewStatus(setInterviewStatus);
    if (interviewResult.shouldRedirect) {
        setInterviewStatus(interviewResult.status_text);
        setStepV3("interview-status");
        navigate(interviewResult.redirectPath, { replace: true });
        return true;
    }

    return false;
};

// 定义 HOC 函数
const withRouteGuard = <P extends object>(
    Component: ComponentType<P>,
    options: RouteGuardOptions = {}
): FC<P> => {
    const WrappedComponent: FC<P> = (props) => {
        const navigate = useNavigate();
        const { pathname } = useLocation();
        const { setRegisterSetting, setState: setWizardState } =
            useWizardStore();
        const {
            token,
            profile,
            setStepV3,
            setInterviewStatus,
            setProfile,
            isLoggedIn
        } = useUserStore();

        const checkAuth = useLockFn(async () => {
            // 使用用户store中的数据，如果store中没有则从本地存储获取
            const currentToken = token || (await window.localForage.getItem("cna-token") as string);
            const payToken = (await window.localForage.getItem(
                "pay-token"
            )) as string;
            const isNew = await window.localForage.getItem("is_new");
            const baseInfo = await window.localForage.getItem("base_info");

            const registerSetting = currentToken
                ? ((await window.localForage.getItem(
                      "register-setting"
                  )) as string[])
                : null;

            // 处理根路径
            if (pathname === "/") {
                if (currentToken) {
                    // 如果有 is_new，显示基础填写页面
                    if (isNew) {
                        setStepV3("basic-info");
                        navigate("/account/register", { replace: true });
                        return;
                    }

                    if (baseInfo === 2) {
                        await window.localForage.removeItem("base_info");
                        navigate("/account/fields", { replace: true });
                        return;
                    }

                    // 如果没有支付，显示支付页面
                    if (payToken) {
                        // 更新用户资料中的pay_token
                        if (profile) {
                            setProfile({ pay_token: payToken });
                        }

                        // 创建进程
                        const processRes = await api.exam.addProcess(
                            "profile_register",
                            payToken
                        );

                        // 保存业务进程编号
                        await window.localForage.setItem(
                            "register_payment_process_no",
                            processRes.data?.process_order_no
                        );
                        setStepV3("payment-qr");
                        return;
                    }

                    const handled = await handleRegistrationAndInterview(
                        registerSetting,
                        setStepV3,
                        setInterviewStatus,
                        setRegisterSetting,
                        setWizardState,
                        navigate
                    );
                    if (!handled) {
                        const menu = await api.exam.getUpgradeMenu();
                        if (menu.status === 1) {
                            navigate("/member/index", { replace: true });
                        } else {
                            navigate("/member/exam", { replace: true });
                        }
                    }
                } else {
                    navigate("/account/login", { replace: true });
                }
                return;
            }

            // 处理注册页面
            if (pathname === "/account/register") {
                if (currentToken) {
                    // 如果有 is_new，显示基础填写页面
                    if (isNew || baseInfo === 2) {
                        setStepV3("basic-info");
                        return;
                    }

                    // 如果没有支付，显示支付页面
                    if (payToken) {
                        // 更新用户资料中的pay_token
                        if (profile) {
                            setProfile({ pay_token: payToken });
                        }

                        // 创建进程
                        const processRes = await api.exam.addProcess(
                            "profile_register",
                            payToken
                        );

                        // 保存业务进程编号
                        await window.localForage.setItem(
                            "register_payment_process_no",
                            processRes.data?.process_order_no
                        );
                        setStepV3("payment-qr");
                        return;
                    }

                    // 其他情况按原有逻辑处理
                    const handled = await handleRegistrationAndInterview(
                        registerSetting,
                        setStepV3,
                        setInterviewStatus,
                        setRegisterSetting,
                        setWizardState,
                        navigate
                    );
                    if (!handled) {
                        const menu = await api.exam.getUpgradeMenu();
                        if (menu.status === 1) {
                            navigate("/member/index", { replace: true });
                        } else {
                            navigate("/member/exam", { replace: true });
                        }
                    }
                    return;
                } else {
                    // 没有token时，检查是否有is_new，如果有则显示基础填写页面
                    if (isNew) {
                        setStepV3("basic-info");
                        return;
                    }
                    // 没有token且没有is_new，允许正常访问注册页面
                    return;
                }
            }

            // 处理其他受保护路由
            if (!isPublicRoute(pathname, options?.publicRoutes ?? [])) {
                if (!token) {
                    navigate("/account/login", { replace: true });
                    return;
                }

                // 绿智地球 不处理
                if (
                    pathname === "/company/apply" ||
                    pathname === "/company/dynamicTable" ||
                    pathname === "/company/financialTable" ||
                    pathname === "/company/staticTable"
                ) {
                    return;
                }

                if (pathname !== "/account/wizard") {
                    const handled = await handleRegistrationAndInterview(
                        registerSetting,
                        setStepV3,
                        setInterviewStatus,
                        setRegisterSetting,
                        setWizardState,
                        navigate
                    );
                    if (handled) {
                        return;
                    }
                }
            }
        });

        useEffect(() => {
            checkAuth();
        }, [pathname]);

        useMount(() => {
            checkAuth();
        });

        return <Component {...props} />;
    };

    WrappedComponent.displayName = `withRouteGuard(${
        Component.displayName || Component.name || "Component"
    })`;

    return WrappedComponent;
};

export default withRouteGuard;
