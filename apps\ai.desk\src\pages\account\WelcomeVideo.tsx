import { useEffect, useState, useRef, useMemo } from "react";
import { getVideoStyles } from "@code.8cent/utils";
import { useNavigate } from "react-router-dom";
import api from "@/apis";

// 检测是否为微信客户端
const isWeChat = () => {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes("micromessenger");
};

const WelcomeVideo = () => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isPlaying, setIsPlaying] = useState(true);
    const [isWeChatClient, setIsWeChatClient] = useState(false);
    const [showTips, setShowTips] = useState(true);
    const [countDown, setCountDown] = useState(5);
    const navigate = useNavigate();

    const { videoContainerStyle, videoStyle } = getVideoStyles();

    const videoSource = useMemo(() => {
        const width = window.innerWidth;
        const height = window.innerHeight;
        return width > height
            ? "/videos/welcome.mp4"
            : "/videos/welcome_mobile.mp4";
    }, []);

    useEffect(() => {
        // 检测微信客户端
        const weChat = isWeChat();
        setIsWeChatClient(weChat);

        const playVideo = async () => {
            try {
                if (videoRef.current) {
                    videoRef.current.muted = true;

                    // 微信客户端特殊处理
                    if (weChat) {
                        // 微信中需要用户交互才能播放视频
                        // 添加点击事件监听
                        const handleClick = async () => {
                            try {
                                await videoRef.current?.play();
                                document.removeEventListener(
                                    "click",
                                    handleClick
                                );
                                document.removeEventListener(
                                    "touchstart",
                                    handleClick
                                );
                                setShowTips(false);
                            } catch (error) {
                                console.error("微信中视频播放失败:", error);
                            }
                        };

                        document.addEventListener("click", handleClick);
                        document.addEventListener("touchstart", handleClick);

                        // 如果5秒后还没有播放，直接跳转
                        setTimeout(() => {
                            if (videoRef.current?.paused) {
                                handleVideoEnd();
                            }
                        }, 5000);
                    } else {
                        // 非微信客户端正常播放
                        await videoRef.current.play();
                    }
                }
            } catch (error) {
                console.error("视频自动播放失败:", error);
                // 播放失败时直接跳转
                setTimeout(() => {
                    handleVideoEnd();
                }, 1000);
            }
        };

        playVideo();
    }, []);

    // 倒计时
    useEffect(() => {
        if (countDown > 0 && showTips && isWeChatClient) {
            const timer = setInterval(() => {
                setCountDown((prev) => prev - 1);
            }, 1000);
            return () => clearInterval(timer);
        }
    }, [countDown, showTips, isWeChatClient]);

    const handleVideoEnd = async () => {
        setIsPlaying(false);
        // 查询是否要跳转到考试页面, 通过接口获取晋升流程菜单
        const menu = await api.exam.getUpgradeMenu();
        if (menu.status === 1) {
            navigate("/member/index", { replace: true });
        } else {
            navigate("/member/exam", { replace: true });
        }
    };

    if (!isPlaying) return null;

    return (
        <div style={videoContainerStyle}>
            {isWeChatClient && showTips && (
                <div
                    style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        zIndex: 10,
                        background: "rgba(0,0,0,0.7)",
                        color: "white",
                        padding: "20px",
                        borderRadius: "10px",
                        textAlign: "center",
                        fontSize: "16px",
                    }}
                >
                    <p>
                        点击屏幕任意位置开始播放视频（{countDown}
                        秒后自动进入首页）
                    </p>
                </div>
            )}
            <video
                ref={videoRef}
                style={videoStyle}
                src={videoSource}
                autoPlay={!isWeChatClient}
                muted
                playsInline
                webkit-playsinline="true"
                x5-playsinline="true"
                x5-video-player-type="h5"
                x5-video-player-fullscreen="false"
                onEnded={handleVideoEnd}
            />
        </div>
    );
};

export default WelcomeVideo;
