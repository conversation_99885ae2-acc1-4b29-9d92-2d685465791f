{"root": ["../../packages/shared-types/global.d.ts", "../../packages/shared-types/api.d.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/@types/global.d.ts", "./src/@types/api/bill.d.ts", "./src/@types/api/community.d.ts", "./src/@types/api/document.d.ts", "./src/@types/api/gernal.d.ts", "./src/@types/api/index.d.ts", "./src/@types/api/notification.d.ts", "./src/@types/api/project.d.ts", "./src/@types/api/register.d.ts", "./src/@types/api/schedule.d.ts", "./src/@types/api/team.d.ts", "./src/@types/api/user.d.ts", "./src/apis/aiassistant.ts", "./src/apis/apply.ts", "./src/apis/community.ts", "./src/apis/config.ts", "./src/apis/email.ts", "./src/apis/gernal.ts", "./src/apis/index.ts", "./src/apis/notoken.ts", "./src/apis/office.ts", "./src/apis/profile.ts", "./src/apis/project.ts", "./src/apis/register.ts", "./src/apis/schedule.ts", "./src/apis/team.ts", "./src/apis/user.ts", "./src/apis/wechat.ts", "./src/components/ai/mainchat.tsx", "./src/components/ai/index.tsx", "./src/components/ai/markdown.ts", "./src/components/ai/ailoading/loading.tsx", "./src/components/ai/animationcomponent/soundwave/index.tsx", "./src/components/apply/phoneloginform.tsx", "./src/components/apply/activate.tsx", "./src/components/apply/applyreportfrom.tsx", "./src/components/apply/component.tsx", "./src/components/apply/contract.tsx", "./src/components/apply/contracttable.tsx", "./src/components/apply/paymodel.tsx", "./src/components/apply/review.tsx", "./src/components/apply/reviewtable.tsx", "./src/components/apply/tablemodel.tsx", "./src/components/apply/uploadmodel.tsx", "./src/components/apply/waiting.tsx", "./src/components/apply/welcome.tsx", "./src/components/company/application/accountsetting.tsx", "./src/components/company/application/applicationcomplete.tsx", "./src/components/company/application/applicationstepper.tsx", "./src/components/company/application/documentsubmission.tsx", "./src/components/company/application/paymentsubmission.tsx", "./src/components/company/application/preliminarysubmission.tsx", "./src/components/digital-human/kickstart/guide.tsx", "./src/components/digital-human/kickstart/regulations.tsx", "./src/components/digital-human/kickstart/status.tsx", "./src/components/digital-human/kickstart/uploadmedia.tsx", "./src/components/digital-human/kickstart/index.tsx", "./src/components/digital-human/video/item.tsx", "./src/components/digital-human/video/list.tsx", "./src/components/forms/gsp/preliminary.tsx", "./src/components/forms/office/application.tsx", "./src/components/forms/office/applicationfile.tsx", "./src/components/layouts/dashboardlayout.tsx", "./src/components/layouts/dashboardlayout2.tsx", "./src/components/layouts/indexlayout.tsx", "./src/components/layouts/rootlayout.tsx", "./src/components/modals/confirmmodal.tsx", "./src/components/modals/filterfunctionmodal.tsx", "./src/components/modals/uploadmodal.tsx", "./src/components/modals/calendar/calendardetailmodal.tsx", "./src/components/modals/calendar/calendareventcreationmodal.tsx", "./src/components/modals/community/profileinfomodal.tsx", "./src/components/modals/community/profileinfov2modal.tsx", "./src/components/modals/profile/clientmodal.tsx", "./src/components/modals/profile/credentialmodal.tsx", "./src/components/modals/profile/detailmodal.tsx", "./src/components/modals/profile/profilevalidatemodal.tsx", "./src/components/modals/profile/skillmodal.tsx", "./src/components/modals/project/companyapplicationmodal.tsx", "./src/components/modals/project/companydetailmodal.tsx", "./src/components/modals/project/createcompanymodal.tsx", "./src/components/modals/project/createusermodal.tsx", "./src/components/modals/project/informationmodel.tsx", "./src/components/modals/register/infovalidatemodal.tsx", "./src/components/munu/navigation-menu.tsx", "./src/components/office/apply.tsx", "./src/components/office/examinerejected.tsx", "./src/components/office/invitemember.tsx", "./src/components/office/memberlist.tsx", "./src/components/office/officepanel.tsx", "./src/components/office/review.tsx", "./src/components/office/signcontract.tsx", "./src/components/profile/profileuserdata.tsx", "./src/components/project/contractsign.tsx", "./src/components/project/exam.tsx", "./src/components/project/list.tsx", "./src/components/register/emailphoneform.tsx", "./src/components/register/icbcpay.tsx", "./src/components/register/informationform.tsx", "./src/components/register/pay.tsx", "./src/components/register/payresult.tsx", "./src/components/register/validatestatussection.tsx", "./src/components/team/successtext.tsx", "./src/components/wizard/bankaccount.tsx", "./src/components/wizard/completesetup.tsx", "./src/components/wizard/contractsign.tsx", "./src/components/wizard/invitepartner.tsx", "./src/components/wizard/languagesetup.tsx", "./src/components/wizard/profilenric.tsx", "./src/components/wizard/securitysetup.tsx", "./src/components/wizard/usersetup.tsx", "./src/contexts/project.ts", "./src/hooks/project/usecompanyapplicationformenabled.ts", "./src/pages/account/documents.tsx", "./src/pages/account/occupationselect.tsx", "./src/pages/account/register.tsx", "./src/pages/account/welcome.tsx", "./src/pages/account/welcomev2.tsx", "./src/pages/account/welcomevideo.tsx", "./src/pages/account/registerpayment/index.tsx", "./src/pages/account/registerpayment/components/icbcqrpay.tsx", "./src/pages/account/registerpayment/components/wechatmppay.tsx", "./src/pages/account/registerpayment/components/wechatqrpay.tsx", "./src/pages/account/registerv2/filenamemappings.ts", "./src/pages/account/registerv2/index.tsx", "./src/pages/account/registerv2/types.ts", "./src/pages/account/registerv2/components/addressinfosection.tsx", "./src/pages/account/registerv2/components/addressselectsection.tsx", "./src/pages/account/registerv2/components/datepicker.tsx", "./src/pages/account/registerv2/components/phoneemailsection.tsx", "./src/pages/ai/card.tsx", "./src/pages/ai/carddetail.tsx", "./src/pages/ai/profile.tsx", "./src/pages/member/bank.tsx", "./src/pages/member/benefit.tsx", "./src/pages/member/billings.tsx", "./src/pages/member/community.tsx", "./src/pages/member/digitalhuman.tsx", "./src/pages/member/document.tsx", "./src/pages/member/editpersonal.tsx", "./src/pages/member/index.tsx", "./src/pages/member/indexv2.tsx", "./src/pages/member/join.tsx", "./src/pages/member/notification.tsx", "./src/pages/member/office.tsx", "./src/pages/member/plan.tsx", "./src/pages/member/profile.tsx", "./src/pages/member/profilev2.tsx", "./src/pages/member/project.tsx", "./src/pages/member/qualification.tsx", "./src/pages/member/resource.tsx", "./src/pages/member/schedule.tsx", "./src/pages/member/skill.tsx", "./src/pages/member/team.tsx", "./src/pages/member/ai-law/comparison/index.tsx", "./src/pages/member/ai-law/evaluation/index.tsx", "./src/pages/member/ai-law/generatecontract/steppersm.tsx", "./src/pages/member/ai-law/generatecontract/steps.tsx", "./src/pages/member/ai-law/generatecontract/index.tsx", "./src/pages/member/ai-law/management/index.tsx", "./src/pages/member/ai-law/optimization/index.tsx", "./src/pages/member/ai-law/simplification/index.tsx", "./src/pages/member/ai-law/translation/index.tsx", "./src/pages/member/ai-law/assets/font/fangsong-normal.ts", "./src/pages/member/ai-law/components/aisend.tsx", "./src/pages/member/ai-law/components/aimoblieenter.tsx", "./src/pages/member/ai-law/components/bubble.tsx", "./src/pages/member/ai-law/components/card.tsx", "./src/pages/member/ai-law/components/fileitem.tsx", "./src/pages/member/ai-law/components/header.tsx", "./src/pages/member/ai-law/components/rolechoose.tsx", "./src/pages/member/ai-law/components/texteditor.tsx", "./src/pages/member/ai-law/components/upload.tsx", "./src/pages/member/ai-law/components/diff-contract/contractbox.tsx", "./src/pages/member/ai-law/components/diff-contract/fileoutput.tsx", "./src/pages/member/ai-law/components/diff-contract/index.tsx", "./src/pages/member/ai-law/components/diff-contract/simhei-normal.ts", "./src/pages/member/ai-law/components/diff-contract/tips.tsx", "./src/pages/member/ai-law/components/translate-contract/save.tsx", "./src/pages/member/ai-law/components/translate-contract/fileoutput.tsx", "./src/pages/member/ai-law/components/translate-contract/index.tsx", "./src/pages/member/ai-law/components/translate-contract/songti-normal.ts", "./src/pages/member/ai-law/components/translate-contract/rich-text/handelmarkdown.ts", "./src/pages/member/ai-law/components/translate-contract/rich-text/rich-text.tsx", "./src/pages/project/ apply.tsx", "./src/pages/team/hub.tsx", "./src/pages/team/index.tsx", "./src/pages/team/join.tsx", "./src/pages/wizard/index.tsx", "./src/router/index.tsx", "./src/store/digital-human.ts", "./src/store/login.ts", "./src/store/modal.ts", "./src/store/office.ts", "./src/store/profile.ts", "./src/store/register.ts", "./src/utils/eventbus.tsx"], "version": "5.7.3"}