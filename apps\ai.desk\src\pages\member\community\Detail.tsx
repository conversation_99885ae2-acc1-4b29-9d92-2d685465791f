import api from "@/apis";
import { CnaButton } from "@code.8cent/react/components";
import {
    Paper,
    ScrollArea,
    Stack,
    Title,
    Text,
    Group,
    Image,
    Grid,
    Avatar,
    SimpleGrid,
    Modal,
} from "@mantine/core";
import {
    BookBookmark,
    Bookmark,
    Books,
    NotePencil,
    CaretDown,
    CaretUp,
    Robot,
} from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { useRequest } from "ahooks";
import useDataStore from "@code.8cent/store/data";
import { CertificateCard } from "@/components/modals/profile/CertificateCard";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import useSettingStore from "@code.8cent/store/setting";
import { cnaRequest } from "@code.8cent/utils";
import { useNavigate, useLocation } from "react-router-dom";
import noty from "@code.8cent/react/noty";

const CommunityDetail = () => {
    const { lang } = useSettingStore();
    const { openFileView } = useFileViewer();
    const navigate = useNavigate();
    const location = useLocation();

    const profileId = location.state?.profileId;

    const { countryDatas, industries, credentialTitles } = useDataStore();

    const [expandedItems, setExpandedItems] = useState<{
        [key: string]: boolean;
    }>({});

    const toggleExpand = (key: string) => {
        setExpandedItems((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    // 个人信息
    const [profileInfo, setProfileInfo] = useState<any>({});
    // 牌照信息
    const [workLicenseInfo, setWorkLicenseInfo] = useState<any>([]);
    // 资格证书
    const [certificateInfo, setCertificateInfo] = useState<any>([]);
    // 业务案例
    const [businessCaseInfo, setBusinessCaseInfo] = useState<any>([]);
    // AI工具使用经验
    const [aiToolInfo, setAiToolInfo] = useState<any>([]);
    // 放大牌照状态
    const [licenseModalVisible, setLicenseModalVisible] =
        useState<boolean>(false);
    const [licenseImage, setLicenseImage] = useState<string>("");
    const [licenseImageScale, setLicenseImageScale] = useState<number>(1);

    // 获取个人简介信息
    const { run: getProfileInfo, loading: getProfileInfoLoading } = useRequest(
        async () => {
            const res = await api.profile.getProfileInfo(profileId);
            console.log("获取个人简介信息：", res);
            if (res) {
                setProfileInfo(res.userInfo);
                setWorkLicenseInfo(
                    res.workLicense.filter(
                        (item: any) => item && item.trim() !== ""
                    )
                );
                setCertificateInfo(res.certificate);
                setBusinessCaseInfo(res.businessCase);
                setAiToolInfo(res.AICase);
            }
        },
        {
            manual: true,
        }
    );

    const { data: { certificates = [] } = {}, run: getExperienceList } =
        useRequest(
            async () => {
                const [certificates] = await Promise.all([
                    api.profile.getExperienceList(1, profileId),
                ]);

                return {
                    certificates,
                };
            },
            {
                manual: true,
            }
        );

    const { run: getToken } = useRequest(
        async (id) => {
            const res = await api.profile.generateDocToken(id);
            if (res) {
                openFileView(
                    `${window.api_base_url}/api/v1/experience/previewDoc/${res}`
                );
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (profileId) {
            getExperienceList();
            getProfileInfo();
        }
    }, [profileId]);

    // 语言列表
    const { data: languagesList = [] } = useRequest(async () => {
        let res = await cnaRequest<[]>("/api/v1/config/globalLanguages", "GET");

        if (res.result.code == 200) {
            return res.result.data;
        } else {
            return [];
        }
    });

    return (
        <Stack>
            <Title order={2}>合伙人详情</Title>
            <ScrollArea h="calc(100vh - 200px)">
                <Paper
                    p="lg"
                    radius="md"
                    mb="lg"
                >
                    <div className="tw-bg-[#060D3D] tw-text-white tw-h-[100px] tw-rounded-t-[0.5rem] -tw-m-[20px]"></div>

                    <Group
                        align="start"
                        className="-tw-mt-[80px]"
                    >
                        <Avatar
                            radius={10}
                            src={
                                profileInfo?.profileAvatar !==
                                "/storage/images/avatar/image-user.svg"
                                    ? `${window.api_base_url}${profileInfo.profileAvatar}`
                                    : "/images/station/default-avatar.svg"
                            }
                            className="tw-border-2 tw-size-[100px] md:tw-size-[160px] tw-border-[#F1F3F5]"
                            style={{
                                userSelect: "none",
                                WebkitUserSelect: "none",
                                MozUserSelect: "none",
                                msUserSelect: "none",
                                pointerEvents: "none",
                            }}
                        />

                        <Group
                            className="tw-flex-1"
                            justify="space-between"
                            align="center"
                        >
                            <Stack
                                gap="0"
                                className="tw-text-white"
                            >
                                <Text
                                    fw={600}
                                    className="tw-text-[20px]"
                                >
                                    {profileInfo?.profileName}
                                </Text>
                                <Text>{profileInfo?.profilePartnerCode}</Text>
                            </Stack>
                            <CnaButton
                                variant="outline"
                                color="white"
                                size="xs"
                                leftSection={<NotePencil size={16} />}
                                onClick={() =>
                                    navigate("/member/community", {
                                        replace: true,
                                    })
                                }
                            >
                                返回
                            </CnaButton>
                        </Group>
                    </Group>

                    <Stack
                        gap="xs"
                        className="md:tw-ml-[174px] md:-tw-mt-[60px] tw-ml-0 tw-mt-2"
                    >
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">性别：</Text>
                                <Text>
                                    {profileInfo?.profileGender === "M"
                                        ? "男"
                                        : "女"}
                                </Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">出生年月：</Text>
                                <Text>{profileInfo?.profileBirthDate}</Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">电话：</Text>
                                <Text>{profileInfo?.profileContact}</Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">邮箱：</Text>
                                <Text>{profileInfo?.profileEmail}</Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">语言：</Text>
                                <Text>
                                    {profileInfo?.settingLanguageAbility?.map(
                                        (item) => {
                                            return (
                                                languagesList.find(
                                                    (language) =>
                                                        language.code === item
                                                )?.[`name${lang}`] + " "
                                            );
                                        }
                                    )}
                                </Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">国籍：</Text>
                                <Text>
                                    {countryDatas.find(
                                        (item) =>
                                            item.countryID ===
                                            profileInfo?.profileNationalityID
                                    )?.countryZH || "-"}
                                </Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">当前职位：</Text>
                                <Text>{profileInfo?.position}</Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">从业年限：</Text>
                                <Text>{profileInfo?.work_year}</Text>
                            </Group>
                        </SimpleGrid>
                        <Group>
                            <Group gap="0">
                                <Text c="dimmed">最高学历：</Text>
                                <Text>
                                    {`${profileInfo?.edu} - ${profileInfo?.eduSubject}`}
                                </Text>
                            </Group>
                            <Text c="dimmed">
                                {(() => {
                                    if (!profileInfo?.eduDatetime) {
                                        return "XXXX-XX ~ XXXX-XX";
                                    }

                                    // 格式化月份时间段显示
                                    const formatMonthRange = (
                                        dateRange: string
                                    ) => {
                                        if (
                                            !dateRange ||
                                            !dateRange.includes("-")
                                        ) {
                                            return "XXXX-XX ~ XXXX-XX";
                                        }

                                        const [startDate, endDate] =
                                            dateRange.split("-");

                                        // 将 YYYY.MM 格式转换为 YYYY年MM月 格式
                                        const formatToChinese = (
                                            dateStr: string
                                        ) => {
                                            if (
                                                !dateStr ||
                                                dateStr.length !== 7
                                            ) {
                                                return "XXXX-XX";
                                            }
                                            const [year, month] =
                                                dateStr.split(".");
                                            if (!year || !month) {
                                                return "XXXX-XX";
                                            }
                                            return `${year}-${month}`;
                                        };

                                        return `${formatToChinese(
                                            startDate
                                        )} ~ ${formatToChinese(endDate)}`;
                                    };

                                    return formatMonthRange(
                                        profileInfo?.eduDatetime
                                    );
                                })()}
                            </Text>
                        </Group>
                        <Group
                            align="flex-start"
                            gap="0"
                        >
                            <Text
                                c="dimmed"
                                w={86}
                                style={{ flexShrink: 0 }}
                            >
                                个人介绍：
                            </Text>
                            <Text style={{ flex: 1, wordBreak: "break-all" }}>
                                {profileInfo?.profileDesc}
                            </Text>
                        </Group>
                    </Stack>
                </Paper>
                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group>
                        <Bookmark size={20} />
                        <Text fw={500}>获得牌照</Text>
                    </Group>
                    <Grid
                        mt="lg"
                        gutter="lg"
                    >
                        {workLicenseInfo?.map((item: any, index: any) => (
                            <Grid.Col
                                span={{ base: 12, md: 4 }}
                                key={index}
                            >
                                <Image
                                    src={`https://doc.corporate-advisory.cn/${item}`}
                                    style={{ cursor: "pointer" }}
                                    draggable={false}
                                    onClick={() => {
                                        // setLicenseImage(
                                        //     `https://doc.corporate-advisory.cn/${item}`
                                        // );
                                        // setLicenseModalVisible(true);
                                        openFileView(
                                            `${window.api_base_url}/api/v1/file/resource?path=${item}&type=remote`
                                        );
                                    }}
                                />
                            </Grid.Col>
                        ))}
                    </Grid>
                    {workLicenseInfo?.length === 0 && (
                        <Text
                            c="dimmed"
                            className="tw-text-center"
                        >
                            暂未获得牌照
                        </Text>
                    )}
                    {/* 放大牌照 */}
                    {/* <Modal
                        opened={licenseModalVisible}
                        onClose={() => setLicenseModalVisible(false)}
                        fullScreen
                        centered
                    >
                        <Image
                            src={licenseImage}
                            style={{
                                maxWidth: "100%",
                                maxHeight: "100%",
                                transform: `scale(${licenseImageScale})`,
                                transition: "transform 0.2s",
                            }}
                            onWheel={(e) => {
                                e.preventDefault();
                                if (e.deltaY < 0)
                                    setLicenseImageScale((s) =>
                                        Math.min(5, s + 0.1)
                                    );
                                else
                                    setLicenseImageScale((s) =>
                                        Math.max(0.2, s - 0.1)
                                    );
                            }}
                        />
                    </Modal> */}
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group mb="md">
                        <BookBookmark size={20} />
                        <Text fw={500}>资格证书</Text>
                    </Group>

                    <SimpleGrid cols={{ base: 1, md: 1, lg: 2, xl: 3 }}>
                        {certificates?.map((certificate, index) => (
                            <CertificateCard
                                key={index}
                                title={
                                    credentialTitles?.find(
                                        (item) =>
                                            item.id ===
                                            certificate.experience_id
                                    )?.["title_" + lang.toLowerCase()]
                                }
                                date={certificate.time_range}
                                region={
                                    countryDatas?.find(
                                        (item) =>
                                            item.countryID ===
                                            certificate.country_id
                                    )?.["country" + lang]
                                }
                                fields={
                                    industries?.find(
                                        (item) =>
                                            item.id === certificate.industry_id
                                    )?.["title_" + lang.toLowerCase()]
                                }
                                isFileView={async () => {
                                    if (certificate.file) {
                                        getToken(certificate.id);
                                    } else {
                                        noty.error("暂无上传证书文件");
                                    }
                                }}
                                readonly
                            />
                        ))}
                    </SimpleGrid>
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group
                        mb="md"
                        justify="space-between"
                    >
                        <Group>
                            <Books size={20} />
                            <Text fw={500}>业务案例</Text>
                        </Group>
                    </Group>
                    <Stack gap="md">
                        {businessCaseInfo?.map((item1: any, index1: any) => (
                            <Paper
                                p="lg"
                                radius="md"
                                className="tw-bg-[#F1F3F5]"
                                key={index1}
                            >
                                <Group justify="space-between">
                                    <Text
                                        c="#060D3D"
                                        fw={600}
                                    >
                                        {item1.name}
                                    </Text>
                                </Group>
                                <Text
                                    c="dimmed"
                                    mb="md"
                                >
                                    {item1.date}
                                </Text>
                                <Stack gap="xs">
                                    <Group gap="0">
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            项目地点：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                            }}
                                        >
                                            {item1.address}
                                        </Text>
                                    </Group>
                                    <Group gap="0">
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            行业领域：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                            }}
                                        >
                                            家电制造、资源管理、生产管理、经济管理、制造管理、资源管理、生产管理、经济管理、制造管理
                                        </Text>
                                    </Group>
                                    <Group
                                        align="flex-start"
                                        gap="0"
                                    >
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            服务内容：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                                display: "-webkit-box",
                                                WebkitLineClamp: expandedItems[
                                                    `service${index1}`
                                                ]
                                                    ? undefined
                                                    : 1,
                                                WebkitBoxOrient: "vertical",
                                                overflow: "hidden",
                                            }}
                                        >
                                            {item1.content}
                                        </Text>
                                        <CnaButton
                                            variant="transparent"
                                            color="#19288F"
                                            size="xs"
                                            onClick={() =>
                                                toggleExpand(`service${index1}`)
                                            }
                                            leftSection={
                                                expandedItems[
                                                    `service${index1}`
                                                ] ? (
                                                    <CaretUp size={16} />
                                                ) : (
                                                    <CaretDown size={16} />
                                                )
                                            }
                                        >
                                            {expandedItems[`service${index1}`]
                                                ? "收起"
                                                : "展开"}
                                        </CnaButton>
                                    </Group>
                                    <Group
                                        align="flex-start"
                                        gap="0"
                                    >
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            成果简述：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                                display: "-webkit-box",
                                                WebkitLineClamp: expandedItems[
                                                    `result${index1}`
                                                ]
                                                    ? undefined
                                                    : 1,
                                                WebkitBoxOrient: "vertical",
                                                overflow: "hidden",
                                            }}
                                        >
                                            {item1.result}
                                        </Text>
                                        <CnaButton
                                            variant="transparent"
                                            color="#19288F"
                                            size="xs"
                                            onClick={() =>
                                                toggleExpand(`result${index1}`)
                                            }
                                            leftSection={
                                                expandedItems[
                                                    `result${index1}`
                                                ] ? (
                                                    <CaretUp size={16} />
                                                ) : (
                                                    <CaretDown size={16} />
                                                )
                                            }
                                        >
                                            {expandedItems[`result${index1}`]
                                                ? "收起"
                                                : "展开"}
                                        </CnaButton>
                                    </Group>
                                </Stack>
                            </Paper>
                        ))}
                    </Stack>
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                >
                    <Group
                        mb="md"
                        justify="space-between"
                    >
                        <Group>
                            <Robot size={20} />
                            <Text fw={500}>AI工具使用经验</Text>
                        </Group>
                    </Group>
                    <Stack gap="md">
                        {aiToolInfo?.map((item1: any, index1: any) => (
                            <Paper
                                p="lg"
                                radius="md"
                                className="tw-bg-[#F1F3F5]"
                                key={index1}
                            >
                                <Group
                                    justify="space-between"
                                    mb="md"
                                >
                                    <Text
                                        c="#060D3D"
                                        fw={600}
                                    >
                                        {item1.name}
                                    </Text>
                                </Group>
                                <Text>{item1.content}</Text>
                            </Paper>
                        ))}
                    </Stack>
                </Paper>
            </ScrollArea>
        </Stack>
    );
};

export default CommunityDetail;
