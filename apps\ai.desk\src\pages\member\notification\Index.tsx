import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import {
    Paper,
    Group,
    Menu,
    ScrollArea,
    Table,
    Text,
    TextInput,
    Checkbox,
    Pagination,
    Loader,
    Center,
} from "@mantine/core";
import {
    CaretDown,
    EnvelopeSimple,
    EnvelopeSimpleOpen,
    MagnifyingGlass,
} from "@phosphor-icons/react";
import api from "@/apis";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { useDebounce } from "ahooks";
import { useNavigate } from "react-router-dom";
import useNotificationStore from "@/store/notification";
import useModalStore from "@/store/modal";

// 通知类型常量
const NOTIFICATION_TYPES = {
    ALL: 0, // 全部
    SYSTEM: 1, // 系统通知
    LEVEL_UP: 2, // 晋级通知
    PAYMENT: 3, // 出入款通知
    ANNOUNCEMENT: 4, // 信息公告
};

// 通知类型映射
const NOTIFICATION_TYPE_NAMES: Record<string, string> = {
    "1": "系统通知",
    "2": "晋级通知",
    "3": "出入款通知",
    "4": "信息公告",
};

// 转换API通知到本地通知格式
const convertNotification = (item: NotificationItem): Notification => {
    return {
        id: item.notificationID,
        title: item.notificationTitleZH,
        content: item.notificationDescriptionZH,
        eventCode: item.eventCode,
        sendTime:
            (item.created_at &&
                dayjs(item.created_at).format("YYYY-MM-DD HH:mm:ss")) ||
            "-",
        isRead: item.notificationState === 1,
        type_remark: item.type_remark,
    };
};

// 本地通知接口定义
interface Notification {
    id: string;
    title: string;
    content: string;
    eventCode: string;
    sendTime: string;
    isRead: boolean;
    type_remark: string;
}

const NotificationIndex = () => {
    const navigate = useNavigate();
    const { deleteNotification, markAsRead, markAllAsRead } =
        useNotificationStore();
    const openConfirm = useModalStore.use.openConfirm();

    // 通知列表数据
    const [notifications, setNotifications] = useState<Notification[]>([]);
    // 选中的行
    const [selectedRows, setSelectedRows] = useState<string[]>([]);
    // 加载状态
    const [loading, setLoading] = useState(false);
    // 搜索关键词
    const [searchKeyword, setSearchKeyword] = useState("");
    const debouncedSearchKeyword = useDebounce(searchKeyword, { wait: 500 });
    // 当前页码
    const [currentPage, setCurrentPage] = useState(1);
    // 每页数量
    const [pageSize] = useState(10);
    // 总数量
    const [totalCount, setTotalCount] = useState(0);
    // 选中的通知类型
    const [selectedType, setSelectedType] = useState(NOTIFICATION_TYPES.ALL);
    // todo 关键字和类型的筛选，接口有问题
    const [filteredNotifications, setFilteredNotifications] = useState<
        Notification[]
    >([]);

    // 获取通知列表
    const fetchNotifications = async () => {
        setLoading(true);
        try {
            let eventCode =
                selectedType === NOTIFICATION_TYPES.ALL
                    ? undefined
                    : selectedType;
            const data = await api.notification.list(
                currentPage,
                pageSize,
                eventCode,
                searchKeyword
            );
            if (data) {
                const transformedData =
                    data.notification.map(convertNotification);
                setNotifications(transformedData);
                setFilteredNotifications(transformedData);
                setTotalCount(data.paginate.totalRecord || 0);
            }
        } catch (error) {
            console.error("获取通知列表失败", error);
            noty.error("获取通知列表失败");
        } finally {
            setLoading(false);
        }
    };

    // 监听页码、筛选类型、搜索关键词变化，重新获取数据
    useEffect(() => {
        fetchNotifications();
    }, [currentPage, selectedType, debouncedSearchKeyword]);

    // 搜索框变化
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const keyword = e.target.value;
        setSearchKeyword(keyword);
        setCurrentPage(1); // 重置页码
    };

    // 全选/取消全选
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedRows(filteredNotifications.map((item) => item.id));
        } else {
            setSelectedRows([]);
        }
    };

    // 单行选择/取消选择
    const handleSelectRow = (id: string, checked: boolean) => {
        if (checked) {
            setSelectedRows([...selectedRows, id]);
        } else {
            setSelectedRows(selectedRows.filter((rowId) => rowId !== id));
        }
    };

    // 删除单个通知
    const handleDeleteSingle = async (id: string) => {
        openConfirm({
            title: "删除通知",
            message: "确定删除该通知吗？",
            onConfirm: async () => {
                try {
                    const success = await deleteNotification(id);
                    if (success) {
                        noty.success("删除成功");
                        fetchNotifications();
                        // 从选中列表中移除
                        setSelectedRows(
                            selectedRows.filter((rowId) => rowId !== id)
                        );
                    }
                } catch (error) {
                    console.error("删除通知失败", error);
                    noty.error("删除通知失败");
                }
            },
        });
    };

    // 批量删除通知
    const handleDeleteBatch = async () => {
        if (selectedRows.length === 0) return;

        openConfirm({
            title: "批量删除通知",
            message: "确定删除选中的通知吗？",
            onConfirm: async () => {
                try {
                    let success = true;
                    for (const id of selectedRows) {
                        const result = await deleteNotification(id);
                        if (!result) {
                            success = false;
                        }
                    }

                    if (success) {
                        noty.success("批量删除成功");
                    } else {
                        noty.error("部分删除操作失败");
                    }

                    fetchNotifications();
                    setSelectedRows([]);
                } catch (error) {
                    console.error("批量删除通知失败", error);
                    noty.error("批量删除通知失败");
                }
            },
        });
    };

    // 标记单个通知为已读
    const handleReadSingle = async (id: string) => {
        try {
            const success = await markAsRead(id);
            if (success) {
                noty.success("标记已读成功");
                fetchNotifications();
            }
        } catch (error) {
            console.error("标记已读失败", error);
            noty.error("标记已读失败");
        }
    };

    // 批量标记已读
    const handleReadBatch = async () => {
        if (selectedRows.length === 0) return;

        try {
            let success = true;
            for (const id of selectedRows) {
                const result = await markAsRead(id);
                if (!result) {
                    success = false;
                }
            }

            if (success) {
                noty.success("批量标记已读成功");
            } else {
                noty.error("部分标记已读操作失败");
            }

            fetchNotifications();
            setSelectedRows([]);
        } catch (error) {
            console.error("批量标记已读失败", error);
            noty.error("批量标记已读失败");
        }
    };

    // 全部标记已读
    const handleReadAll = async () => {
        try {
            const success = await markAllAsRead();
            if (success) {
                noty.success("全部标记已读成功");
                fetchNotifications();
            }
        } catch (error) {
            console.error("全部标记已读失败", error);
            noty.error("全部标记已读失败");
        }
    };

    // 切换通知类型
    const handleTypeChange = (type: number) => {
        setSelectedType(type);
        setCurrentPage(1); // 重置页码
    };

    // 获取当前类型名称
    const getCurrentTypeName = () => {
        if (selectedType === NOTIFICATION_TYPES.ALL) return "全部";
        return NOTIFICATION_TYPE_NAMES[String(selectedType)] || "全部";
    };

    // 处理行点击
    const handleRowClick = (item: Notification) => {
        // 如果未读，则标记为已读
        if (!item.isRead) {
            handleReadSingle(item.id);
        }
        navigate(`/member/notifications/detail`, {
            state: {
                notificationID: item.id,
            },
        });
    };

    return (
        <div>
            <PageHeader
                title="通知中心"
                subTitle="本栏目整合重要通知与信息，助力合伙人速览业务动态；实时推送与分类存储，为决策提供有力支撑。"
            />
            <Paper
                mt="xl"
                p="lg"
                radius="lg"
            >
                <TextInput
                    placeholder="搜索标题、通知类型"
                    rightSection={<MagnifyingGlass />}
                    className="tw-mb-8"
                    value={searchKeyword}
                    onChange={handleSearchChange}
                />
                <Group justify="space-between">
                    <Menu
                        shadow="md"
                        width={200}
                        position="bottom-start"
                    >
                        <Menu.Target>
                            <CnaButton
                                variant="default"
                                rightSection={<CaretDown weight="fill" />}
                                className="tw-border-none tw-text-lg"
                                fw={600}
                            >
                                {getCurrentTypeName()}
                            </CnaButton>
                        </Menu.Target>
                        <Menu.Dropdown>
                            <Menu.Item
                                onClick={() =>
                                    handleTypeChange(NOTIFICATION_TYPES.ALL)
                                }
                            >
                                全部
                            </Menu.Item>
                            <Menu.Item
                                onClick={() =>
                                    handleTypeChange(NOTIFICATION_TYPES.SYSTEM)
                                }
                            >
                                系统通知
                            </Menu.Item>
                            <Menu.Item
                                onClick={() =>
                                    handleTypeChange(
                                        NOTIFICATION_TYPES.LEVEL_UP
                                    )
                                }
                            >
                                晋级通知
                            </Menu.Item>
                            <Menu.Item
                                onClick={() =>
                                    handleTypeChange(NOTIFICATION_TYPES.PAYMENT)
                                }
                            >
                                出入款通知
                            </Menu.Item>
                            <Menu.Item
                                onClick={() =>
                                    handleTypeChange(
                                        NOTIFICATION_TYPES.ANNOUNCEMENT
                                    )
                                }
                            >
                                信息公告
                            </Menu.Item>
                        </Menu.Dropdown>
                    </Menu>
                    <Group gap="xs">
                        <CnaButton
                            variant="default"
                            disabled={selectedRows.length === 0}
                            onClick={handleDeleteBatch}
                            c="#FF4D4F"
                        >
                            删除
                        </CnaButton>
                        <CnaButton
                            variant="default"
                            disabled={selectedRows.length === 0}
                            onClick={handleReadBatch}
                        >
                            标为已读
                        </CnaButton>
                        <CnaButton
                            variant="default"
                            onClick={handleReadAll}
                        >
                            全部标为已读
                        </CnaButton>
                    </Group>
                </Group>
                <ScrollArea
                    h="calc(100vh - 500px)"
                    mt="lg"
                >
                    {loading ? (
                        <Center h={400}>
                            <Loader size="md" />
                        </Center>
                    ) : filteredNotifications.length === 0 ? (
                        <Center h={400}>
                            <Text c="dimmed">暂无通知</Text>
                        </Center>
                    ) : (
                        <Table
                            withRowBorders={false}
                            highlightOnHover
                            className="custom-table tw-whitespace-nowrap"
                        >
                            <Table.Thead>
                                <Table.Tr>
                                    <Table.Th>
                                        <Checkbox
                                            checked={
                                                filteredNotifications.length >
                                                    0 &&
                                                selectedRows.length ===
                                                    filteredNotifications.length
                                            }
                                            onChange={(event) =>
                                                handleSelectAll(
                                                    event.currentTarget.checked
                                                )
                                            }
                                        />
                                    </Table.Th>
                                    <Table.Th>消息标题</Table.Th>
                                    <Table.Th>类型</Table.Th>
                                    <Table.Th>接收时间</Table.Th>
                                    <Table.Th>操作</Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody className="tw-whitespace-nowrap">
                                {filteredNotifications.map((item, index) => (
                                    <Table.Tr
                                        key={item.id}
                                        c={item.isRead ? "dimmed" : undefined}
                                        onClick={() => handleRowClick(item)}
                                        style={{ cursor: "pointer" }}
                                    >
                                        <Table.Td>
                                            <Checkbox
                                                checked={selectedRows.includes(
                                                    item.id
                                                )}
                                                onClick={(event) => {
                                                    event.stopPropagation();
                                                }}
                                                onChange={(event) => {
                                                    event.stopPropagation();
                                                    handleSelectRow(
                                                        item.id,
                                                        event.currentTarget
                                                            .checked
                                                    );
                                                }}
                                            />
                                        </Table.Td>
                                        <Table.Td>
                                            <Group
                                                gap="xs"
                                                wrap="nowrap"
                                            >
                                                {item.isRead ? (
                                                    <EnvelopeSimpleOpen
                                                        size={20}
                                                    />
                                                ) : (
                                                    <EnvelopeSimple
                                                        size={20}
                                                        color="#165DFF"
                                                    />
                                                )}
                                                <Text>{item.title}</Text>
                                            </Group>
                                        </Table.Td>
                                        <Table.Td>{item.type_remark}</Table.Td>
                                        <Table.Td>{item.sendTime}</Table.Td>
                                        <Table.Td>
                                            <Group wrap="nowrap">
                                                {!item.isRead && (
                                                    <CnaButton
                                                        unstyled
                                                        className="tw-text-[#165DFF]"
                                                        onClick={(event) => {
                                                            event.stopPropagation();
                                                            handleReadSingle(
                                                                item.id
                                                            );
                                                        }}
                                                    >
                                                        标为已读
                                                    </CnaButton>
                                                )}
                                                <CnaButton
                                                    unstyled
                                                    className="tw-text-[#FF4D4F]"
                                                    onClick={(event) => {
                                                        event.stopPropagation();
                                                        handleDeleteSingle(
                                                            item.id
                                                        );
                                                    }}
                                                >
                                                    删除
                                                </CnaButton>
                                            </Group>
                                        </Table.Td>
                                    </Table.Tr>
                                ))}
                            </Table.Tbody>
                        </Table>
                    )}
                </ScrollArea>

                {totalCount > 0 && (
                    <Group
                        justify="space-between"
                        mt="xl"
                    >
                        <Text>共{totalCount}条</Text>
                        <Pagination
                            total={Math.ceil(totalCount / pageSize)}
                            value={currentPage}
                            onChange={setCurrentPage}
                        />
                    </Group>
                )}
            </Paper>
        </div>
    );
};

export default NotificationIndex;
