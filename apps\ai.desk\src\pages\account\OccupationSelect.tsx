import {
    Image,
    Stack,
    Text,
    TextInput,
    SimpleGrid,
    ScrollArea,
    Title,
} from "@mantine/core";
import { useRequest } from "ahooks";
import { useNavigate, useLocation } from "react-router-dom";
import { useState } from "react";
import { cnaRequest } from "@code.8cent/utils";
import { MagnifyingGlass, SpinnerGap } from "@phosphor-icons/react";

const OccupationSelect = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [search, setSearch] = useState("");
    const [occupations, setOccupations] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const { loading, run: fetchOccupations } = useRequest(
        async () => {
            setIsLoading(true);
            const { error, result } = await cnaRequest(
                "/api/v1/config/professionList",
                "GET",
                { keyword: search }
            );
            if (!error && result) {
                setOccupations(result.data);
            }
            setIsLoading(false);
        },
        {
            debounceWait: 500,
            refreshDeps: [search],
        }
    );

    const handleOccupationClick = async (occupation: any) => {
        // 保存职业信息，用于注册页使用
        await window.localForage.setItem("occupation_id", occupation.id);
        await window.localForage.setItem("occupation_name", occupation.nameZH);

        // 获取refer参数
        const queryParams = new URLSearchParams(location.search);
        const refer = queryParams.get("refer");

        // 不是新用户且未填写基本信息，则跳转到注册页
        const baseInfo = await window.localForage.getItem("base_info");
        const isNew = await window.localForage.getItem("is_new");
        if (isNew !== "1" && baseInfo === 2) {
            sessionStorage.setItem("refer", refer || "");
            navigate("/account/register", { replace: true });
            return;
        }


        const url = refer
            ? "/account/company?refer=" + refer
            : "/account/company";
        navigate(url, {
            replace: true,
        });
    };

    return (
        <div className="tw-relative boot-bg tw-min-h-screen">
            <Stack className="md:tw-px-32 tw-px-4 tw-pt-16">
                {/* <TextInput
                    placeholder="搜索"
                    size="lg"
                    className="tw-mt-16"
                    leftSection={<MagnifyingGlass />}
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                /> */}
                <Title
                    order={2}
                    style={{ letterSpacing: "5px" }}
                    className="tw-text-white tw-text-center tw-tracking-[0.5rem] tw-translate-x-[0.25rem]"
                >
                    请点击选择您的专业属性
                </Title>
                {isLoading ? (
                    <div className="tw-flex tw-justify-center tw-items-center tw-h-[80vh]">
                        <SpinnerGap
                            size={32}
                            color="white"
                            className="tw-animate-spin"
                        />
                    </div>
                ) : (
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center md:tw-h-[70vh] tw-h-[80vh]">
                        <ScrollArea
                            className="tw-w-full"
                            styles={{
                                viewport: {
                                    height: "100%",
                                    width: "100%",
                                },
                            }}
                        >
                            <SimpleGrid
                                cols={{ base: 3, md: 6 }}
                                spacing={{ md: 16 }}
                                verticalSpacing={{ md: 4 }}
                                className="tw-py-8"
                            >
                                {occupations.map((occupation, index) => (
                                    <div
                                        key={index}
                                        className="tw-text-center tw-cursor-pointer"
                                        onClick={() =>
                                            handleOccupationClick(occupation)
                                        }
                                    >
                                        <div className="xl:tw-p-12 tw-p-4">
                                            <Image
                                                src={`https://doc.corporate-advisory.cn/${occupation.image}`}
                                                alt={occupation.nameZH}
                                                className="tw-rounded-full tw-select-none tw-drag-none"
                                                draggable={false}
                                            />
                                            <Text
                                                style={{ letterSpacing: "3px" }}
                                                className="tw-text-white md:tw-text-lg tw-tracking-[0.5rem] tw-translate-x-[0.25rem]"
                                            >
                                                {occupation.nameZH}
                                            </Text>
                                        </div>
                                    </div>
                                ))}
                            </SimpleGrid>
                        </ScrollArea>
                    </div>
                )}
            </Stack>
            <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2025
            </div>
        </div>
    );
};

export default OccupationSelect;
