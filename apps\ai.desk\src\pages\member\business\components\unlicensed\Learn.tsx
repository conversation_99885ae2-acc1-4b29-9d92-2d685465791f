import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@code.8cent/react/components";
import { Group, Paper, Stack, Text, Image, Grid, Menu } from "@mantine/core";
import {
    CaretLeft,
    SpeakerHigh,
    SkipBack,
    SkipForward,
    Queue,
    Check,
} from "@phosphor-icons/react";
import { useEffect, useState, useRef } from "react";
import ExamPdfViewer from "@/components/member/business/ExamPdfViewer";
import AITeacher from "@/components/member/business/AITeacher";
import business from "@/apis/business";
import { getFileByUrl } from "@code.8cent/react/FileViewer";
import useBusinessStore, {
    setBusinessSection,
    updateLearningProgress,
    setCurrentPage as setStorePage,
    setTotalPages as setStoreTotalPages,
    setProgress as setStoreProgress,
    setTimer as setStoreTimer,
    setIsTimerRunning as setStoreTimerRunning,
} from "@/store/business";

const Learn = () => {
    const [showAITeacher, setShowAITeacher] = useState(false);
    const [pdfFile, setPdfFile] = useState<File | null>(null);
    const [fileInitialPage, setFileInitialPage] = useState<number>(1);
    // menu list
    const [courseList, setCourseList] = useState<any | null>(null);
    const [prevCourseId, setPrevCourseId] = useState<number | null>(null);
    const [nextCourseId, setNextCourseId] = useState<number | null>(null);
    const [courseDetail, setCourseDetail] = useState<TCourseItem | null>(null);
    const [courseProgress, setCourseProgress] = useState<number>(0);
    // 计时器相关状态
    const [timer, setTimer] = useState<number>(0);
    const [isTimerRunning, setIsTimerRunning] = useState<boolean>(false);
    // 进度管理相关状态
    const [totalPages, setTotalPages] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [originalProgress, setOriginalProgress] = useState<number>(0);
    const [pendingProgress, setPendingProgress] = useState<number>(0);
    const progressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [isLoadingCourse, setIsLoadingCourse] = useState<boolean>(false);
    const [pdfLoadError, setPdfLoadError] = useState<string | null>(null);

    const courseId = useBusinessStore.use.courseId();
    const serviceId = useBusinessStore.use.serviceId();
    const learningProgress = useBusinessStore.use.learningProgress();

    // 格式化时间显示为 00:00:00
    const formatTime = (seconds: number): string => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return `${hours.toString().padStart(2, "0")}:${minutes
            .toString()
            .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    // PDF文件缓存管理
    const getCachedPdfFile = async (url: string): Promise<File | null> => {
        try {
            const cacheKey = `pdf_cache_${url}`;
            const cached = await window.localForage.getItem(cacheKey);

            if (cached) {
                const { data, timestamp } = cached as {
                    data: string;
                    timestamp: number;
                };
                // 检查缓存是否过期（24小时）
                if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
                    // 将base64数据转换回File对象
                    const byteCharacters = atob(data);
                    const byteNumbers = new Array(byteCharacters.length);
                    for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                    }
                    const byteArray = new Uint8Array(byteNumbers);
                    const blob = new Blob([byteArray], {
                        type: "application/pdf",
                    });
                    return new File([blob], "course.pdf", {
                        type: "application/pdf",
                    });
                } else {
                    // 缓存过期，删除
                    await window.localForage.removeItem(cacheKey);
                }
            }
        } catch (error) {
            console.error("读取PDF缓存失败:", error);
        }
        return null;
    };

    const setCachedPdfFile = async (url: string, file: File) => {
        try {
            const reader = new FileReader();
            reader.onload = async () => {
                const base64Data = (reader.result as string).split(",")[1];
                const cacheData = {
                    data: base64Data,
                    timestamp: Date.now(),
                };
                const cacheKey = `pdf_cache_${url}`;
                await window.localForage.setItem(cacheKey, cacheData);
            };
            reader.readAsDataURL(file);
        } catch (error) {
            console.error("设置PDF缓存失败:", error);
        }
    };

    // 清理过期的PDF缓存
    const cleanExpiredCache = async () => {
        try {
            const keys = await window.localForage.keys();
            const pdfCacheKeys = keys.filter((key) =>
                key.startsWith("pdf_cache_")
            );
            const now = Date.now();
            const expireTime = 24 * 60 * 60 * 1000; // 24小时

            for (const key of pdfCacheKeys) {
                const cached = await window.localForage.getItem(key);
                if (cached && (cached as any).timestamp) {
                    if (now - (cached as any).timestamp > expireTime) {
                        await window.localForage.removeItem(key);
                    }
                }
            }
        } catch (error) {
            console.error("清理过期缓存失败:", error);
        }
    };

    // 手动清理所有PDF缓存
    const clearAllPdfCache = async () => {
        try {
            const keys = await window.localForage.keys();
            const pdfCacheKeys = keys.filter((key) =>
                key.startsWith("pdf_cache_")
            );

            for (const key of pdfCacheKeys) {
                await window.localForage.removeItem(key);
            }
        } catch (error) {
            console.error("清理所有PDF缓存失败:", error);
        }
    };

    // 获取PDF文件
    const fetchPdfFile = async (url: string): Promise<File | null> => {
        try {
            // 先检查本地缓存
            const cachedFile = await getCachedPdfFile(url);
            if (cachedFile) {
                return cachedFile;
            }

            const fileUrl = `${window.api_base_url}/api/v1/file/resource?path=${url}&type=remote`;
            const file = await getFileByUrl(fileUrl);

            if (file) {
                // 设置缓存
                await setCachedPdfFile(url, file);
                return file;
            } else {
                throw new Error(
                    "getFileByUrl返回null，可能是网络问题或文件不存在"
                );
            }
        } catch (error) {
            console.error("获取PDF文件失败:", error);
            if (error instanceof Error) {
                console.error("错误详情:", error.message);
            }
            return null;
        }
    };

    // 计算初始页码
    const calculateInitialPage = (
        progress: number,
        totalPages: number
    ): number => {
        if (progress <= 0 || totalPages <= 0) return 1;
        const page = Math.ceil((progress / 100) * totalPages);
        return Math.max(1, Math.min(page, totalPages));
    };

    // 计算当前进度
    const calculateProgress = (
        currentPage: number,
        totalPages: number
    ): number => {
        if (totalPages <= 0) return 0;
        const progress = Math.round((currentPage / totalPages) * 100);
        return Math.min(progress, 100);
    };

    // 提交进度
    const submitProgress = async (progress: number) => {
        if (!courseDetail || progress <= originalProgress) return;

        try {
            const success = await business.submitCourseProgress([
                {
                    project_service_course_id: courseDetail.id,
                    progress: progress,
                },
            ]);

            if (success) {
                setOriginalProgress(progress);
                setCourseProgress(progress);
            }
        } catch (error) {
            console.error("提交进度失败:", error);
        }
    };

    // 延迟提交进度
    const debouncedSubmitProgress = (progress: number) => {
        if (progressTimeoutRef.current) {
            clearTimeout(progressTimeoutRef.current);
        }

        // 保存当前课程ID，确保只更新正确的课程
        const currentCourseId = courseDetail?.id;

        progressTimeoutRef.current = setTimeout(() => {
            // 确保当前课程ID与提交时的课程ID一致
            if (currentCourseId === courseDetail?.id) {
                submitProgress(progress);
            }
        }, 2000); // 2秒后提交
    };

    // 计时器效果
    useEffect(() => {
        let interval: NodeJS.Timeout;

        if (isTimerRunning) {
            interval = setInterval(() => {
                setTimer((prev) => prev + 1);
            }, 1000);
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [isTimerRunning]);

    // 初始化数据
    useEffect(() => {
        // 只有在没有学习进度数据时才从store初始化
        if (!learningProgress || !learningProgress.timer) {
            setIsTimerRunning(true);
            setTimer(0);
        } else {
            // 如果有学习进度数据，恢复状态
            setCurrentPage(learningProgress.currentPage);
            setTotalPages(learningProgress.totalPages);
            setPendingProgress(learningProgress.progress);
            setTimer(learningProgress.timer);
            setIsTimerRunning(learningProgress.isTimerRunning);
        }

        fetchCourseList();
        fetchCourseDetail(courseId);
        // 清理过期的PDF缓存
        cleanExpiredCache();
    }, []);

    // 当课程切换时，重置计时器
    useEffect(() => {
        // 只有在课程ID真正改变时才重置计时器
        if (courseId) {
            setTimer(0);
            setIsTimerRunning(true);
            // 同时更新store中的计时器状态
            setStoreTimer(0);
            setStoreTimerRunning(true);
        }
    }, [courseId]);

    // 当课程详情更新时，获取PDF文件
    useEffect(() => {
        if (courseDetail?.url) {
            loadPdfFile();
        }
    }, [courseDetail]);

    // 加载PDF文件
    const loadPdfFile = async () => {
        if (!courseDetail?.url) {
            return;
        }

        setPdfLoadError(null);
        const file = await fetchPdfFile(courseDetail.url);
        if (file) {
            setPdfFile(file);

            // 计算初始页码
            const initialPage = calculateInitialPage(
                courseProgress,
                courseDetail.total || 0
            );
            setFileInitialPage(initialPage);
            setCurrentPage(initialPage);
            setTotalPages(courseDetail.total || 0);
            setOriginalProgress(courseProgress);
        } else {
            setPdfLoadError("PDF文件加载失败，请检查网络连接或稍后重试");
        }
    };

    const handleBack = () => {
        // 离开页面时提交当前进度
        if (pendingProgress > originalProgress) {
            submitProgress(pendingProgress);
        }
        setBusinessSection("unlicensed-courses");
    };

    const toggleAITeacher = () => {
        setShowAITeacher(!showAITeacher);
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);

        // 计算新进度
        const newProgress = calculateProgress(page, totalPages);
        setPendingProgress(newProgress);

        // 延迟提交进度
        debouncedSubmitProgress(newProgress);
    };

    // 滚动到底部
    const handleReachedBottom = () => {
        setCurrentPage(totalPages);
        setPendingProgress(100);
        debouncedSubmitProgress(100);
    };

    const fetchCourseList = async () => {
        try {
            // 获取全部的课程
            const res = await business.getCourseChapters(
                serviceId.toString(),
                1,
                100
            );
            if (res) {
                setCourseList(res.data);
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchCourseDetail = async (course_id: number) => {
        if (isLoadingCourse || course_id === courseDetail?.id) return;

        try {
            setIsLoadingCourse(true);

            // 清除待处理的进度提交
            if (progressTimeoutRef.current) {
                clearTimeout(progressTimeoutRef.current);
                progressTimeoutRef.current = null;
            }

            // 切换课程前，提交当前课程的进度
            if (courseDetail?.id && pendingProgress > originalProgress) {
                await submitProgress(pendingProgress);
            }

            // 重置PDF文件，确保切换课程时不会显示旧课程的PDF
            setPdfFile(null);

            const res = await business.getChapterDetail(course_id);

            if (res) {
                const newProgress = res.profile_course[0].pivot.progress;
                setCourseDetail(res);
                setPrevCourseId(res.prev_course_id);
                setNextCourseId(res.next_course_id);
                setCourseProgress(newProgress);
                // 切换课程时重置计时器
                setTimer(0);
                setIsTimerRunning(true);
                // 同步到store
                setStoreTimer(0);
                setStoreTimerRunning(true);
                // 重置进度相关状态
                setOriginalProgress(newProgress);
                setPendingProgress(newProgress);

                // 如果有总页数信息，提前计算初始页码
                if (res.total) {
                    const initialPage = calculateInitialPage(
                        newProgress,
                        res.total
                    );
                    setFileInitialPage(initialPage);
                }
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoadingCourse(false);
        }
    };

    // 组件卸载时清理
    useEffect(() => {
        return () => {
            if (progressTimeoutRef.current) {
                clearTimeout(progressTimeoutRef.current);
            }
            // 离开页面时提交当前进度
            if (pendingProgress > originalProgress) {
                submitProgress(pendingProgress);
            }
        };
    }, []);

    // Update store timer when local timer changes
    useEffect(() => {
        setStoreTimer(timer);
    }, [timer]);

    // Update store timer running state
    useEffect(() => {
        setStoreTimerRunning(isTimerRunning);
    }, [isTimerRunning]);

    // Update store with page changes
    useEffect(() => {
        setStorePage(currentPage);
    }, [currentPage]);

    // Update store with total pages
    useEffect(() => {
        setStoreTotalPages(totalPages);
    }, [totalPages]);

    // Update store with progress
    useEffect(() => {
        setStoreProgress(pendingProgress);
    }, [pendingProgress]);

    // Initialize from store if available
    useEffect(() => {
        // 这个逻辑已经在初始化数据的useEffect中处理了，避免重复
        // if (learningProgress) {
        //     setCurrentPage(learningProgress.currentPage);
        //     setTotalPages(learningProgress.totalPages);
        //     setPendingProgress(learningProgress.progress);
        //     setTimer(learningProgress.timer);
        //     setIsTimerRunning(learningProgress.isTimerRunning);
        // }
    }, []);

    return (
        <Stack className="tw-h-full">
            <Group className="tw-w-full tw-relative">
                <CnaButton
                    leftSection={<CaretLeft />}
                    variant="default"
                    className="tw-bg-[#F6F6F6] tw-border-none"
                    size="md"
                    fw={500}
                    onClick={handleBack}
                >
                    返回
                </CnaButton>
                <Text
                    fw={500}
                    fz={{ base: "20px", md: "24px" }}
                    className="tw-absolute tw-left-1/2 tw-transform -tw-translate-x-1/2"
                >
                    合伙人加盟手册课程
                </Text>
            </Group>

            <Stack className="tw-p-4 tw-h-full tw-bg-white tw-rounded-xl">
                <Group justify="space-between">
                    <Text>{formatTime(timer)}</Text>
                    <Text>{courseDetail?.name}</Text>
                    <Text>本节学习进度 {courseProgress}%</Text>
                </Group>

                <Grid className="tw-h-full [&_.mantine-Grid-inner]:tw-h-full">
                    <Grid.Col span={showAITeacher ? 8 : 12}>
                        <Paper className="tw-bg-[#F2F4F7] tw-relative tw-rounded-lg tw-flex tw-flex-col tw-h-full">
                            {isLoadingCourse ? (
                                <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                                    <Text>正在加载课程...</Text>
                                </div>
                            ) : pdfLoadError ? (
                                <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-full tw-gap-4">
                                    <Text className="tw-text-red-500">
                                        {pdfLoadError}
                                    </Text>
                                    <Group>
                                        <CnaButton
                                            variant="outline"
                                            onClick={loadPdfFile}
                                            disabled={isLoadingCourse}
                                        >
                                            重试
                                        </CnaButton>
                                        <CnaButton
                                            variant="outline"
                                            onClick={async () => {
                                                await clearAllPdfCache();
                                                loadPdfFile();
                                            }}
                                            disabled={isLoadingCourse}
                                        >
                                            清理缓存并重试
                                        </CnaButton>
                                    </Group>
                                </div>
                            ) : pdfFile ? (
                                <ExamPdfViewer
                                    file={pdfFile}
                                    initialPage={fileInitialPage}
                                    onPageChange={handlePageChange}
                                    onReachedBottom={handleReachedBottom}
                                />
                            ) : (
                                <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                                    <Text>正在加载课程...</Text>
                                </div>
                            )}
                        </Paper>
                    </Grid.Col>
                    {showAITeacher && (
                        <Grid.Col span={4}>
                            <AITeacher
                                onClose={() => setShowAITeacher(false)}
                            />
                        </Grid.Col>
                    )}
                </Grid>

                <Group justify="center">
                    {/* <CnaButton
                        variant="outline"
                        radius="xl"
                        className="tw-bg-[linear-gradient(277deg,#FFF_35.09%,#EEEFF5_100%)] tw-border-[#19288F]"
                    >
                        <SpeakerHigh
                            size={20}
                            color="#19288F"
                        />
                    </CnaButton> */}
                    <Group>
                        <Menu position="top-start">
                            <Menu.Target>
                                <CnaButton
                                    variant="outline"
                                    radius="xl"
                                    className="tw-bg-[linear-gradient(277deg,#FFF_35.09%,#EEEFF5_100%)] tw-border-[#19288F]"
                                >
                                    <Queue
                                        size={20}
                                        color="#19288F"
                                    />
                                </CnaButton>
                            </Menu.Target>
                            <Menu.Dropdown>
                                {courseList?.map((item) =>
                                    courseDetail?.id == item.id ? (
                                        <Menu.Item key={item.id}>
                                            <Group>
                                                <Text>{item.name}</Text>
                                                <Check
                                                    size={12}
                                                    color="#165DFF"
                                                />
                                            </Group>
                                        </Menu.Item>
                                    ) : (
                                        <Menu.Item
                                            key={item.id}
                                            onClick={() =>
                                                !isLoadingCourse &&
                                                fetchCourseDetail(item.id)
                                            }
                                            disabled={isLoadingCourse}
                                        >
                                            {item.name}
                                        </Menu.Item>
                                    )
                                )}
                            </Menu.Dropdown>
                        </Menu>
                        <CnaButton
                            variant="outline"
                            radius="xl"
                            className="tw-bg-[linear-gradient(277deg,#FFF_35.09%,#EEEFF5_100%)] tw-border-[#19288F]"
                            onClick={() => fetchCourseDetail(prevCourseId)}
                            disabled={!prevCourseId || isLoadingCourse}
                        >
                            <SkipBack
                                size={20}
                                color="#19288F"
                            />
                        </CnaButton>
                        <CnaButton
                            variant="outline"
                            radius="xl"
                            className="tw-bg-[linear-gradient(277deg,#FFF_35.09%,#EEEFF5_100%)] tw-border-[#19288F]"
                            onClick={() => fetchCourseDetail(nextCourseId)}
                            disabled={!nextCourseId || isLoadingCourse}
                        >
                            <SkipForward
                                size={20}
                                color="#19288F"
                            />
                        </CnaButton>
                    </Group>
                    {/* <Group
                        gap={4}
                        className="tw-cursor-pointer"
                        onClick={toggleAITeacher}
                    >
                        <Image
                            src="/images/station/ai-teacher-border.png"
                            alt="AI小老师"
                            w={30}
                        />
                        <Text>AI小老师</Text>
                    </Group> */}
                </Group>
            </Stack>
        </Stack>
    );
};

export default Learn;
