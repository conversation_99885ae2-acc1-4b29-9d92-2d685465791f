# 用户状态管理优化方案

## 问题分析

### 当前存在的问题

1. **数据分散存储**：用户数据分散在 `localForage`、`sessionStorage` 和多个 store 中，缺乏统一管理
2. **刷新页面数据丢失**：store 中的数据在页面刷新后会丢失，需要重新从存储中恢复
3. **重复的 API 调用**：多个地方都在获取相同的用户数据
4. **复杂的跳转逻辑**：`TargetV2.tsx` 中的跳转逻辑过于复杂，难以维护
5. **状态同步问题**：不同组件间的状态同步困难，容易出现数据不一致

## 优化方案

### 1. 统一用户状态管理

创建了新的 `useUserStore`，统一管理所有用户相关数据：

```typescript
// apps/ai.desk/src/store/user.ts
export interface UserProfile {
    profile_id: number;
    profile_status: number;
    register_setting: string[];
    pay_token?: string;
    is_new?: 1 | 2;
    base_info?: 1 | 2;
    occupation_id?: string;
    occupation_name?: string;
    refer?: string;
}

export interface UserState {
    token: string;
    profile: UserProfile | null;
    isLoggedIn: boolean;
    isLoading: boolean;
    stepV3: string;
    interviewStatus: string;
    hasShownInterviewSuccess: Record<number, boolean>;
}
```

### 2. 持久化存储

使用 Zustand 的 `persist` 中间件实现自动持久化：

```typescript
const baseUserStore = create<UserStore>()(
    devtools(
        persist(
            (set, get) => ({
                // store 实现
            }),
            {
                name: "user-store",
                partialize: (state) => ({
                    token: state.token,
                    profile: state.profile,
                    isLoggedIn: state.isLoggedIn,
                    stepV3: state.stepV3,
                    interviewStatus: state.interviewStatus,
                    hasShownInterviewSuccess: state.hasShownInterviewSuccess,
                }),
            }
        ),
        {
            name: "user-store",
        }
    )
);
```

### 3. 页面跳转逻辑抽离

创建了 `NavigationLogic` 类，将复杂的跳转逻辑从组件中抽离：

```typescript
// apps/ai.desk/src/utils/navigationLogic.ts
export class NavigationLogic {
    async handleUserNavigation(
        userData: UserLoginData,
        params: NavigationParams
    ): Promise<void> {
        // 处理新用户逻辑
        if (userData.is_new === 1) {
            await this.handleNewUser(params);
            return;
        }

        // 处理推荐人信息
        if (params.refer) {
            await this.handleReferral(params.refer);
        }

        // 根据用户状态进行跳转
        await this.handleUserStatusNavigation(userData);
    }
}
```

### 4. 用户数据初始化Hook

创建了 `useUserInitialization` hook，在应用启动时自动恢复用户状态：

```typescript
// apps/ai.desk/src/hooks/useUserInitialization.ts
export const useUserInitialization = () => {
    const userStore = useUserStore();
    const wizardStore = useWizardStore();

    const initializeUserData = async () => {
        // 从本地存储恢复用户状态
        await userStore.syncFromStorage();

        // 如果用户已登录，恢复wizard状态
        if (userStore.isLoggedIn && userStore.profile) {
            const { register_setting } = userStore.profile;
            if (register_setting?.length > 0) {
                wizardStore.setRegisterSetting(register_setting);
                wizardStore.setState(0);
            }
        }
    };

    useMount(() => {
        initializeUserData();
    });

    return {
        isInitialized: userStore.isLoggedIn,
        isLoading: userStore.isLoading,
        user: userStore.profile,
    };
};
```

## 优化效果

### 1. 解决刷新页面数据丢失问题

- ✅ 使用 Zustand persist 中间件自动持久化
- ✅ 应用启动时自动恢复用户状态
- ✅ 避免重复的 API 调用

### 2. 简化代码结构

- ✅ 将复杂的跳转逻辑抽离到独立类中
- ✅ 统一用户状态管理
- ✅ 减少代码重复

### 3. 提高数据一致性

- ✅ 单一数据源
- ✅ 自动状态同步
- ✅ 减少数据不一致问题

### 4. 改善开发体验

- ✅ 更好的类型支持
- ✅ 更清晰的代码结构
- ✅ 更容易调试和维护

## 使用方式

### 1. 在组件中使用用户状态

```typescript
import useUserStore from "@/store/user";

const MyComponent = () => {
    const { profile, isLoggedIn, setProfile } = useUserStore();

    // 使用用户数据
    if (isLoggedIn && profile) {
        console.log("用户ID:", profile.profile_id);
    }

    // 更新用户数据
    const updateUser = () => {
        setProfile({ occupation_name: "新职业" });
    };
};
```

### 2. 在应用根组件中初始化

```typescript
import { useUserInitialization } from "@/hooks/useUserInitialization";

const App = () => {
    const { isInitialized, isLoading } = useUserInitialization();

    if (isLoading) {
        return <LoadingSpinner />;
    }

    return <Router />;
};
```

### 3. 处理登录逻辑

```typescript
import useUserStore from "@/store/user";

const LoginComponent = () => {
    const { login } = useUserStore();

    const handleLogin = async (token: string) => {
        try {
            await login(token);
            // 登录成功，用户状态会自动更新
        } catch (error) {
            console.error("登录失败:", error);
        }
    };
};
```

## 迁移指南

### 1. 替换原有的 register store

```typescript
// 旧代码
import useRegisterStore from "@/store/register";
const { setStepV3, setInterviewStatus } = useRegisterStore();

// 新代码
import useUserStore from "@/store/user";
const { setStepV3, setInterviewStatus } = useUserStore();
```

### 2. 更新路由守卫

```typescript
// 旧代码
const token = await window.localForage.getItem("cna-token");

// 新代码
const { token, isLoggedIn } = useUserStore();
```

### 3. 简化页面跳转逻辑

```typescript
// 旧代码 - 复杂的条件判断
if (data.is_new === 1) {
    if (type === "register") {
        navigate("/account/register", { replace: true });
    } else {
        navigate("/account/welcome", { replace: true });
    }
    return;
}

// 新代码 - 使用导航逻辑类
const navigationLogic = new NavigationLogic(navigate, userStore, wizardStore);
await navigationLogic.handleUserNavigation(userData, params);
```

## 注意事项

1. **向后兼容**：新的 store 保持了与原有 API 的兼容性
2. **渐进式迁移**：可以逐步迁移，不需要一次性替换所有代码
3. **数据迁移**：原有的本地存储数据会自动迁移到新的 store 中
4. **性能优化**：使用 persist 中间件避免不必要的重新渲染

## 总结

通过这次优化，我们解决了用户状态管理中的主要问题：

- ✅ 统一了数据管理
- ✅ 解决了刷新页面数据丢失问题
- ✅ 简化了代码结构
- ✅ 提高了数据一致性
- ✅ 改善了开发体验

这个方案为后续的功能开发提供了更好的基础，使得用户状态管理更加可靠和易于维护。
