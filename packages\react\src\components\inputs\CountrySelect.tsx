import {
    Group,
    Text,
    Select,
    MultiSelect,
    SelectProps,
    MultiSelectProps,
} from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import { forwardRef, useEffect, useState } from "react";
import FlagComponent from "../common/FlagComponent";
import React from "react";

type CountrySelectBaseProps<T = any> = {
    flagKey: keyof T;
    labelKey: keyof T;
    valueKey: keyof T;
    data: T[];
};

type CountrySelectSingleProps<T = any> = CountrySelectBaseProps<T> &
    Omit<SelectProps, "data"> & {
        multiple?: false;
    };

type CountrySelectMultiProps<T = any> = CountrySelectBaseProps<T> &
    Omit<MultiSelectProps, "data"> & {
        multiple: true;
    };

// 根据 multiple 值动态切换类型
type CountrySelectProps<T = any> =
    | CountrySelectSingleProps<T>
    | CountrySelectMultiProps<T>;


const CountrySelectComp = <T,>({
    data,
    flagKey,
    labelKey,
    valueKey,
    multiple,
    ...selectProps
}: CountrySelectProps<T>) => {
    const [flagName, setFlagName] = useState<string>("");

    const setFlagItem = useMemoizedFn(() => {
        const value = multiple
            ? Array.isArray(selectProps?.value)
                ? String(selectProps?.value[0])
                : undefined
            : String(selectProps?.value);

        if (value) {
            const flag_item = data.find(
                (item) => `${item[valueKey]}` === value
            );

            if (flag_item) {
                setFlagName(flag_item[flagKey] as string);
            }
        }
    });

    useEffect(() => {
        setFlagItem();
    }, [selectProps?.value, data]);

    useMount(() => {
        setFlagItem();
    });

    const commonProps = {
        ...selectProps,
        data: data?.map?.((item) => ({
            ...item,
            label: `${item[labelKey]}`,
            value: String(item[valueKey]),
        })) ?? [],
    };

    const renderOption = ({
        option,
        checked,
    }: {
        option: T & { label: string; value: string };
        checked: boolean;
    }) => {
        return (
            <Group
                className={`${checked && "tw-bg-neutral-100"} tw-w-full tw-p-2`}
            >
                <FlagComponent countryCode={`${option[flagKey]}`} />
                <Text>{option.label}</Text>
            </Group>
        );
    };

    if (multiple === true) {
        return (
            <MultiSelect
                {...commonProps as MultiSelectProps}
                classNames={{
                    ...selectProps?.classNames,
                    option: `tw-p-0 ${
                        (selectProps?.classNames as any)?.option ?? ""
                    }`,
                }}
                renderOption={renderOption}
            />
        );
    }

    return (
        <Select
            {...commonProps as SelectProps}
            classNames={{
                ...selectProps?.classNames,
                option: `tw-p-0 ${
                    (selectProps?.classNames as any)?.option ?? ""
                }`,
            }}
            renderOption={renderOption}
            leftSection={
                flagName.length > 0 && <FlagComponent countryCode={flagName} />
            }
        />
    );
};

const CountrySelect = forwardRef<HTMLDivElement, CountrySelectProps<any>>(
    (props, ref) => (
        <CountrySelectComp
            {...props}
        />
    )
) as <T>(
    props: CountrySelectProps<T> & { ref?: React.Ref<HTMLDivElement> }
) => React.ReactElement | null;

export default CountrySelect;
