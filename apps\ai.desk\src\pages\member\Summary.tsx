import api from "@/apis";
import CredentialModal from "@/components/modals/profile/CredentialModal";
import DetailModal from "@/components/modals/profile/DetailModal";
import BusinessCaseModal from "@/components/modals/profile/BusinessCaseModal";
import AICaseModal from "@/components/modals/profile/AICaseModal";
import { CnaButton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import {
    Paper,
    ScrollArea,
    Stack,
    Title,
    Text,
    Group,
    Image,
    Grid,
    Avatar,
    Card,
    SimpleGrid,
    Modal,
    Menu,
} from "@mantine/core";
import {
    BookBookmark,
    Bookmark,
    Books,
    NotePencil,
    PlusCircle,
    CaretDown,
    CaretUp,
    Plus,
    Robot,
    DotsThree,
} from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { useRequest, useSetState } from "ahooks";
import useProfileStore from "@/store/profile";
import useDataStore from "@code.8cent/store/data";
import { CertificateCard } from "@/components/modals/profile/CertificateCard";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { modals } from "@mantine/modals";
import useSettingStore from "@code.8cent/store/setting";
import { cnaRequest } from "@code.8cent/utils";

const Summary = () => {
    const profile = useProfileStore();
    const { lang } = useSettingStore();
    const { openFileView } = useFileViewer();

    const { countryDatas, industries, credentialTitles, skillTitles, areas } =
        useDataStore();

    const [expandedItems, setExpandedItems] = useState<{
        [key: string]: boolean;
    }>({});

    const toggleExpand = (key: string) => {
        setExpandedItems((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const [profileDetailVisible, setProfileDetailVisible] = useState(false);

    const [credentialDetail, setCredentialDetail] = useSetState<{
        visible: boolean;
        credential: any;
    }>({
        visible: false,
        credential: null,
    });

    // 个人信息
    const [profileInfo, setProfileInfo] = useState<any>({});
    // 牌照信息
    const [workLicenseInfo, setWorkLicenseInfo] = useState<any>([]);
    // 资格证书
    const [certificateInfo, setCertificateInfo] = useState<any>([]);
    // 业务案例
    const [businessCaseInfo, setBusinessCaseInfo] = useState<any>([]);
    // AI工具使用经验
    const [aiToolInfo, setAiToolInfo] = useState<any>([]);
    // 业务案例弹窗状态
    const [businessCaseModalVisible, setBusinessCaseModalVisible] =
        useState<boolean>(false);
    // AI工具使用经验弹窗状态
    const [aiToolModalVisible, setAiToolModalVisible] =
        useState<boolean>(false);
    // 业务案例下面各个案例的内容数据
    const [editBusinessCase, setEditBusinessCase] = useState<any>(null);
    // AI工具使用经验下面各个案例的内容数据
    const [editAiTool, setEditAiTool] = useState<any>(null);
    // 放大牌照状态
    const [licenseModalVisible, setLicenseModalVisible] =
        useState<boolean>(false);
    const [licenseImage, setLicenseImage] = useState<string>("");
    const [licenseImageScale, setLicenseImageScale] = useState<number>(1);

    // 获取个人简介信息
    const { run: getProfileInfo, loading: getProfileInfoLoading } = useRequest(
        async () => {
            const res = await api.profile.getProfileInfo();
            console.log("获取个人简介信息：", res);
            if (res) {
                setProfileInfo(res.userInfo);
                setWorkLicenseInfo(
                    res.workLicense.filter(
                        (item: any) => item && item.trim() !== ""
                    )
                );
                console.log("ssssss",res.businessCase)
                setCertificateInfo(res.certificate);
                setBusinessCaseInfo(res.businessCase);
                setAiToolInfo(res.AICase);
            }
        }
    );

    const {
        data: { certificates = [] } = {},
        refresh,
        run: getExperienceList,
    } = useRequest(
        async () => {
            const [certificates] = await Promise.all([
                api.profile.getExperienceList(1, profile.profileID),
            ]);

            return {
                certificates,
            };
        },
        {
            manual: true,
        }
    );

    const { run: getToken } = useRequest(
        async (id) => {
            const res = await api.profile.generateDocToken(id);
            if (res) {
                openFileView(
                    `${window.api_base_url}/api/v1/experience/previewDoc/${res}`
                );
            }
        },
        {
            manual: true,
        }
    );

    const { run: deleteExperience } = useRequest(
        async (id) => {
            const res = await api.profile.deleteExperience(id);
            if (res) {
                getExperienceList();
                noty.success("删除成功");
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (profile.profileID) {
            getExperienceList();
        }
    }, [profile]);

    // 语言列表
    const { data: languagesList = [] } = useRequest(async () => {
        let res = await cnaRequest<[]>("/api/v1/config/globalLanguages", "GET");

        if (res.result.code == 200) {
            return res.result.data;
        } else {
            return [];
        }
    });

    const { run: deleteBusinessCase } = useRequest(
        async (id) => {
            const res = await cnaRequest<any>(
                `/api/v1/businessCase/del/${id}`,
                "DELETE"
            );

            if (res) {
                noty.success("删除成功");
                getProfileInfo();
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Stack>
            <Title order={2}>个人简介</Title>
            <ScrollArea h={{ base: "calc(100vh - 140px)", md: "calc(100vh - 200px)" }}>
                <Paper
                    p="lg"
                    radius="md"
                    mb="lg"
                >
                    <div className="tw-bg-[#060D3D] tw-text-white tw-h-[100px] tw-rounded-t-[0.5rem] -tw-m-[20px]"></div>

                    <Group
                        align="start"
                        className="-tw-mt-[80px]"
                    >
                        <Avatar
                            radius={10}
                            src={
                                profileInfo.profileAvatar !=
                                "/storage/images/avatar/image-user.svg"
                                    ? `${window.api_base_url}${profileInfo.profileAvatar}`
                                    : "/images/station/default-avatar.svg"
                            }
                            className="tw-border-2 tw-size-[100px] md:tw-size-[160px] tw-border-[#F1F3F5]"
                            style={{
                                userSelect: "none",
                                WebkitUserSelect: "none",
                                MozUserSelect: "none",
                                msUserSelect: "none",
                                pointerEvents: "none",
                            }}
                        />

                        <Group
                            className="tw-flex-1"
                            justify="space-between"
                            align="center"
                        >
                            <Stack
                                gap="0"
                                className="tw-text-white"
                            >
                                <Text
                                    fw={600}
                                    className="tw-text-[20px]"
                                >
                                    {profileInfo.profileName}
                                </Text>
                                <Text>{profileInfo.profilePartnerCode}</Text>
                            </Stack>
                            <CnaButton
                                variant="outline"
                                color="white"
                                size="xs"
                                leftSection={<NotePencil size={16} />}
                                onClick={() => setProfileDetailVisible(true)}
                            >
                                编辑
                            </CnaButton>
                        </Group>
                    </Group>

                    <Stack
                        gap="xs"
                        className="md:tw-ml-[174px] md:-tw-mt-[60px] tw-ml-0 tw-mt-2"
                    >
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">性别：</Text>
                                <Text>
                                    {profileInfo.profileGender === "M"
                                        ? "男"
                                        : "女"}
                                </Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">出生年月：</Text>
                                <Text>{profileInfo.profileBirthDate}</Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">电话：</Text>
                                <Text>{profileInfo.profileContact}</Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">邮箱：</Text>
                                <Text>{profileInfo.profileEmail}</Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">语言：</Text>
                                <Text>
                                    {profile.settingLanguageAbility?.map(
                                        (item) => {
                                            return (
                                                languagesList.find(
                                                    (language) =>
                                                        language.code === item
                                                )?.[`name${lang}`] + " "
                                            );
                                        }
                                    )}
                                </Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">国籍：</Text>
                                <Text>
                                    {countryDatas.find(
                                        (item) =>
                                            item.countryID ===
                                            profile.profileNationalityID
                                    )?.countryZH || "-"}
                                </Text>
                            </Group>
                        </SimpleGrid>
                        <SimpleGrid cols={{ base: 1, md: 2 }}>
                            <Group gap="0">
                                <Text c="dimmed">当前职位：</Text>
                                <Text>{profileInfo.position}</Text>
                            </Group>
                            <Group gap="0">
                                <Text c="dimmed">从业年限：</Text>
                                <Text>{profileInfo.work_year}</Text>
                            </Group>
                        </SimpleGrid>
                        <Group>
                            <Group gap="0">
                                <Text c="dimmed">最高学历：</Text>
                                <Text>
                                    {`${profileInfo.edu} - ${profileInfo.eduSubject}`}
                                </Text>
                            </Group>
                            <Text c="dimmed">
                                {(() => {
                                    if (!profileInfo.eduDatetime) {
                                        return "XXXX-XX ~ XXXX-XX";
                                    }

                                    // 格式化月份时间段显示
                                    const formatMonthRange = (
                                        dateRange: string
                                    ) => {
                                        if (
                                            !dateRange ||
                                            !dateRange.includes("-")
                                        ) {
                                            return "XXXX-XX ~ XXXX-XX";
                                        }

                                        const [startDate, endDate] =
                                            dateRange.split("-");

                                        // 将 YYYY.MM 格式转换为 YYYY年MM月 格式
                                        const formatToChinese = (
                                            dateStr: string
                                        ) => {
                                            if (
                                                !dateStr ||
                                                dateStr.length !== 7
                                            ) {
                                                return "XXXX-XX";
                                            }
                                            const [year, month] =
                                                dateStr.split(".");
                                            if (!year || !month) {
                                                return "XXXX-XX";
                                            }
                                            return `${year}-${month}`;
                                        };

                                        return `${formatToChinese(
                                            startDate
                                        )} ~ ${formatToChinese(endDate)}`;
                                    };

                                    return formatMonthRange(
                                        profileInfo.eduDatetime
                                    );
                                })()}
                            </Text>
                        </Group>
                        <Group
                            align="flex-start"
                            gap="0"
                        >
                            <Text
                                c="dimmed"
                                w={86}
                                style={{ flexShrink: 0 }}
                            >
                                个人介绍：
                            </Text>
                            <Text style={{ flex: 1, wordBreak: "break-all" }}>
                                {profileInfo.profileDesc}
                            </Text>
                        </Group>
                    </Stack>
                </Paper>
                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group>
                        <Bookmark size={20} />
                        <Text fw={500}>获得牌照</Text>
                    </Group>
                    <Grid
                        mt="lg"
                        gutter="lg"
                    >
                        {workLicenseInfo.map((item: any, index: any) => (
                            <Grid.Col
                                span={{ base: 12, md: 4 }}
                                key={index}
                            >
                                <Image
                                    src={`https://doc.corporate-advisory.cn/${item}`}
                                    style={{ cursor: "pointer" }}
                                    draggable={false}
                                    onClick={() => {
                                        // setLicenseImage(
                                        //     `https://doc.corporate-advisory.cn/${item}`
                                        // );
                                        // setLicenseModalVisible(true);
                                        openFileView(
                                            `${window.api_base_url}/api/v1/file/resource?path=${item}&type=remote`
                                        );
                                    }}
                                />
                            </Grid.Col>
                        ))}
                    </Grid>
                    {workLicenseInfo.length === 0 && (
                        <Text
                            c="dimmed"
                            className="tw-text-center"
                        >
                            暂未获得牌照，快去考试吧~
                        </Text>
                    )}
                    {/* 放大牌照 */}
                    {/* <Modal
                        opened={licenseModalVisible}
                        onClose={() => setLicenseModalVisible(false)}
                        fullScreen
                        centered
                    >
                        <Image
                            src={licenseImage}
                            style={{
                                maxWidth: "100%",
                                maxHeight: "100%",
                                transform: `scale(${licenseImageScale})`,
                                transition: "transform 0.2s",
                            }}
                            onWheel={(e) => {
                                e.preventDefault();
                                if (e.deltaY < 0)
                                    setLicenseImageScale((s) =>
                                        Math.min(5, s + 0.1)
                                    );
                                else
                                    setLicenseImageScale((s) =>
                                        Math.max(0.2, s - 0.1)
                                    );
                            }}
                        />
                    </Modal> */}
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group mb="md">
                        <BookBookmark size={20} />
                        <Text fw={500}>资格证书</Text>
                    </Group>

                    <SimpleGrid cols={{ base: 1, md: 1, lg: 2, xl: 3 }}>
                        {certificates?.map((certificate, index) => (
                            <CertificateCard
                                key={index}
                                title={
                                    credentialTitles?.find(
                                        (item) =>
                                            item.id ===
                                            certificate.experience_id
                                    )?.["title_" + lang.toLowerCase()]
                                }
                                date={certificate.time_range}
                                region={
                                    countryDatas?.find(
                                        (item) =>
                                            item.countryID ===
                                            certificate.country_id
                                    )?.["country" + lang]
                                }
                                fields={
                                    industries?.find(
                                        (item) =>
                                            item.id === certificate.industry_id
                                    )?.["title_" + lang.toLowerCase()]
                                }
                                isFileView={async () => {
                                    if (certificate.file) {
                                        getToken(certificate.id);
                                    } else {
                                        noty.error("暂无上传证书文件");
                                    }
                                }}
                                onEdit={() => {
                                    setCredentialDetail({
                                        visible: true,
                                        credential: certificate,
                                    });
                                }}
                                onDelete={() => {
                                    modals.openConfirmModal({
                                        id: "",
                                        title: "删除资格证书",
                                        children: (
                                            <Text>
                                                确定要删除这个资格证书？
                                            </Text>
                                        ),
                                        labels: {
                                            confirm: "确定",
                                            cancel: "取消",
                                        },
                                        confirmProps: { color: "basic" },
                                        closeOnConfirm: true,
                                        onConfirm: async () => {
                                            deleteExperience(certificate.id);
                                        },
                                    });
                                }}
                            />
                        ))}

                        <Card
                            radius="md"
                            p="md"
                            className="hover:tw-bg-[#F1F3FA] tw-border-2 tw-border-gray-100 tw-border-dashed tw-h-full"
                        >
                            <Stack
                                gap={0}
                                justify="center"
                                align="center"
                                className="tw-h-full"
                                c="#060D3D"
                                onClick={() =>
                                    setCredentialDetail({
                                        visible: true,
                                        credential: null,
                                    })
                                }
                            >
                                <PlusCircle size={40} />
                                <Text>添加</Text>
                            </Stack>
                        </Card>
                    </SimpleGrid>
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                    mb="lg"
                >
                    <Group
                        mb="md"
                        justify="space-between"
                    >
                        <Group>
                            <Books size={20} />
                            <Text fw={500}>业务案例</Text>
                        </Group>
                        <Plus
                            size={20}
                            className="tw-cursor-pointer"
                            onClick={() => {
                                setEditBusinessCase(null);
                                setBusinessCaseModalVisible(true);
                            }}
                        />
                    </Group>
                    <Stack gap="md">
                        {businessCaseInfo.map((item1: any, index1: any) => (
                            <Paper
                                key={index1}
                                p="lg"
                                radius="md"
                                className="tw-bg-[#F1F3F5]"
                            >
                                <Group
                                    justify="space-between"
                                    wrap="nowrap"
                                >
                                    <Text
                                        c="#060D3D"
                                        fw={600}
                                    >
                                        {item1.name}
                                    </Text>
                                    <div className="tw-cursor-pointer">
                                        <Menu>
                                            <Menu.Target>
                                                <DotsThree size={20} />
                                            </Menu.Target>
                                            <Menu.Dropdown>
                                                <Menu.Item
                                                    onClick={() => {
                                                        setEditBusinessCase(
                                                            item1
                                                        );
                                                        setBusinessCaseModalVisible(
                                                            true
                                                        );
                                                    }}
                                                    className="tw-px-[18px] tw-py-[6px]"
                                                >
                                                    编辑
                                                </Menu.Item>
                                                <Menu.Item
                                                    onClick={() => {
                                                        deleteBusinessCase(
                                                            item1.id
                                                        );
                                                    }}
                                                    className="tw-text-[#DA0101] tw-px-[18px] tw-py-[6px]"
                                                >
                                                    删除
                                                </Menu.Item>
                                            </Menu.Dropdown>
                                        </Menu>
                                    </div>
                                </Group>
                                <Text
                                    c="dimmed"
                                    mb="md"
                                >
                                    {item1.date}
                                </Text>
                                <Stack gap="xs">
                                    <Group gap="0">
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            项目地点：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                            }}
                                        >
                                            {item1.address}
                                        </Text>
                                    </Group>
                                    <Group gap="0">
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            行业领域：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                            }}
                                        >
                                            家电制造、资源管理、生产管理、经济管理、制造管理
                                        </Text>
                                    </Group>
                                    <Group
                                        align="flex-start"
                                        gap="0"
                                    >
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            服务内容：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                                display: "-webkit-box",
                                                WebkitLineClamp: expandedItems[
                                                    `service${index1}`
                                                ]
                                                    ? undefined
                                                    : 1,
                                                WebkitBoxOrient: "vertical",
                                                overflow: "hidden",
                                            }}
                                        >
                                            {item1.content}
                                        </Text>
                                        <CnaButton
                                            variant="transparent"
                                            color="#19288F"
                                            size="xs"
                                            onClick={() =>
                                                toggleExpand(`service${index1}`)
                                            }
                                            leftSection={
                                                expandedItems[
                                                    `service${index1}`
                                                ] ? (
                                                    <CaretUp size={16} />
                                                ) : (
                                                    <CaretDown size={16} />
                                                )
                                            }
                                        >
                                            {expandedItems[`service${index1}`]
                                                ? "收起"
                                                : "展开"}
                                        </CnaButton>
                                    </Group>
                                    <Group
                                        align="flex-start"
                                        gap="0"
                                    >
                                        <Text
                                            c="dimmed"
                                            w={85}
                                            style={{ flexShrink: 0 }}
                                        >
                                            成果简述：
                                        </Text>
                                        <Text
                                            style={{
                                                flex: 1,
                                                wordBreak: "break-all",
                                                display: "-webkit-box",
                                                WebkitLineClamp: expandedItems[
                                                    `result${index1}`
                                                ]
                                                    ? undefined
                                                    : 1,
                                                WebkitBoxOrient: "vertical",
                                                overflow: "hidden",
                                            }}
                                        >
                                            {item1.result}
                                        </Text>
                                        <CnaButton
                                            variant="transparent"
                                            color="#19288F"
                                            size="xs"
                                            onClick={() =>
                                                toggleExpand(`result${index1}`)
                                            }
                                            leftSection={
                                                expandedItems[
                                                    `result${index1}`
                                                ] ? (
                                                    <CaretUp size={16} />
                                                ) : (
                                                    <CaretDown size={16} />
                                                )
                                            }
                                        >
                                            {expandedItems[`result${index1}`]
                                                ? "收起"
                                                : "展开"}
                                        </CnaButton>
                                    </Group>
                                </Stack>
                            </Paper>
                        ))}
                    </Stack>
                </Paper>

                <Paper
                    p="lg"
                    radius="md"
                    styles={{
                        root: {
                            borderTop: "4px solid transparent",
                            background:
                                "linear-gradient(white, white) padding-box, linear-gradient(to right, #243081, #727AAF) border-box",
                        },
                    }}
                >
                    <Group
                        mb="md"
                        justify="space-between"
                    >
                        <Group>
                            <Robot size={20} />
                            <Text fw={500}>AI工具使用经验</Text>
                        </Group>
                        <Plus
                            size={20}
                            className="tw-cursor-pointer"
                            onClick={() => {
                                setEditAiTool(null);
                                setAiToolModalVisible(true);
                            }}
                        />
                    </Group>
                    <Stack gap="md">
                        {aiToolInfo.map((item1: any, index: any) => (
                            <Paper
                                p="lg"
                                radius="md"
                                className="tw-bg-[#F1F3F5]"
                                key={index}
                            >
                                <Group
                                    justify="space-between"
                                    mb="md"
                                    wrap="nowrap"
                                >
                                    <Text
                                        c="#060D3D"
                                        fw={600}
                                    >
                                        {item1.name}
                                    </Text>
                                    <div className="tw-cursor-pointer">
                                        <Menu>
                                            <Menu.Target>
                                                <DotsThree size={20} />
                                            </Menu.Target>
                                            <Menu.Dropdown>
                                                <Menu.Item
                                                    onClick={() => {
                                                        setEditAiTool(item1);
                                                        setAiToolModalVisible(
                                                            true
                                                        );
                                                    }}
                                                    className="tw-px-[18px] tw-py-[6px]"
                                                >
                                                    编辑
                                                </Menu.Item>
                                                <Menu.Item
                                                    onClick={() => {
                                                        deleteBusinessCase(
                                                            item1.id
                                                        );
                                                    }}
                                                    className="tw-text-[#DA0101] tw-px-[18px] tw-py-[6px]"
                                                >
                                                    删除
                                                </Menu.Item>
                                            </Menu.Dropdown>
                                        </Menu>
                                    </div>
                                </Group>
                                <Text>{item1.content}</Text>
                            </Paper>
                        ))}
                    </Stack>
                </Paper>
            </ScrollArea>

            {/* 个人简介弹窗 */}
            <DetailModal
                isVisible={profileDetailVisible}
                onClose={() => {
                    setProfileDetailVisible(false);
                    // 刷新个人简介信息
                    getProfileInfo();
                }}
                onSuccess={getProfileInfo}
            />

            {/* 资格证书弹窗 */}
            <CredentialModal
                isVisible={credentialDetail.visible}
                credential={credentialDetail.credential}
                onClose={() =>
                    setCredentialDetail({ visible: false, credential: null })
                }
                onSave={() => {
                    refresh();
                }}
            />

            {/* 添加业务案例弹窗 */}
            <BusinessCaseModal
                isVisible={businessCaseModalVisible}
                onClose={() => setBusinessCaseModalVisible(false)}
                onSuccess={getProfileInfo}
                editItem={editBusinessCase}
            />

            {/* 添加AI工具使用经验弹窗 */}
            <AICaseModal
                isVisible={aiToolModalVisible}
                onClose={() => setAiToolModalVisible(false)}
                onSuccess={getProfileInfo}
                editItem={editAiTool}
            />
        </Stack>
    );
};

export default Summary;
