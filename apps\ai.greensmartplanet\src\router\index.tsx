import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON>, Navigate } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import RootLayout from "@/components/layouts/RootLayout";
import SettingsLayout from "@/components/layouts/SettingsLayout";
import IndexLayout from "@/components/layouts/IndexLayout";
import LoginPage from "@/pages/account/LoginPage";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
// import SettingPage from "@code.8cent/react/pages/SettingPage";
import withRouteGuard from "@code.8cent/react/hoc/withRouteGuard";
import MemberPage from "@/pages/member";
import SettingPage from "@/pages/account/settingPage";
import AccountWizardPage from "@/pages/wizard";
import AccountWizardPage2 from "@/pages/wizard/index2";
import IndexPage from "@/pages/front";
import DeveloperPage from "@/pages/category/developer";

import UserPage from "@/pages/setting/user";
import SecurityPage from "@/pages/setting/security";
import RegionPage from "@/pages/setting/region";
import NotificationsPage from "@/pages/setting/notifications";
import LogsPage from "@/pages/setting/logs";
import ManagerPage from "@/pages/setting/manager";
import PaymentPage from "@/pages/setting/payment";
import SupportPage from "@/pages/setting/support";
import TabsPage from "@/pages/setting/tabs";

const GuardedRootLayout = withRouteGuard(RootLayout, {
    publicRoutes: ["/account/*", "/member/*"],
});

const router = createBrowserRouter([
    {
        path: "/",
        // element: <GuardedRootLayout />,
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        children: [
            {
                path: "account/login",
                element: <LoginPage />,
            },
            {
                path: "account/forget-password",
                element: <ForgetPasswordPage />,
            },
            {
                path: "account/wizard",
                element: <AccountWizardPage />,
            },
            {
                path: "account/wizard2",
                element: <AccountWizardPage2 />,
            },
            {
                path: "/index",
                element: <IndexPage />,
            },
            {
                path: "settings",
                element: <SettingsLayout />,
                children: [
                    {
                        path: "user",
                        element: <UserPage />,
                    },
                    {
                        path: "security",
                        element: <SecurityPage />,
                    },
                    {
                        path: "region",
                        element: <RegionPage />,
                    },
                    {
                        path: "notifications",
                        element: <NotificationsPage />,
                    },
                    {
                        path: "logs",
                        element: <LogsPage/>,
                    },
                    {
                        path: "manager",
                        element: <ManagerPage />,
                    },
                    {
                        path: "payment",
                        element: <PaymentPage />,
                    },
                    {
                        path: "support",
                        element: <SupportPage />,
                    },
                    {
                        path: "tabs",
                        element: <TabsPage />,
                    },
                    

                    {
                        path: "*",
                        errorElement: (
                            <RouteError className="tw-h-[100%] tw-w-[100%]" />
                        ),
                        loader: () => {
                            throw json(
                                {},
                                { status: 404, statusText: "Page Not Found" }
                            );
                        },
                    },
                ],
            },
            {
                path: "member",
                element: <IndexLayout />,
                children: [
                    {
                        path: "index",
                        element: <MemberPage />,
                    },
                    {
                        path: "*",
                        errorElement: (
                            <RouteError className="tw-h-[100%] tw-w-[100%]" />
                        ),
                        loader: () => {
                            throw json(
                                {},
                                { status: 404, statusText: "Page Not Found" }
                            );
                        },
                    },
                ],
            },
            {
                path: "/category/developer",
                element: <DeveloperPage />,
            },
            // {
            //     path: "member",
            //     element: <DashboardLayout />,
            //     children: [
            //         {
            //             path: "settings",
            //             element: <SettingPage />,
            //         },
            //         {
            //             path: "*",
            //             errorElement: <RouteError className="tw-h-[100%] tw-w-[100%]" />,
            //             loader: () => {
            //                 throw json({}, { status: 404, statusText: "Page Not Found" });
            //             },
            //         },
            //     ],
            // },
        ],
    },
    {
        path: "*",
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        loader: () => {
            throw json({}, { status: 404, statusText: "Page Not Found" });
        },
    },
]);

export default router;
