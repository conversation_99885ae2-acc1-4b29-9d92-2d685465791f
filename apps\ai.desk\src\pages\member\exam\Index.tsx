import AssociateFlowLayout from "@/components/layouts/AssociateFlowLayout";
import ApplyOptions from "@/components/member/exam/ApplyOptions";
import Statement from "@/components/member/exam/Statement";
import Start from "@/components/member/exam/Start";
import Fail from "@/components/member/exam/Fail";
import Success from "@/components/member/exam/Success";
import Answering from "@/components/member/exam/Answering";
import Documents from "@/components/member/exam/Documents";
import Payment from "@/components/member/exam/Payment";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useMemoizedFn, useRequest } from "ahooks";
import api from "@/apis";
import useProfileStore from "@/store/profile";
import useBusinessStore, {
    setUpgradeExamSection,
    UpgradeExamSection,
} from "@/store/business";

const ExamIndex = () => {
    const upgradeExamSection = useBusinessStore.use.upgradeExamSection();

    const navigate = useNavigate();

    // 获取当前业务进程
    const { run: getCurrentProcess } = useRequest(
        async () => {
            try {
                const res = await api.exam.getCurrentProcess();
                let project_code = res.project_code || undefined;
                if (project_code === "profile_upgrade_partner") {
                    setUpgradeExamSection("upgrade-exam-payment");
                } else if (project_code === "profile_upgrade_partner_manager") {
                    setUpgradeExamSection("upgrade-exam-start");
                }
            } catch (error) {
                console.error("获取当前业务进程失败：", error);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        // getCurrentProcess();

        // 获取当前升级考试部分
        const upgradeExamSection = sessionStorage.getItem("upgradeExamSection");
        if (upgradeExamSection) {
            setUpgradeExamSection(upgradeExamSection as UpgradeExamSection);
        }
    }, []);

    const toMemberIndex = useMemoizedFn(() => {
        navigate("/member/index", { replace: true });
    });

    const getPageContent = () => {
        switch (upgradeExamSection) {
            case "upgrade-exam-statement":
                return (
                    <Statement
                        onNext={() =>
                            setUpgradeExamSection("upgrade-exam-apply-options")
                        }
                    />
                );
            case "upgrade-exam-apply-options":
                return (
                    <ApplyOptions
                        onPrev={() =>
                            setUpgradeExamSection("upgrade-exam-statement")
                        }
                        toStart={async () => {
                            try {
                                const res = await api.exam.addProcess(
                                    "profile_upgrade_partner_manager"
                                );
                                if (res.status) {
                                    setUpgradeExamSection("upgrade-exam-start");
                                }
                            } catch (error) {
                                console.error("添加进程失败：", error);
                            }
                        }}
                        toPayment={async () => {
                            try {
                                const res = await api.exam.addProcess(
                                    "profile_upgrade_partner"
                                );

                                await window.localForage.setItem(
                                    "upgrade_payment_process_no",
                                    res.data?.process_order_no
                                );

                                if (res.status) {
                                    setUpgradeExamSection(
                                        "upgrade-exam-payment"
                                    );
                                }
                            } catch (error) {
                                console.error("添加进程失败：", error);
                            }
                        }}
                    />
                );
            case "upgrade-exam-payment":
                return (
                    <Payment
                        onPrev={async () => {
                            await api.exam.withdrawProcess(
                                "profile_upgrade_partner"
                            );
                            await window.localForage.removeItem(
                                "upgrade_payment_process_no"
                            );
                            setUpgradeExamSection("upgrade-exam-apply-options");
                        }}
                        toIndex={toMemberIndex}
                    />
                );
            case "upgrade-exam-start":
                return (
                    <Start
                        onPrev={async () => {
                            await api.exam.withdrawProcess(
                                "profile_upgrade_partner_manager"
                            );
                            setUpgradeExamSection("upgrade-exam-apply-options");
                        }}
                        onNext={() =>
                            setUpgradeExamSection("upgrade-exam-answering")
                        }
                    />
                );
            case "upgrade-exam-answering":
                return (
                    <Answering
                        onPrev={() =>
                            setUpgradeExamSection("upgrade-exam-start")
                        }
                        toSuccess={() =>
                            setUpgradeExamSection("upgrade-exam-success")
                        }
                        toFail={() =>
                            setUpgradeExamSection("upgrade-exam-fail")
                        }
                    />
                );
            case "upgrade-exam-success":
                return (
                    <Success
                        onNext={async () => {
                            await api.exam.submitExam();
                            // 设置 profile store has_manager_power 为 1
                            useProfileStore
                                .getState()
                                .setProfileValue("has_manager_power", 1);
                            setUpgradeExamSection("upgrade-exam-documents");
                        }}
                    />
                );
            case "upgrade-exam-fail":
                return (
                    <Fail
                        onPrev={() =>
                            setUpgradeExamSection("upgrade-exam-start")
                        }
                        onRetry={() =>
                            setUpgradeExamSection("upgrade-exam-answering")
                        }
                    />
                );
            case "upgrade-exam-documents":
                return <Documents toIndex={toMemberIndex} />;
        }
    };

    return <AssociateFlowLayout>{getPageContent()}</AssociateFlowLayout>;
};

export default ExamIndex;
