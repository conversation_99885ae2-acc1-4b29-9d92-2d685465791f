<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130.2 130.2">
<style>
.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
}
.circle {
  -webkit-animation: dash .9s ease-in-out;
  animation: dash .9s ease-in-out;
}
.line {
  stroke-dashoffset: 1000;
  -webkit-animation: dash .9s .35s ease-in-out forwards;
  animation: dash .9s .35s ease-in-out forwards;
}
.check {
  stroke-dashoffset: -100;
  -webkit-animation: dash-check .9s .35s ease-in-out forwards;
  animation: dash-check .9s .35s ease-in-out forwards;
}
.alert {
  stroke-dashoffset: -100;
  -webkit-animation: dash-alert .9s .35s ease-in-out forwards;
  animation: dash-alert .9s .35s ease-in-out forwards;
}
@-webkit-keyframes dash {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes dash {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@-webkit-keyframes dash-check {
  0% {
    stroke-dashoffset: -100;
  }
  100% {
    stroke-dashoffset: 900;
  }
}
@keyframes dash-alert {
  0% {
    stroke-dashoffset: -100;
  }
  100% {
    stroke-dashoffset: 900;
  }
}
@-webkit-keyframes dash-alert {
  0% {
    stroke-dashoffset: -200;
  }
  100% {
    stroke-dashoffset: 1000;
  }
}
@keyframes dash-check {
  0% {
    stroke-dashoffset: -200;
  }
  100% {
    stroke-dashoffset: 1000;
  }
}
</style>
	<circle class="path circle" fill="#FFECB5" stroke="#FFC107" stroke-width="6" stroke-miterlimit="10" cx="65" cy="65" r="60.376"/>
	<path class="path alert" fill="#FFECB5" stroke="#FFC107" stroke-width="10" stroke-linecap="round" stroke-miterlimit="10" d="M64.997,74.721v-43.18"/>
	<circle class="path alert" fill="#FFC107" stroke="#FFC107" stroke-width="10" stroke-linecap="round" stroke-miterlimit="10" cx="64.997" cy="88.492" r="0.729"/>
</svg>
