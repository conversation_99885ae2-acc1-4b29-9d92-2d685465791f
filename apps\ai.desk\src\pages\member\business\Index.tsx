import useBusinessStore, { setBusinessSection } from "@/store/business";
import UnlicensedIntro from "./components/unlicensed/Intro";
import UnlicensedCourses from "./components/unlicensed/Courses";
import UnlicensedLearn from "./components/unlicensed/Learn";
import UnlicensedExam from "./components/unlicensed/Exam";
import LicensedList from "./components/licensed/List";
import BasicLicensedList from "./components/licensed/BasicList";
import GreenEarth from "./components/licensed/GreenEarth";
import Pay from "./components/unlicensed/Pay";
import UnlicensedOverduePay from "./components/unlicensed/OverduePay";
import { useUnmount, useRequest } from "ahooks";
import api from "@/apis";
import { useEffect } from "react";

const Index = () => {
    const businessSection = useBusinessStore.use.businessSection();

    useUnmount(() => {
        if (businessSection === "unlicensed-exam") {
            // 退出考试页面，撤回考试进程
            api.exam.withdrawProcess("partner_franchise_handbook_license");
        }
    });

    // 暂时这样处理，后续需要优化
    const { run: getUnLicensedExamResult } = useRequest(
        async () => {
            try {
                const res = await api.business.getExamResult("56");
                if (res.pass === 1) {
                    setBusinessSection("licensed-list");
                }
            } catch (error) {
                console.log("error", error);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (businessSection === "unlicensed-intro") {
            getUnLicensedExamResult();
        }
    }, [businessSection]);

    const getPageContent = () => {
        switch (businessSection) {
            case "unlicensed-intro":
                return <UnlicensedIntro />;
            case "unlicensed-courses":
                return <UnlicensedCourses />;
            case "unlicensed-learn":
                return <UnlicensedLearn />;
            case "unlicensed-exam":
                return <UnlicensedExam />;
            case "licensed-list":
                return <LicensedList />;
            case "basic-licensed-list":
                return <BasicLicensedList />;
            case "green-earth":
                return <GreenEarth />;
            case "professional-licensed-pay":
                return <Pay />;
            case "unlicensed-overdue-pay":
                return <UnlicensedOverduePay />;
        }
    };

    return <>{getPageContent()}</>;
};

export default Index;
