import cnaRequest from "@code.8cent/utils/cnaRequest";
import noty from "@code.8cent/react/noty";

// 添加分页参数接口
interface LoginLogParams {
    pageSize: number;
    page: number;
}

// 添加接口定义
interface NotificationSettings {
    settingNotifyType?: number;
    settingNotifySafeUpdated?: number;
    settingNotifyEmergency?: number;
    settingNotifySuspiciousOperation?: number;
    settingNotifyRecPrivateMsg?: number;
    settingNotifyImportanceUpdate?: number;
    settingNotifyJoinInvestigate?: number;
    settingNotifySystemUpdate?: number;
}

// 修改登录日志响应接口
interface LoginLogResponse {
    list: Array<{
        id: number;
        profile_id: number;
        token_status: number;
        device: string;
        ip: string;
        created_at: string;
        updated_at: string;
        current: number;
        create_time: number;
    }>;
    paginate: {
        currentPage: number;
        perPage: number;
        totalRecord: number;
        totalPage: number;
    };
}

// 添加 LogItem 接口定义
interface LogItem {
    id: number;
    active_type: number;
    obj_type: number;
    created_at: string;
    descZH: string;
    descZT: string;
    descEN: string;
    descMS: string;
    create_time: number;
}

// 活动日志响应接口
interface ActiveLogResponse {
    list: LogItem[];
    total: number;
}

// 在接口定义区域添加新的接口
export interface TemplateItem {
    TemplateID: number;
    WebType: string;
    en: string;
    ms: string;
    zh: string;
    zt: string;
}

export interface TemplateListResponse {
    data: {
        TemplateID: string;
        WebType: string;
        EN: string;
        MS: string;
        ZH: string;
        ZT: string;
    }[];
}

// 添加模板内容项接口
interface TemplateContentItem {
    StructureListID: number;
    ENName: string;
    MSName: string;
    ZHName: string;
    ZTName: string;
    EN: string;
    MS: string;
    ZH: string;
    ZT: string;
    checked: number;
}

interface TemplateContentResponse {
    status: boolean;
    code: number;
    message: string;
    data: TemplateContentItem[];
}

// 添加保存模板的参数接口
interface SaveTemplateParams {
    templateId?: number;
    WebTab1?: number;
    WebTab2?: number;
    WebTab3?: number;
    WebTab4?: number;
    WebTab5?: number;
    WebTab6?: number;
    WebTab7?: number;
    WebTab8?: number;
    WebTab9?: number;
}

const setting = {
    getNotifyType: async () => {
        let { result, error } = await cnaRequest(
            "/api/v1/setting/notification/notifyType",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    updateNotification: async (settings: NotificationSettings) => {
        const formData = new FormData();

        // 遍历所有设置项并添加到 FormData
        Object.entries(settings).forEach(([key, value]) => {
            if (value !== undefined) {
                formData.append(key, value.toString());
            }
        });

        let { result, error } = await cnaRequest<string>(
            "/api/v1/setting/notification/update",
            "POST",
            formData
        );

        if (error) {
            return null;
        } else {
            return true;
        }
    },

    // 添加获取登录足迹列表的方法
    getLoginLogList: async (params: LoginLogParams) => {
        const formData = new FormData();
        formData.append("pageSize", params.pageSize.toString());
        formData.append("page", params.page.toString());

        let { result, error } = await cnaRequest<LoginLogResponse>(
            "/api/v1/setting/loginLog/list",
            "POST",
            formData
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    quitLoginLog: async (id: number = 0) => {
        const formData = new FormData();
        formData.append("id", id.toString());

        let { result, error } = await cnaRequest(
            "/api/v1/setting/loginLog/quit",
            "POST",
            formData
        );

        if (!error) {
            return true;
        } else {
            return null;
        }
    },

    getActiveLogList: async (params: LoginLogParams) => {
        const formData = new FormData();
        formData.append("pageSize", params.pageSize.toString());
        formData.append("page", params.page.toString());

        let { result, error } = await cnaRequest<ActiveLogResponse>(
            "/api/v1/setting/activeLog",
            "POST",
            formData
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    // 添加获取模板列表的方法
    getTemplateList: async () => {
        let { result, error } = await cnaRequest(
            "/api/v1/template/list",
            "GET"
        );

        if (!error && result?.data) {
            return result.data;
        }
        return [];
    },

    // 添加获取模板内容的方法
    getTemplateContent: async (templateId: string) => {
        let { result, error } = await cnaRequest<
            TemplateContentResponse["data"]
        >("/api/v1/template/content", "GET", { templateId });

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    // 添加保存模板的方法
    saveTemplate: async (params: SaveTemplateParams) => {
        const formData = new FormData();

        // 遍历所有参数并添加到 FormData
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined) {
                formData.append(key, value.toString());
            }
        });

        let { result, error } = await cnaRequest(
            "/api/v1/template/save",
            "POST",
            formData
        );

        return result;

        // if (!error) {
        //     return true;
        // } else {

        //     return null;
        // }
    },
};

export default setting;
