.team-page-wrapper {
    min-height: 100vh;
    background-color: #000033;
    /* 深蓝色背景 */
    position: relative;
    height: 200%;
    top: -180%;
}

/* 添加左侧 AI 机器人背景 */
.team-page-wrapper::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40%;
    height: 100%;
    background: url('/images/team/ai.jpg') no-repeat left bottom;
    background-size: contain;
    z-index: 1;
}

/* 添加右侧人像背景 */
.team-page-wrapper::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 40%;
    height: 100%;
    /* background: url('/images/team/person.jpg') no-repeat right bottom; */
    background-size: contain;
    z-index: 1;
}

.logo-desktop {
    position: absolute;
    top: 24px;
    left: 40px;
    z-index: 10;
    display: none;
}

.logo-mobile {
    position: absolute;
    top: -8px;
    left: 43%;
    z-index: 10;
    display: none;
}

.team-page-container {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0.5rem;
    overflow-y: auto;
    min-height: 100vh;
    /* position: relative; */
    z-index: 2;
}

.ai-image {
    /* display: none; */
    position: relative;
    bottom: -12px;
    align-self: flex-end;
    height: 600px;
    z-index: 20;
    margin-left: -10rem;
    margin-right: -2.5rem;
}


.content-stack {
    letter-spacing: 3px;
    text-align: justify;
    position: relative;
    z-index: 10;
    padding: 2rem;
    flex: 1;
    margin: 0 auto;
    min-height: 500px;
    max-width: 800px;
}

/* 信封容器的样式 */
.envelope-container {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: 764px;
    /* 修改默认宽高比 */
    aspect-ratio: 764/500;
    /* 增加了高度 */
    perspective: 2000px;
}

.envelope-part {
    position: absolute;
    background-size: 100% 100%;
    transition: all 2s ease;
    will-change: transform;
}

.envelope-part.top.open {
    transform: rotateX(180deg);
    /* 翻转信封顶部 */
    z-index: 1;
    /* 确保信纸显示在信封顶部下方 */
    transition: all 0.5s ease;
}


/* 信封正面 */
.envelope-part.front {
    position: absolute;
    /* top: 52%;  */
    top: 0%;
    left: 0;
    width: 100%;
    height: 101%;
    /* background: url('/images/envelop-front1.png') no-repeat; */
    /* background-size: cover; */
    background-size: inherit;
    z-index: 20;
    /* 在信纸和顶部之间 */
    transition: all 1s ease;
}

.envelope-part.top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 62%;
    background: url('/images/envelop-top.png') no-repeat;
    background-size: 100% 100%;
    transform-origin: top;
    /* 从顶部开始翻转 */
    transform-style: preserve-3d;
    /* transition: transform 1s ease; */
    z-index: 25;
}


.envelope-part.bottom {
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 56%;
    /* background: url('/images/envelop-bottom.png') no-repeat; */
    z-index: 5004;
    display: flex;
    justify-content: center;
    border-radius: 0 0 4px 4px;
    background-size: 100% 100%;
}

/* 信封内容打开状态 */
.paper.open {
    /* transform: translateY(0); */
    opacity: 1;
    /* z-index: 25; */
    transition: 1.5s;
}

.seal {
    width: clamp(50px, 13vw, 100px);
    height: clamp(50px, 13vw, 100px);
    /* margin-left: 15%; */
    margin-left: 18%;
    /* 使用父级宽度的 50% */
    transform: translateX(-50%);
    /* 居中 */
    /* background: url('/images/C&A-Logo-Letter.png') no-repeat; */
    background: url('/images/C&A-Logo-Letter1.png') no-repeat;
    /* background-size: 100% 100%; */
    background-size: 118% 103%;
    scale: 1.4;
    margin-top: -10%;
}

/* 信封内容（纸张）*/
.paper {
    position: absolute;
    top: -80%;
    left: 4.7%;
    width: 89.5%;
    height: 150%;
    background: #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    /* 初始位置隐藏 */
    opacity: 0;
    /* 初始不可见 */
    z-index: 1055;
    /* 信纸层级在顶部下方 */
    padding: clamp(15px, 3vw, 30px);
    border-radius: 4px;
    overflow: auto;
    transition: all 1s ease;
}

/* 继续btuuon */
.dynamicButtons {
    font-weight: 900;
    text-transform: uppercase;
    transition: all 0.3s ease;
    top: 65%;
}


.continue-btn {
    position: absolute;
    bottom: clamp(0px, 1vw, 18px);
    left: 51%;
    transform: translateX(-50%);
    padding: 5px 15px;
    color: #fff;
    border: 1px solid #fff;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    z-index: 10002;
    background: transparent;
    transition: opacity 1s ease;
}

.continue-btn.visible {
    opacity: 1;
}

.paper-content {
    height: 100%;
    overflow-y: auto;
    padding-right: 10px;
    scroll-behavior: smooth;
    /* 添加平滑滚动效果 */
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
}

.paper-content::-webkit-scrollbar {
    width: 4px;
}

.paper-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.paper-content::-webkit-scrollbar-track {
    background-color: transparent;
}

.footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    text-align: center;
    color: white;
    padding: 0.5rem;
    font-size: 0.875rem;
    z-index: 50;
}

/* 响应式调整 */
@media (min-width: 1200px) {

    /* .ai-image {
        display: block;
    } */
    .team-page-wrapper::before,
    .team-page-wrapper::after {
        width: 30%;
    }

    .envelope-container {
        aspect-ratio: 764/550;
        /* 在较小屏幕上进一步增加高度 */
    }

    /* .paper {
        height: 220%;
        top: -80%;
    } */

    .dynamicButtons {
        width: 20%;
        margin-left: 41%;
        top: 310px;
    }
}

@media (min-width: 1100px) {

    .logo-mobile {
        left: 43%;
    }
}

@media (max-width: 768px) {

    .team-page-wrapper::before,
    .team-page-wrapper::after {
        width: 45%;
        height: 70vh;
        /* opacity: 0.3; */
    }

    .paper {
        height: 170%;
        top: -85%;
        /* 调整内边距使内容更紧凑 */
        padding: clamp(10px, 2vw, 20px);
    }

    /* 调整文字大小 */
    .paper-content p {
        font-size: clamp(12px, 1.8vw, 16px);
        line-height: 1.6;
        margin-bottom: 8px;
    }

    .dynamicButtons {
        width: 20%;
        margin-left: 40%;
        top: 280px;
    }

    .footer {
        display: block;
    }
}

@media (max-width: 650px) {
    .team-page-wrapper .envelope-container .paper {
        height: 230% !important;
        /* 使用 !important 确保样式生效 */
        top: -144% !important;
    }

    .dynamicButtons {
        width: 20%;
        margin-left: 40%;
        height: 15%;
        top: 240px;
    }
}

@media (min-width: 640px) {
    .logo-desktop {
        display: block;
    }
}

@media (max-width: 576px) {
    .team-page-wrapper .envelope-container .paper {
        height: 260% !important;
        /* 使用 !important 确保样式生效 */
        top: -174% !important;
    }

    .dynamicButtons {
        width: 20%;
        margin-left: 40%;
        height: 15%;
        top: 210px;
    }

    .team-page-wrapper::before {
        display: none;
    }
}

@media (max-width: 480px) {

    .team-page-wrapper .envelope-container .paper {
        height: 300% !important;
        /* 使用 !important 确保样式生效 */
        top: -200% !important;
    }

    .content-stack {
        padding: 0.5rem;
    }

    .envelope-container {
        aspect-ratio: 764/650;
        /* transform: scale(0.8); */
        margin-top: 2rem;
    }


    .paper-content {
        height: 100%;
        /* 确保内容可以完全展示 */
        -webkit-overflow-scrolling: touch;
        /* 增加 iOS 滚动支持 */
        padding-bottom: 4rem;
        /* 为按钮留出空间 */
    }

    /* 调整继续按钮位置 */
    .dynamicButtons {
        position: fixed;
        /* 改为固定定位 */
        bottom: 2rem;
        /* 距离底部固定距离 */
        top: auto;
        /* 清除 top 值 */
        left: 50%;
        /* 水平居中 */
        transform: translateX(-50%);
        /* 水平居中 */
        width: 120px;
        /* 固定宽度 */
        height: 40px;
        /* 固定高度 */
        margin-left: 10% !important;
    }

    .paper {
        height: 260%;
        /* 按要求调整高度 */
        top: -174%;
        /* 按要求调整顶部位置 */
    }

    .paper-content p {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 8px;
    }

    .dynamicButtons {
        width: 20%;
        margin-left: 40%;
        top: 170px;
        height: 15%;
    }

    .footer {
        display: block;
        /* bottom: 0.5rem !important; */

    }

    .team-page-wrapper::before {
        display: none;
    }

    .logo-mobile {
        display: block;
        position: absolute;
        top: 1%;
        left: 23%;
        z-index: 10;
    }

}

@media (min-width: 1100px) and (max-height: 750px) {
    .team-page-container {
        transform: scale(0.8);
        overflow-y: hidden;
    }

    .dynamicButtons {
        /* width: 20%;
        margin-left: 41% !important; */
        height: 12% !important;
        top: 65% !important;
    }
}

@media (max-width: 1099px) and (max-height: 750px) {
    .team-page-container {
        transform: scale(0.8);
        overflow-y: hidden;
    }

    .footer {
        /* display: none; */
        bottom: 20px;
    }
}

@media (max-width: 390px) {
    .dynamicButtons {
        width: 20%;
        margin-left: 40%;
        top: 150px;
    }

    .logo-mobile {
        position: absolute;
        top: -2%;
        left: 22%;
        z-index: 10;
        display: block;
    }

    .footer {
        display: block;
    }

    .paper-content {
        height: 180%;
        overflow-y: auto;
        padding-right: 15px;
        /* 为滚动条留出空间 */
    }
}

@media (max-width: 344px) {
    .dynamicButtons {
        width: 20%;
        /* margin-left: 40% !important; */
        top: 140px;
    }

    .footer {
        display: block;
    }
}

/* 添加响应式背景处理 */
@media (max-width: 768px) {
    .team-page-wrapper {
        background-attachment: scroll;
    }

    .footer {
        bottom: -0.2rem !important;
    }
}

/* 优化滚动条样式 */
.paper-content {
    padding-bottom: 200px !important;
    height: 80%;
    overflow-y: auto;
    padding-right: 15px;
    /* 为滚动条留出空间 */
}

.paper-content::-webkit-scrollbar {
    width: 4px;
}

.paper-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.paper-content::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}

.dynamicButtons,
.dynamicButtons *,
.dynamicButtons :after,
.dynamicButtons :before,
.dynamicButtons:after,
.dynamicButtons:before {
    border: 0 solid;
    box-sizing: border-box;
}

.dynamicButtons {
    /* width: 40%; */
    height: 3.125rem;
    text-align: center;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: dynamicButton;
    background-color: #060d3d;
    background-image: none;
    color: #fff;
    cursor: pointer;
    font-size: 100%;
    line-height: 1.5;
    margin: 0;

    -webkit-mask-image: -webkit-radial-gradient(#000, #fff);
}

.dynamicButtons:disabled {
    cursor: default;
}

.dynamicButtons:-moz-focusring {
    outline: auto;
}

.dynamicButtons svg {
    display: block;
    vertical-align: middle;
}

.dynamicButtons [hidden] {
    display: none;
}

.dynamicButtons {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 900;
    position: absolute;
    /* 改为绝对定位 */
    width: 20%;
    height: 12%;
    left: 41%;
    /* 水平居中 */
    /* bottom: 15%; */
    /* top: 78%; */
    top: 65%;
    text-transform: uppercase;
    transition: color 0.1s linear;
    background-color: #060d3d;
    color: #fff;
    cursor: pointer;
    z-index: 10000;
    /* 确保按钮在最上层 */
    border-radius: 6px;
    /* 让按钮变得更圆 */
    background: rgba(255, 255, 255, 0.2);
    /* 半透明背景 */
    backdrop-filter: blur(8px);
    /* 玻璃模糊效果 */
    -webkit-backdrop-filter: blur(8px);
    /* 兼容 Safari */
    /* 玻璃拟态 + 内发光霓虹效果 */
    box-shadow: inset 0 2px 13px rgba(255, 255, 255, 2.3),
        /* 内部阴影 */
        0 4px 12px rgba(255, 255, 255, 0.25);
    /* 外发光 */
    cursor: pointer;
    transition: all 0.3s ease;
}

.dynamicButtons:after,
.dynamicButtons:before {
    content: "";
    height: 0;
    position: absolute;
    width: 0;
    /* border-radius: 6px; */
}

.dynamicButtons:hover {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 2px 8px rgba(255, 255, 255, 3.4), 
    0 6px 14px rgba(255, 255, 255, 0.4);
}

.dynamicButtons:active {
    background: rgba(255, 255, 255, 0.4);
    box-shadow: inset 0 3px 8px rgba(255, 255, 255, 3.5), 
    0 2px 5px rgba(255, 255, 255, 0.3);
}

.dynamicButtons:before {
    border-right: var(--border);
    border-top: var(--border);
    left: 0;
    top: 0;
}

.dynamicButtons:before {
    --border: 2px solid #fff;
    -webkit-animation: border-top-and-right 2s infinite;
    animation: border-top-and-right 2s infinite;
}

.dynamicButtons:after {
    border-bottom: var(--border);
    border-left: var(--border);
    bottom: 0;
    right: 0;
    z-index: -1;
}

.dynamicButtons:after {
    --border: 2px solid #fff;
    -webkit-animation: border-bottom-and-left 2s infinite;
    animation: border-bottom-and-left 2s infinite;
}

/* 添加 AI 机器人渐变消失的媒体查询 */
@media (max-width: 1300px) {
    .team-page-wrapper::before {
        width: 35%;
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .dynamicButtons {
        top: 70%;
    }
}

@media (max-width: 1200px) {
    .team-page-wrapper::before {
        width: 32%;
        opacity: 0.8;
    }
}

@media (max-width: 1100px) {
    .team-page-wrapper::before {
        width: 28%;
        opacity: 0.7;
    }
}

@media (max-width: 1000px) {
    .team-page-wrapper::before {
        width: 25%;
        opacity: 0.5;
    }
}

@media (max-width: 900px) {
    .team-page-wrapper::before {
        width: 22%;
        opacity: 0.3;
    }
}

@media (max-width: 800px) {
    .team-page-wrapper::before {
        width: 20%;
        opacity: 0.2;
    }
}

@media (max-width: 576px) {
    .team-page-wrapper::before {
        display: none;
    }
}

/* 添加 iOS 特定的修复 */
@supports (-webkit-touch-callout: none) {
    .team-page-container {
        min-height: -webkit-fill-available;
        /* iOS viewport 高度修复 */
    }

    .paper-content {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}