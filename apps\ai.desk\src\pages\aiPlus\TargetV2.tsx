import { AuthenticationLayout } from "@code.8cent/react/layouts";
import { useRequest } from "ahooks";
import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import noty from "@code.8cent/react/noty";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { initUserData } from "@/utils/initUserData";
import useUserStore from "@/store/user";
import useWizardStore from "@code.8cent/store/wizard";
import NavigationLogic from "@/utils/navigationLogic";

// 处理 AiPlus target 页面
const AiPlusTargetV2Page = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { lang } = useSettingStore();
    const userStore = useUserStore();
    const wizardStore = useWizardStore();

    // 退出登录
    const handleLogout = async () => {
        await userStore.logout();
        navigate("/account/login", { replace: true });
    };

    // 登录或注册
    const { run: handleLoginOrRegister, loading: loggingIn } = useRequest(
        async (token: string) => {
            try {
                // 使用用户store进行登录
                await userStore.login(token);

                // 初始化用户数据（通知列表等）
                await initUserData();

                // 获取 query 参数
                const queryParams = new URLSearchParams(location.search);
                const params = {
                    refer: queryParams.get("refer") || "",
                    occupation_name: queryParams.get("occupation_name") || "",
                    occupation_id: queryParams.get("occupation_id") || "",
                    type: queryParams.get("type") || "",
                };

                // 创建导航逻辑实例
                const navigationLogic = new NavigationLogic(navigate, userStore, wizardStore);

                // 获取用户数据
                const userData = userStore.profile;
                if (!userData) {
                    throw new Error("用户数据获取失败");
                }

                // 构造UserLoginData格式的数据
                const loginData = {
                    token: userStore.token,
                    profile_status: userData.profile_status,
                    profile_id: userData.profile_id,
                    register_setting: userData.register_setting,
                    pay_token: userData.pay_token,
                    is_new: userData.is_new,
                    base_info: userData.base_info,
                };

                // 处理页面跳转逻辑
                await navigationLogic.handleUserNavigation(loginData, params);

            } catch (error) {
                console.error("登录失败:", error);
                noty.error(t("login.fail", lang), error instanceof Error ? error.message : "登录失败");
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        // AiPlus 返回的参数
        const token = queryParams.get("token");
        const state = queryParams.get("state");

        // 处理退出登录
        if (state && state === "logout") {
            handleLogout();
            return;
        }

        if (!token) {
            navigate("/account/login", { replace: true });
            return;
        } else {
            // 处理登录或注册逻辑
            handleLoginOrRegister(token);
        }
    }, [location]);

    return <AuthenticationLayout />;
};

export default AiPlusTargetV2Page;
