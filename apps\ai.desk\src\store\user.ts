import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";
import { cnaRequest } from "@code.8cent/utils";

export interface UserProfile {
    profile_id: number;
    profile_status: number;
    register_setting: string[];
    pay_token?: string;
    is_new?: 1 | 2;
    base_info?: 1 | 2;
    occupation_id?: string;
    occupation_name?: string;
    refer?: string;
}

export interface UserState {
    token: string;
    profile: UserProfile | null;
    isLoggedIn: boolean;
    isLoading: boolean;
    stepV3: string;
    interviewStatus: string;
    hasShownInterviewSuccess: Record<number, boolean>;
}

export interface UserActions {
    login: (token: string) => Promise<void>;
    logout: () => Promise<void>;
    setToken: (token: string) => void;
    setProfile: (profile: Partial<UserProfile>) => void;
    updateProfile: (updates: Partial<UserProfile>) => void;
    setStepV3: (step: string) => void;
    setInterviewStatus: (status: string) => void;
    setInterviewSuccessShown: (profileId: number, shown: boolean) => void;
    syncFromStorage: () => Promise<void>;
    syncToStorage: () => Promise<void>;
    reset: () => void;
}

type UserStore = UserState & UserActions;

const initialState: UserState = {
    token: "",
    profile: null,
    isLoggedIn: false,
    isLoading: false,
    stepV3: "basic-info",
    interviewStatus: "pending",
    hasShownInterviewSuccess: {},
};

const baseUserStore = create<UserStore>()(
    devtools(
        persist(
            (set, get) => ({
                ...initialState,

                login: async (token: string) => {
                    set({ isLoading: true });
                    try {
                        const { result, error } = await cnaRequest<{
                            token: string;
                            profile_status: number;
                            profile_id: number;
                            register_setting: string[];
                            pay_token?: string;
                            is_new?: 1 | 2;
                            base_info?: 1 | 2;
                        }>("/api/v1/login/userLogin", "POST", { AiPlus_token: token });

                        if (error) throw new Error(error.message);

                        if (result?.data) {
                            const profile: UserProfile = {
                                profile_id: result.data.profile_id,
                                profile_status: result.data.profile_status,
                                register_setting: result.data.register_setting || [],
                                pay_token: result.data.pay_token,
                                is_new: result.data.is_new,
                                base_info: result.data.base_info,
                            };

                            set({
                                token: `Bearer ${result.data.token}`,
                                profile,
                                isLoggedIn: true,
                                isLoading: false,
                            });

                            await get().syncToStorage();
                        }
                    } catch (error) {
                        set({ isLoading: false });
                        throw error;
                    }
                },

                logout: async () => {
                    try {
                        await cnaRequest("/api/v1/login/logout", "POST");
                    } catch (error) {
                        console.error("登出失败:", error);
                    } finally {
                        set(initialState);
                        await window.localForage.removeItem("cna-token");
                        await window.localForage.removeItem("register-setting");
                        await window.localForage.removeItem("pay-token");
                        await window.localForage.removeItem("base_info");
                        await window.localForage.removeItem("is_new");
                        await window.localForage.removeItem("occupation_id");
                        await window.localForage.removeItem("occupation_name");
                        sessionStorage.clear();
                    }
                },

                setToken: (token: string) => set({ token }),

                setProfile: (profile: Partial<UserProfile>) => {
                    set((state) => ({
                        profile: state.profile ? { ...state.profile, ...profile } : profile as UserProfile,
                    }));
                },

                updateProfile: (updates: Partial<UserProfile>) => {
                    set((state) => ({
                        profile: state.profile ? { ...state.profile, ...updates } : null,
                    }));
                },

                setStepV3: (step: string) => {
                    set({ stepV3: step });
                    sessionStorage.setItem("step-v3", step);
                },

                setInterviewStatus: (status: string) => {
                    set({ interviewStatus: status });
                    sessionStorage.setItem("interview-status", status);
                },

                setInterviewSuccessShown: (profileId: number, shown: boolean) => {
                    set((state) => ({
                        hasShownInterviewSuccess: {
                            ...state.hasShownInterviewSuccess,
                            [profileId]: shown,
                        },
                    }));
                    window.localForage.setItem(`hasShownInterviewSuccess_${profileId}`, shown);
                },

                syncFromStorage: async () => {
                    const token = await window.localForage.getItem("cna-token") as string;
                    const stepV3 = sessionStorage.getItem("step-v3") || "basic-info";
                    const interviewStatus = sessionStorage.getItem("interview-status") || "pending";

                    if (token) {
                        set({
                            token,
                            stepV3,
                            interviewStatus,
                            isLoggedIn: true,
                        });

                        const profileData = await window.localForage.getItem("user_profile");
                        if (profileData) {
                            set({ profile: profileData as UserProfile });
                        }
                    }
                },

                syncToStorage: async () => {
                    const { token, profile } = get();
                    if (token) {
                        await window.localForage.setItem("cna-token", token);
                    }
                    if (profile) {
                        await window.localForage.setItem("user_profile", profile);
                        await window.localForage.setItem("register-setting", profile.register_setting);
                        if (profile.pay_token) {
                            await window.localForage.setItem("pay-token", profile.pay_token);
                        }
                        if (profile.base_info) {
                            await window.localForage.setItem("base_info", profile.base_info);
                        }
                        if (profile.is_new) {
                            await window.localForage.setItem("is_new", profile.is_new);
                        }
                        if (profile.occupation_id) {
                            await window.localForage.setItem("occupation_id", profile.occupation_id);
                        }
                        if (profile.occupation_name) {
                            await window.localForage.setItem("occupation_name", profile.occupation_name);
                        }
                    }
                },

                reset: () => set(initialState),
            }),
            {
                name: "user-store",
                partialize: (state) => ({
                    token: state.token,
                    profile: state.profile,
                    isLoggedIn: state.isLoggedIn,
                    stepV3: state.stepV3,
                    interviewStatus: state.interviewStatus,
                    hasShownInterviewSuccess: state.hasShownInterviewSuccess,
                }),
            }
        ),
        {
            name: "user-store",
        }
    )
);

const useUserStore = createSelectors(baseUserStore);

export default useUserStore;
