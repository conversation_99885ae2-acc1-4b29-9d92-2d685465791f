import PageHeader from "@/components/member/PageHeader";
import {
    AspectRatio,
    Center,
    Group,
    Image,
    LoadingOverlay,
    Overlay,
    ScrollArea,
    Stack,
    Tabs,
    Text,
} from "@mantine/core";
import IntroComponent from "@/components/member/business/Intro";
import IntroModal from "@/components/member/business/modals/Intro";
import useModalStore from "@/store/modal";
import useBusinessStore, { setBusinessSection } from "@/store/business";
import business from "@/apis/business";
import { useRequest } from "ahooks";
import { useEffect, useState } from "react";
import noty from "@code.8cent/react/noty";
import { CnaButton } from "@code.8cent/react/components";
import { FileViewer } from "@code.8cent/react/FileViewer";
import { CaretLeft } from "@phosphor-icons/react";

const Pay = () => {
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();
    const businessSection = useBusinessStore.use.businessSection();
    const [payStatus, setPayStatus] = useState<
        "ready" | "pay" | "paying" | "success" | "fail" | "overTime"
    >("ready");

    // 获取业务信息
    const {
        run: getBusinessInfo,
        data: businessInfo,
        loading,
    } = useRequest(
        async () => {
            try {
                const res = await business.getBusinessList(
                    "partner_franchise_handbook_license"
                );

                const introRes = await business.getCourseInfo(res?.[0]?.id);

                return {
                    ...res?.[0],
                    ...introRes,
                };
            } catch (error) {
                noty.error(error.message);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        getBusinessInfo();
    }, []);

    const handleStartLearn = () => {
        setBusinessSection("unlicensed-courses");
    };

    const handleStartExam = async () => {
        const canExam = await business.checkCanExam("56");
        if (!canExam) {
            openConfirm({
                title: "无法考试",
                message: "您尚有学习未完成，无法开始考试",
                onConfirmText: "前往学习",
                onConfirm: () => {
                    setBusinessSection("unlicensed-courses");
                },
            });
            return false;
        }

        setBusinessSection("unlicensed-exam");
    };

    const onStartPay = () => {
        console.log("onStartPay");
    };

    return (
        <Stack>
            <PageHeader
                title="专案认证牌照"
                subTitle="简单介绍简单介绍简单介绍简单介绍简单介绍简单介绍"
            />

            <LoadingOverlay visible={loading} />

            <Group justify="flex-start">
                <Group
                    className="tw-cursor-pointer"
                    onClick={() => setBusinessSection("licensed-list")}
                >
                    <CaretLeft size={24} />
                    <Text fw={600}>返回</Text>
                </Group>
            </Group>

            {!loading && (
                <Stack
                    gap={0}
                    className="tw-h-full tw-flex tw-flex-col"
                >
                    <div className="tw-rounded-[40px_40px_0px_0px] tw-bg-[linear-gradient(90deg,#273173_41.57%,#767FBF_99.77%)] tw-h-[60px] tw-self-stretch"></div>
                    {payStatus === "ready" ? (
                        <Stack
                            p="md"
                            className="tw-bg-white tw-rounded-b-xl tw-h-full tw-flex-1"
                            gap={0}
                        >
                            <Group justify="space-between">
                                <Group>
                                    <Image
                                        src="/images/C&A-Logo-Icon-White.svg"
                                        alt="C&A Logo"
                                        className="tw-rounded-[20px] tw-bg-[linear-gradient(180deg,#393E88_0%,#0F1642_100%)] tw-w-[120px] tw-h-[120px] tw-p-[29px_20px_28px_20px]"
                                        draggable={false}
                                    />
                                    <Stack
                                        justify="center"
                                        gap={0}
                                    >
                                        <Text
                                            c="#101744"
                                            fw={400}
                                            fz="24px"
                                        >
                                            专案认证牌照
                                        </Text>
                                        <Text
                                            c="#243081"
                                            size="lg"
                                            fw={600}
                                            fz="38px"
                                        >
                                            ￥4XXX.00
                                        </Text>
                                    </Stack>
                                </Group>
                                <Group className="tw-flex tw-items-end tw-self-stretch">
                                    <CnaButton
                                        radius="xl"
                                        variant="outline"
                                        className="tw-bg-[linear-gradient(to_right,#E2E5F4_0%,#EBEDF7_20%,transparent_75%)] tw-text-[#19288F] tw-border-[#19288F]"
                                        onClick={() => setPayStatus("pay")}
                                    >
                                        立即申请
                                    </CnaButton>
                                </Group>
                            </Group>
                            <Tabs
                                defaultValue="intro"
                                className="tw-flex-1 tw-flex tw-flex-col"
                            >
                                <Tabs.List className="tw-pt-[20px]">
                                    <Tabs.Tab value="intro">课程介绍</Tabs.Tab>
                                    <Tabs.Tab value="general">
                                        课程目录
                                    </Tabs.Tab>
                                </Tabs.List>

                                <Tabs.Panel
                                    value="intro"
                                    className="tw-pt-[20px]"
                                >
                                    <ScrollArea
                                        h={400}
                                        className="tw-px-6"
                                    >
                                        <Stack>
                                            <Text
                                                fz="16px"
                                                fw={700}
                                                mb={5}
                                            >
                                                {" "}
                                                【购前须知】
                                            </Text>
                                            <Text
                                                fz="14px"
                                                style={{ lineHeight: "1.6" }}
                                            >
                                                您选购的牌照认证自购买当日起1年有效，有效期内，可随时进行课程学习;无学习次数、学习时长限制，线上课程资料暂不开放下载，考试期间系统将关闭课程访问入口，请做好学习安排。
                                            </Text>
                                            <Text
                                                fz="14px"
                                                style={{ lineHeight: "1.6" }}
                                            >
                                                开具发票请查询对应订单，在订单详情可"申请发票"，支持开具个人和企业抬头的电子发票，亦支持开增值税专用发票
                                            </Text>
                                            <Text
                                                fz="14px"
                                                mb={15}
                                            >
                                                最终解释权归C&A所有
                                            </Text>

                                            <Text
                                                fz="16px"
                                                fw={700}
                                                c="#2C3E50"
                                                mb={5}
                                            >
                                                【课程目标】
                                            </Text>
                                            <Text
                                                fz="14px"
                                                style={{ lineHeight: "1.6" }}
                                                mb={15}
                                            >
                                                帮助合伙人深入了解全球合伙人核心价值，通过加盟拓展国际客源、提升专业能力、优化收益结构，并借助A!技术与联盟资源实现从本地业务到全球化市场的持续增长
                                            </Text>

                                            <Text
                                                fz="16px"
                                                fw={700}
                                                c="#2C3E50"
                                                mb={5}
                                            >
                                                【目标学员】
                                            </Text>
                                            <Text
                                                fz="14px"
                                                style={{ lineHeight: "1.6" }}
                                                mb={15}
                                            >
                                                初加入合伙人大联盟的合伙人
                                            </Text>

                                            <Text
                                                fz="16px"
                                                fw={700}
                                                c="#2C3E50"
                                                mb={5}
                                            >
                                                【通过要求】
                                            </Text>
                                            <Text
                                                fz="14px"
                                                style={{ lineHeight: "1.6" }}
                                            >
                                                考试成绩达到80分及以上
                                            </Text>
                                        </Stack>
                                    </ScrollArea>
                                </Tabs.Panel>
                                <Tabs.Panel
                                    value="general"
                                    className="tw-pt-[20px] tw-h-[420px]"
                                >
                                    {/* <FileViewer file={generalDoc} /> */}
                                    <div></div>
                                </Tabs.Panel>
                            </Tabs>
                        </Stack>
                    ) : (
                        <Stack
                            p="md"
                            className="tw-bg-white tw-rounded-b-xl tw-h-full tw-flex-1"
                            gap={0}
                        >
                            <Text size="xs">内容：专案认证牌照</Text>
                            <Text size="xs">价格：￥4XXX.XX</Text>
                            <div className="tw-flex tw-pb-2 tw-items-center tw-justify-end tw-border-b tw-border-dashed tw-border-gray-200 ">
                                <Text
                                    size="sm"
                                    c="#000"
                                    fw={700}
                                    className="tw-mr-2"
                                >
                                    支付金额:
                                </Text>
                                <Text
                                    size="xl"
                                    fw={600}
                                    c="#F98500"
                                >
                                    ￥4XXX.XX
                                </Text>
                            </div>

                            <Stack
                                align="center"
                                justify="center"
                                gap="xs"
                                h="55vh"
                                className="tw-relative"
                            >
                                <Center className="tw-relative tw-w-full tw-h-full tw-flex-col">
                                    <Image
                                        radius="md"
                                        h={160}
                                        w={160}
                                        src="/images/station/qrcode.png"
                                    />
                                    <div className="tw-text-center tw-text-[#000] tw-text-sm tw-mt-4">
                                        请扫码支付（微信、支付宝）
                                    </div>
                                    {(payStatus === "success" ||
                                        payStatus === "fail" ||
                                        payStatus === "overTime") && (
                                        <Overlay
                                            color="#fff"
                                            backgroundOpacity={0.9}
                                            blur={2}
                                        />
                                    )}
                                </Center>
                                <Center className="tw-absolute tw-z-[1000]">
                                    {payStatus === "success" && (
                                        <Image
                                            src="/images/station/successIcon.svg"
                                            w={60}
                                        />
                                    )}
                                    {payStatus === "overTime" && (
                                        <Image
                                            src="/images/station/overTime.svg"
                                            w={80}
                                        />
                                    )}
                                </Center>
                            </Stack>
                        </Stack>
                    )}
                </Stack>
            )}

            <IntroModal
                title={businessInfo?.project_name}
                onStartExam={handleStartExam}
            />
        </Stack>
    );
};

export default Pay;
