import logoMySvg from "@/assets/svg/my.svg";
import { TextInput } from "@mantine/core";
import { Divide } from "lucide-react";
import Footer from "@/components/layouts/components/Footer";
import { Image } from "@mantine/core";
const SearchLayout = ({ LeftNavItems, logoSrc, PicIndexSrc }) => {
    return (
        <div className="tw-bg-green_bg_primary">
            <section className="tw-flex   tw-flex-col-reverse  md:tw-flex-row tw-w-full md:tw-items-stretch">
                <div
                    className="tw-w-full md:tw-w-3/12 xl:tw-w-2/12  
                     tw-py-[1rem] md:tw-py-[1.5rem] xl:tw-py-[3rem]  
                
                "
                >
                    <div className="tw-w-full">
                        <Image
                            src={logoSrc}
                            alt="Logo"
                            className="tw-w-3/12 sm:tw-w-4/12 md:tw-w-7/12 tw-mb-[2rem]  tw-mx-auto"
                        />
                        <div className="tw-w-full tw-flex tw-flex-wrap md:tw-text-[16px]  tw-tracking-[0.3vw] ">
                            {LeftNavItems.map((item) => {
                                return (
                                    <div className="tw-text-white tw-text-center tw-w-6/12 md:tw-w-full tw-mb-1">
                                        {item.name}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
                <div className="tw-w-full md:tw-w-10/12 ">
                    <Image
                        src={PicIndexSrc}
                        className="tw-w-full tw-h-full"
                    ></Image>
                </div>
            </section>
            <section className="tw-text-white tw-p-[3rem]">
                <div className="tw-flex tw-items-center tw-gap-4">
                    <Image
                        src={logoMySvg}
                        w={130}
                    ></Image>
                    <p className="tw-text-[3.3vw] tw-tracking-[0.4vw]">马来西亚</p>
                </div>
                <div className="tw-text-[1.75vw] tw-tracking-[0.3vw]">
                    <p>绿色智慧城市基建、城镇、设施等开发项目分类搜索</p>
                    <div className="tw-mb-2">
                        <div>开发商 / 开发管理公司搜索:</div>
                        <TextInput
                            rightSection={
                                <div className="tw-text-[12px] tw-text-black tw-cursor-pointer">
                                    搜索
                                </div>
                            }
                        ></TextInput>
                    </div>
                </div>
            </section>
            <Footer />
        </div>
    );
};

export default SearchLayout;
