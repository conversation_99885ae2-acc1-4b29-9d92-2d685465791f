import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";
import notification from "@/apis/notification";
import user from "@/apis/user";

type NotificationItem = {
    notificationID: `${number}`;
    notificationState: 0 | 1;
    notificationTitleZH: string;
    notificationTitleZT: string;
    notificationTitleEN: string;
    notificationTitleMS: string;
    notificationDescriptionZH: string;
    notificationDescriptionZT: string;
    notificationDescriptionEN: string;
    notificationDescriptionMS: string;
    created_at: string | null;
};

type NotificationState = {
    notifications: NotificationItem[];
    unreadCount: number;
    loading: boolean;
    hasMore: boolean;
    page: number;
    pageSize: number;
    initialized: boolean;
};

type NotificationAction = {
    setNotifications: (notifications: NotificationItem[]) => void;
    addNotifications: (notifications: NotificationItem[]) => void;
    setUnreadCount: (count: number) => void;
    setLoading: (loading: boolean) => void;
    setHasMore: (hasMore: boolean) => void;
    setPage: (page: number) => void;
    setInitialized: (initialized: boolean) => void;
    fetchNotifications: (
        page?: number,
        pageSize?: number,
        reset?: boolean
    ) => Promise<void>;
    markAsRead: (notificationID: string) => Promise<boolean>;
    deleteNotification: (notificationID: string) => Promise<boolean>;
    markAllAsRead: () => Promise<boolean>;
    fetchNotificationCount: () => Promise<void>;
};

type NotificationStateAndAction = NotificationState & NotificationAction;

const baseNotificationStore = create<NotificationStateAndAction>()(
    devtools(
        (set, get) => ({
            notifications: [],
            unreadCount: 0,
            loading: false,
            hasMore: true,
            page: 1,
            pageSize: 5,
            initialized: false,

            setNotifications: async (notifications) => {
                set({ notifications });
                await get().fetchNotificationCount();
            },

            addNotifications: async (newNotifications) => {
                // 合并通知并去重
                const currentNotifications = get().notifications;
                const mergedNotifications = [...currentNotifications];

                newNotifications.forEach((newNotification) => {
                    const existingIndex = mergedNotifications.findIndex(
                        (item) =>
                            item.notificationID ===
                            newNotification.notificationID
                    );

                    if (existingIndex >= 0) {
                        // 更新已存在的通知
                        mergedNotifications[existingIndex] = newNotification;
                    } else {
                        // 添加新通知
                        mergedNotifications.push(newNotification);
                    }
                });

                set({ notifications: mergedNotifications });
                await get().fetchNotificationCount();
            },

            setUnreadCount: (unreadCount) => {
                set({ unreadCount });
            },

            setLoading: (loading) => {
                set({ loading });
            },

            setHasMore: (hasMore) => {
                set({ hasMore });
            },

            setPage: (page) => {
                set({ page });
            },

            setInitialized: (initialized) => {
                set({ initialized });
            },

            fetchNotificationCount: async () => {
                try {
                    const countResult = await user.getNotificationCount();
                    if (countResult) {
                        set({ unreadCount: countResult.unread });
                    }
                } catch (error) {
                    console.error("Failed to fetch notification count:", error);
                }
            },

            fetchNotifications: async (page, pageSize, reset = false) => {
                const state = get();
                const currentPage = page || state.page;
                const currentPageSize = pageSize || state.pageSize;

                if (state.loading) return;

                set({ loading: true });

                try {
                    const result = await notification.list(
                        currentPage,
                        currentPageSize
                    );

                    if (result) {
                        const { notification: notifications } = result;
                        const convertedNotifications = notifications.map(
                            (item) => ({
                                ...item,
                                notificationState: Number(
                                    item.notificationState
                                ) as 0 | 1,
                            })
                        );

                        if (reset) {
                            // 重置通知列表
                            set({ notifications: convertedNotifications });
                        } else {
                            // 添加到现有通知列表
                            get().addNotifications(convertedNotifications);
                        }

                        // 更新分页信息
                        set({
                            hasMore: notifications.length === currentPageSize,
                            page: currentPage + 1,
                            initialized: true,
                        });

                        // 获取最新的未读数量统计
                        await get().fetchNotificationCount();
                    }
                } catch (error) {
                    console.error("Failed to fetch notifications:", error);
                } finally {
                    set({ loading: false });
                }
            },

            markAsRead: async (notificationID) => {
                try {
                    const success = await notification.read(notificationID);

                    if (success) {
                        // 更新通知状态
                        const { notifications } = get();
                        const updatedNotifications = notifications.map(
                            (item) => {
                                if (item.notificationID === notificationID) {
                                    return {
                                        ...item,
                                        notificationState: 1 as 0 | 1,
                                    };
                                }
                                return item;
                            }
                        );

                        set({ notifications: updatedNotifications });
                        // 获取最新的未读数量统计
                        await get().fetchNotificationCount();
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error(
                        "Failed to mark notification as read:",
                        error
                    );
                    return false;
                }
            },

            deleteNotification: async (notificationID) => {
                try {
                    const success = await notification.delete(notificationID);

                    if (success) {
                        // 更新通知状态
                        const { notifications } = get();
                        const updatedNotifications = notifications.map(
                            (item) => {
                                if (item.notificationID === notificationID) {
                                    return {
                                        ...item,
                                        notificationState: 1 as 0 | 1,
                                    };
                                }
                                return item;
                            }
                        );

                        set({ notifications: updatedNotifications });
                        // 获取最新的未读数量统计
                        await get().fetchNotificationCount();
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error("Failed to delete notification:", error);
                    return false;
                }
            },

            markAllAsRead: async () => {
                try {
                    const { notifications } = get();
                    const updatedNotifications = notifications.map((item) => ({
                        ...item,
                        notificationState: 1 as 0 | 1,
                    }));

                    set({
                        notifications: updatedNotifications,
                        unreadCount: 0,
                    });

                    // 提交 API
                    const success = await notification.readAll();
                    return success;
                } catch (error) {
                    console.error(
                        "Failed to mark all notifications as read:",
                        error
                    );
                    return false;
                }
            },
        }),
        {
            name: "notification-store",
        }
    )
);

const useNotificationStore = createSelectors(baseNotificationStore);

export default useNotificationStore;
