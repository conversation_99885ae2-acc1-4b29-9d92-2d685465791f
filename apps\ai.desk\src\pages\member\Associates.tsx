import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import {
    Paper,
    Table,
    Title,
    Group,
    ScrollArea,
    Text,
    LoadingOverlay,
    Modal,
    Stack,
    Tabs,
} from "@mantine/core";
import {
    BookBookmark,
    CheckCircle,
    Clock,
    XCircle,
} from "@phosphor-icons/react";
import { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useProfileStore from "@/store/profile";

const renderInterviewStatus = (status: number) => {
    switch (status) {
        case 0:
            return (
                <Text
                    fw={600}
                    className="tw-text-[#0648DE]"
                    size="sm"
                >
                    申请面试中
                </Text>
            );
        case 1:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#FF9500]"
                >
                    <Clock
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        面试完成 - 审核中
                    </Text>
                </Group>
            );
        case 2:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#05C126]"
                >
                    <CheckCircle
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        审核通过
                    </Text>
                </Group>
            );
        case 3:
            return (
                <Group
                    gap={2}
                    className="tw-text-[#DA0101]"
                >
                    <XCircle
                        weight="fill"
                        size={20}
                    />
                    <Text
                        fw={600}
                        size="sm"
                    >
                        未通过
                    </Text>
                </Group>
            );
        default:
            return null;
    }
};

const Associates = () => {
    const [associates, setAssociates] = useState<TAssociateItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [inviteModalOpened, setInviteModalOpened] = useState(false);
    const profileInfo = useProfileStore();
    const [isTeamJob, setIsTeamJob] = useState(false);

    const navigate = useNavigate();

    useEffect(() => {
        // TODO: 实现获取合伙人列表的逻辑
        const fetchAssociates = async () => {
            try {
                setLoading(true);
                // 先获取第一页，1000条数据（不做分页）
                const res = await api.associate.list(1, 1000);
                setAssociates(res.data);
            } catch (error) {
                console.error("获取合伙人列表失败:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchAssociates();

        // 是否三三制合伙人
        if (
            profileInfo.job &&
            profileInfo.job !== "管理合伙人" &&
            profileInfo.job !== "联盟合伙人" &&
            profileInfo.job !== "业务合伙人"
        ) {
            setIsTeamJob(true);
        }
    }, [profileInfo]);

    const handleInviteAssociate = () => {
        setInviteModalOpened(true);
    };

    const handleViewProfile = (profileId: number) => {
        navigate("/member/associates/profile", {
            replace: true,
            state: {
                profileId,
            },
        });
    };

    // 点击面试按钮，跳转面试页面
    const handleStartInterview = (profileId: number) => {
        navigate("/member/interview", {
            replace: true,
            state: {
                profileId,
            },
        });
    };

    return (
        <div>
            <PageHeader
                title="管理合伙人"
                subTitle="本栏目用于管理您的合伙人，您可以邀请合伙人并完成面试，同时了解每个合伙人完成业务后您可以获取的津贴分成。"
            />
            <Modal
                opened={inviteModalOpened}
                onClose={() => setInviteModalOpened(false)}
                title="邀请合伙人"
                centered
                radius="md"
                styles={{
                    header: {
                        borderBottom: "1px solid #E2E5F4",
                    },
                }}
                size="lg"
            >
                <Stack className="tw-p-4">
                    <Text fw={600}>已生成您的合伙人邀请链接</Text>
                    <Text className="tw-underline">
                        {`${location.origin}/team?refer=${profileInfo.profilePartnerCode}`}
                    </Text>
                    <CnaButton
                        className="tw-w-full"
                        onClick={() => {
                            navigator.clipboard.writeText(
                                `${location.origin}/team?refer=${profileInfo.profilePartnerCode}`
                            );
                            noty.success("合伙人邀请链接已复制到剪贴板");
                        }}
                    >
                        复制链接
                    </CnaButton>
                    {isTeamJob && (
                        <>
                            <Text fw={600}>三三制邀请链接</Text>
                            <Text className="tw-underline">
                                {`${location.origin}/team?refer=${profileInfo.profileContact}`}
                            </Text>
                            <CnaButton
                                className="tw-w-full"
                                onClick={() => {
                                    navigator.clipboard.writeText(
                                        `${location.origin}/team?refer=${profileInfo.profileContact}`
                                    );
                                    noty.success(
                                        "三三制邀请链接已复制到剪贴板"
                                    );
                                }}
                            >
                                复制链接
                            </CnaButton>
                        </>
                    )}
                </Stack>
            </Modal>
            <Paper
                className="tw-mt-8 tw-p-8"
                radius="lg"
                pos="relative"
            >
                <LoadingOverlay visible={loading} />

                <Tabs defaultValue="associates">
                    <Tabs.List>
                        <Tabs.Tab value="associates">
                            <Title order={4}>合伙人</Title>
                        </Tabs.Tab>
                        <Tabs.Tab value="bonus">
                            <Title order={4}>管理津贴</Title>
                        </Tabs.Tab>
                        <Group className="tw-absolute tw-right-0">
                            {profileInfo.role_level !== 1 &&
                                profileInfo.manager_time > 0 && (
                                    <Text
                                        c={`${
                                            profileInfo.manager_time > 3
                                                ? "#FF9500"
                                                : "#DA0101"
                                        }`}
                                        size="sm"
                                    >
                                        未达标，豁免期限还剩
                                        {profileInfo.manager_time}天
                                    </Text>
                                )}
                            <CnaButton
                                leftSection={<BookBookmark />}
                                radius="xl"
                                variant="outline"
                                className="tw-bg-[linear-gradient(to_right,#E2E5F4_0%,#EBEDF7_20%,transparent_75%)] tw-text-[#19288F] tw-border-[#19288F]"
                                onClick={handleInviteAssociate}
                            >
                                邀请合伙人
                            </CnaButton>
                        </Group>
                    </Tabs.List>

                    <Tabs.Panel value="associates">
                        <ScrollArea
                            h="calc(100vh - 400px)"
                            mt="lg"
                        >
                            <Table
                                withRowBorders={false}
                                highlightOnHover
                                className="custom-table"
                            >
                                <Table.Thead>
                                    <Table.Tr>
                                        <Table.Th>姓名</Table.Th>
                                        <Table.Th>手机</Table.Th>
                                        <Table.Th>邮箱</Table.Th>
                                        <Table.Th>申请时间</Table.Th>
                                        <Table.Th>状态</Table.Th>
                                        <Table.Th>操作</Table.Th>
                                    </Table.Tr>
                                </Table.Thead>
                                <Table.Tbody>
                                    {associates.map((associate) => (
                                        <Table.Tr key={associate.profileID}>
                                            <Table.Td>
                                                {associate.profileName}
                                            </Table.Td>
                                            <Table.Td>
                                                {associate.profileContact}
                                            </Table.Td>
                                            <Table.Td>
                                                {associate.profileEmail}
                                            </Table.Td>
                                            <Table.Td>
                                                {dayjs(
                                                    associate.created_at
                                                ).format("YYYY-MM-DD HH:mm:ss")}
                                            </Table.Td>
                                            <Table.Td>
                                                {renderInterviewStatus(
                                                    associate.interview_status
                                                )}
                                            </Table.Td>
                                            <Table.Td>
                                                <Group>
                                                    <CnaButton
                                                        variant="outline"
                                                        radius="xl"
                                                        onClick={() =>
                                                            handleViewProfile(
                                                                associate.profileID
                                                            )
                                                        }
                                                    >
                                                        查看资料
                                                    </CnaButton>
                                                    {associate.interview_status ===
                                                        0 && (
                                                        <CnaButton
                                                            radius="xl"
                                                            onClick={() =>
                                                                handleStartInterview(
                                                                    associate.profileID
                                                                )
                                                            }
                                                        >
                                                            开始面试
                                                        </CnaButton>
                                                    )}
                                                </Group>
                                            </Table.Td>
                                        </Table.Tr>
                                    ))}
                                    {!associates.length && !loading && (
                                        <Table.Tr>
                                            <Table.Td colSpan={6}>
                                                <Text
                                                    ta="center"
                                                    c="dimmed"
                                                >
                                                    暂无数据
                                                </Text>
                                            </Table.Td>
                                        </Table.Tr>
                                    )}
                                </Table.Tbody>
                            </Table>
                        </ScrollArea>
                    </Tabs.Panel>
                    <Tabs.Panel value="bonus">
                        <ScrollArea
                            h="calc(100vh - 400px)"
                            mt="lg"
                        >
                            <Table
                                withRowBorders={false}
                                highlightOnHover
                                className="custom-table"
                            >
                                <Table.Thead>
                                    <Table.Tr>
                                        <Table.Th>业务完成时间</Table.Th>
                                        <Table.Th>姓名</Table.Th>
                                        <Table.Th>合伙人类型</Table.Th>
                                        <Table.Th>津贴类型</Table.Th>
                                        <Table.Th>业务类型</Table.Th>
                                        <Table.Th>津贴金额</Table.Th>
                                        <Table.Th>收款状态</Table.Th>
                                        <Table.Th>到账日期</Table.Th>
                                    </Table.Tr>
                                </Table.Thead>
                                <Table.Tbody>
                                    {/* <Table.Tr>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                        <Table.Td>张三</Table.Td>
                                        <Table.Td>三三制</Table.Td>
                                        <Table.Td>A类</Table.Td>
                                        <Table.Td>绿智地球</Table.Td>
                                        <Table.Td>￥200.00</Table.Td>
                                        <Table.Td>冻结中</Table.Td>
                                        <Table.Td>-</Table.Td>
                                    </Table.Tr>
                                    <Table.Tr>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                        <Table.Td>张三</Table.Td>
                                        <Table.Td>三三制</Table.Td>
                                        <Table.Td>B类</Table.Td>
                                        <Table.Td>AI Plus</Table.Td>
                                        <Table.Td>￥200.00</Table.Td>
                                        <Table.Td>
                                            <Text className="tw-text-[#05C126]">
                                                已到账
                                            </Text>
                                        </Table.Td>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                    </Table.Tr>
                                    <Table.Tr>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                        <Table.Td>张三</Table.Td>
                                        <Table.Td>管理合伙人</Table.Td>
                                        <Table.Td>C类</Table.Td>
                                        <Table.Td>高阶合伙人</Table.Td>
                                        <Table.Td>￥200.00</Table.Td>
                                        <Table.Td>
                                            <Text className="tw-text-[#DA0101]">
                                                未到账
                                            </Text>
                                        </Table.Td>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                    </Table.Tr>
                                    <Table.Tr>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                        <Table.Td>张三</Table.Td>
                                        <Table.Td>三三制</Table.Td>
                                        <Table.Td>D类</Table.Td>
                                        <Table.Td>绿智地球</Table.Td>
                                        <Table.Td>￥200.00</Table.Td>
                                        <Table.Td>
                                            <Text className="tw-text-[#FF9500]">
                                                进账中
                                            </Text>
                                        </Table.Td>
                                        <Table.Td>2024-01-01 12:00:00</Table.Td>
                                    </Table.Tr> */}
                                    <Table.Tr>
                                        <Table.Td colSpan={8}>
                                            <Text
                                                ta="center"
                                                c="dimmed"
                                            >
                                                暂无数据
                                            </Text>
                                        </Table.Td>
                                    </Table.Tr>
                                </Table.Tbody>
                            </Table>
                        </ScrollArea>
                    </Tabs.Panel>
                </Tabs>
            </Paper>
        </div>
    );
};

export default Associates;
