import React, { useState, useEffect, useRef } from "react";
import { Box, Image, Stack } from "@mantine/core";
import { useNavigate, useLocation } from "react-router-dom";
import "./Welcome.css";
import { useRequest } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";
import useRegisterStore from "@/store/register";

// 定义职位文本常量
const POSITION_TEXTS = {
    "-1": {
        title: "营运总裁",
        department: "C&A 中国总公司",
    },
    "11": {
        title: "战略总监",
        department: "C&A 营运基地",
    },
    "10": {
        title: "策划总监",
        department: "C&A 战略办事处",
    },
    "9": {
        title: "行政总监",
        department: "C&A 策划办事处",
    },
    "8": {
        title: "市场总监",
        department: "C&A 行政办事处",
    },
    "7": {
        title: "执行总监",
        department: "C&A 市场办事处",
    },
    "6": {
        title: "监督主任",
        department: "C&A 执行办事处",
    },
    "5": {
        title: "咨询主任",
        department: "C&A 监督办事处",
    },
    "4": {
        title: "指导主任",
        department: "C&A 咨询办事处",
    },
    "3": {
        title: "业务主任",
        department: "C&A 指导办事处",
    },
    "2": {
        title: "中国区域合伙人",
        department: "C&A 业务办事处",
    },
} as const;

const TeamWelcomePage = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [isOpen, setIsOpen] = useState(false);
    const [mounted, setMounted] = useState(false);
    const [isAnimating, setIsAnimating] = useState(false);
    const { refer } = useRegisterStore();
    const [containerWidth, setContainerWidth] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    // 存储推荐人refer 到 sessionStorage
    const [sessionRefer] = useState(() => {
        const savedRefer = sessionStorage.getItem("refer");
        if (refer) {
            sessionStorage.setItem("refer", refer);
            return refer;
        }
        return savedRefer || "";
    });

    // 获取推荐人信息
    const { data: refferer } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<ReffererInfo>(
                "/api/v1/team/getRecommendInfo",
                "POST",
                {
                    token: sessionRefer,
                }
            );
            if (!error) {
                return result.data;
            }
            return null;
        },
        {
            manual: false,
            ready: sessionRefer?.length > 0,
        }
    );

    // 修改导航函数
    const toDocuments = () => {
        setIsOpen(false);
        const queryParams = new URLSearchParams(location.search);
        const refer = queryParams.get("refer");
        const url = refer
            ? "/account/fields?refer=" + refer
            : "/account/fields";

        const timer = setTimeout(() => {
            navigate(url, { replace: true });
        }, 100);
        return () => clearTimeout(timer);
    };

    const [titleText, setTitleText] = useState(
        `欢迎您加入 "全球合伙人大联盟" 成为 C&A 中国区域合伙人，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，然后跟随系统指示完成合伙人加盟流程，设置您的 "AI 个人办公室"，开通 AI 账号，开启业务工作。`
    );

    useEffect(() => {
        const teamRank = refferer?.is_team ? refferer?.team_rank : null;
        if (
            !teamRank ||
            !POSITION_TEXTS[teamRank as keyof typeof POSITION_TEXTS]
        ) {
            return;
        }
        const position =
            POSITION_TEXTS[teamRank as keyof typeof POSITION_TEXTS];

        if (teamRank == "2") {
            setTitleText(
                `恭贺您受 C&A 业务办事处的推荐加入成为 C&A ${position.title}，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，跟随系统指示完成合伙人加盟流程，设置您的 "AI 个人办公室"，开通 AI 账号，开启业务工作。`
            );
        } else {
            setTitleText(
                `恭贺您受 ${position.department}的推荐出任 "${position.title}" 职位，请耐心阅读 C&A 企业简介、合伙人加盟手册及其他相关 PPT，跟随系统指示完成合伙人加盟流程，并在您的专属 "AI 办事处" 获取 "${position.title}" 手册，开启工作完成职务指标。`
            );
        }
    }, [refferer]);

    useEffect(() => {
        setMounted(true);
        return () => setMounted(false);
    }, []);

    // 路由变化时的状态重置
    useEffect(() => {
        if (mounted) {
            setIsOpen(false);
            const timer = setTimeout(() => {
                setIsOpen(true);
            }, 100);

            return () => {
                clearTimeout(timer);
                setIsOpen(false);
            };
        }
    }, [location.key, mounted]);

    // 组件卸载时的清理
    useEffect(() => {
        return () => {
            setIsOpen(false);
            setMounted(false);
        };
    }, []);

    const paperContentRef = React.useRef<HTMLDivElement>(null);

    // useEffect(() => {
    //     if (isOpen && paperContentRef.current) {
    //         const element = paperContentRef.current;
    //         const totalDuration = 50000; // 5秒
    //         const totalDistance = element.scrollHeight;
    //         const steps = 1000; // 将滚动分成100步
    //         const stepDuration = totalDuration / steps;
    //         const stepDistance = totalDistance / steps;
    //         let currentStep = 0;

    //         // 添加500ms延迟
    //         const timer = setTimeout(() => {
    //             const scrollInterval = setInterval(() => {
    //                 if (currentStep >= steps) {
    //                     clearInterval(scrollInterval);
    //                     return;
    //                 }

    //                 element.scrollTop += stepDistance;
    //                 currentStep++;
    //             }, stepDuration);

    //             // 清理interval
    //             return () => clearInterval(scrollInterval);
    //         }, 1000); // 500ms延迟

    //         // 清理所有定时器
    //         return () => {
    //             clearTimeout(timer);
    //         };
    //     }
    // }, [isOpen]);

    const containerStyles = {
        position: "relative" as const,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        minHeight: "100vh",
    };

    // 监听容器宽度变化
    useEffect(() => {
        // 更新容器宽度的函数
        const updateWidth = () => {
            if (containerRef.current) {
                const width = containerRef.current.offsetWidth;
                setContainerWidth(width);
            }
        };

        // 初始化时更新一次
        updateWidth();
        // 监听窗口大小变化
        window.addEventListener("resize", updateWidth);
        // 清理函数：组件卸载时移除事件监听
        return () => window.removeEventListener("resize", updateWidth);
    }, []);

    // iOS设备特定样式处理
    useEffect(() => {
        // 创建样式元素
        const style = document.createElement("style");
        // 根据容器宽度计算对应高度，保持764:406的比例，最大高度406px
        const height = Math.min(containerWidth * (406 / 764), 406);

        // 仅在iOS设备上应用样式
        // @supports (-webkit-touch-callout: none) 用于检测iOS设备
        style.innerHTML = `
            @supports (-webkit-touch-callout: none) {
                .envelope-container {
                    height: ${height}px !important;
                    aspect-ratio: auto !important;
                }
            }
        `;

        // 将样式注入到文档头部
        document.head.appendChild(style);

        // 清理函数：组件卸载时移除注入的样式
        return () => {
            document.head.removeChild(style);
        };
    }, [containerWidth]); // 当容器宽度变化时重新计算

    const envelopStyles = {
        position: "relative" as const,
        width: "100%",
        maxWidth: "764px", // 最大宽度限制
        aspectRatio: "764/406", // 默认宽高比（非iOS设备）
        background: "url('/images/envelop-back.png') no-repeat",
        backgroundSize: "100% 100%",
        transform: isOpen
            ? window.innerWidth <= 375
                ? "translateY(92%)"
                : window.innerWidth <= 400
                ? "translateY(163px)"
                : "translateY(202px)"
            : "none",
        transition: "transform 0.3s ease",
    };

    const paperStyles = {
        position: "absolute" as const,
        left: "5.5%",
        width: "89.5%",
        // height: "162.6%",
        // height: "120.6%",
        background: "#ffffff",
        boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
        transform: isOpen
            ? "translateY(0)"
            : window.innerWidth <= 475
            ? "translateY(2%)"
            : window.innerWidth >= 475
            ? "translateY(15%)"
            : "translateY(21%)",
        zIndex: 17,
        padding: "clamp(15px, 3vw, 30px)",
        borderRadius: "4px",
        overflow: "auto",
        backgroundImage: "url('/images/C&Apaper-content.png')",
        backgroundSize: "116% 100%", // 让图片覆盖整个容器
        backgroundPosition: "center", // 居中对齐
        backgroundRepeat: "no-repeat", // 防止重复
    };

    const textStyles = {
        fontSize: "clamp(13px, 2vw, 18px)",
        lineHeight: 1.8,
        marginBottom: "10px",
        textIndent: "2em",
        fontWeight: "bold",
        color: "#060d3d",
        // color: "rgb(52 69 82)",
        letterSpacing: "3px",
    };

    return (
        <div className="team-page-wrapper boot-bg">
            <Image
                className="logo-desktop"
                src="/images/C&A-Logo-Icon-White.svg"
                alt=""
                w={80}
                draggable={false}
            />
            {/* <Image
                className="logo-mobile"
                src="/images/C&A-Logo-Full-White.svg"
                alt=""
                w={220}
            /> */}

            <Box className="team-page-container">
                <Stack className="content-stack">
                    <div
                        style={containerStyles}
                        ref={containerRef}
                    >
                        <div
                            className="envelope-container"
                            style={envelopStyles}
                        >
                            <div
                                className={`envelope-part top ${
                                    isOpen ? "open" : ""
                                }`}
                            />
                            {/* 这里替换原来的 div */}
                            <Image
                                src="/images/envelop-front1.png"
                                className="envelope-part front"
                                alt="信封正面"
                            />
                            <div
                                className={`paper ${isOpen ? "open" : ""}`}
                                style={paperStyles}
                            >
                                <div
                                    className="paper-content"
                                    ref={paperContentRef}
                                    // style={{
                                    //     backgroundImage: "url('/images/C&Apaper-content.png')",
                                    //     backgroundSize: "cover", // 让图片覆盖整个容器
                                    //     backgroundPosition: "center", // 居中对齐
                                    //     backgroundRepeat: "no-repeat" // 防止重复
                                    // }}
                                >
                                    <Image
                                        src="/images/C&A Logo (Horizontal Blue) v1.svg"
                                        className="paper-contents"
                                        alt="信封内容logo"
                                        style={{
                                            marginBottom: "20px",
                                            scale: "0.8",
                                            marginTop: "6%",
                                        }}
                                        draggable={false}
                                    />
                                    <Image
                                        src="/images/Pen.png"
                                        className="paper-contents"
                                        alt="Pen"
                                        style={{
                                            width: "30%",
                                            marginLeft: "70%",
                                            marginBottom: "-10px", // 增加与下方文字的间距
                                            display: "block", // 确保不会受到行内元素默认行为的影响
                                        }}
                                    />
                                    <p style={{ ...textStyles, textIndent: 0 }}>
                                        亲爱的朋友，
                                    </p>
                                    <p style={textStyles}>
                                        C&A 新加坡启动
                                        "全球合伙人大联盟"，打造全球最强大的专业人士资源共享
                                        AI
                                        赋能平台，邀请中国区域专业人士加盟，共享
                                        C&A 强大的品牌 IP 和独家代理的
                                        "绿智地球"
                                        业务，加入全球最顶尖的智库型专业人士圈层，对接世界各地的专业资源和商业机会。
                                    </p>
                                    <p style={textStyles}>
                                        C&A 提供 "AI 个人办公室"、"AI 小助理" 和
                                        "AI
                                        国际咨询资源库"，赋能合伙人于国际咨询和跨国商业服务的业务能力。配置
                                        "全球资源共享 AI 平台" 和 "全球语言切换
                                        AI 洽谈室"
                                        促进世界各地合伙人之间的交流和合作，相互支援落地业务，共享客户人脉资源，开拓海外市场新契机，撮合彼此间企业客户的生意对接。
                                    </p>
                                    <p style={textStyles}>
                                        C&A 独家代理的 "绿智地球" 业务和独创的
                                        "战略沙盘"
                                        工具，赋能合伙人协助中国企业运用 AI
                                        以最短的时间和最低的成本跳出内卷，领跑绿色智慧新赛道，抢占海外无人区国际市场，辅导中国企业以
                                        "知识经济" 借力 "资本市场"
                                        的创新模式开展全球战略布局，抓住时代风口成为
                                        "绿色智慧城市"
                                        特定行业或细分领域的霸主。
                                    </p>
                                    <p style={textStyles}>{titleText}</p>
                                </div>
                            </div>

                            <div className="envelope-part bottom">
                                <div className="seal" />
                            </div>

                            <div
                                className={`
                                    dynamicButtons
                                    ${isOpen ? "opened" : ""}
                                    ${isAnimating ? "animating" : ""}
                                    tw-tracking-[0.5rem]
                                    tw-relative
                                    tw-z-[10000]
                                `}
                                style={{ letterSpacing: "8px" }}
                                onClick={toDocuments}
                            >
                                <div
                                    className="
                                    tw-flex
                                    tw-items-center
                                    tw-justify-center
                                    tw-w-full
                                    sm:tw-text-base
                                    tw-text-sm
                                "
                                >
                                    <span>继续</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </Stack>
            </Box>

            <div className="footer">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2025
            </div>
        </div>
    );
};

export default TeamWelcomePage;
