import Catalog from "@/components/member/business/Catalog";
import IntroModal from "@/components/member/business/modals/Intro";
import useModalStore from "@/store/modal";
import useBusinessStore, {
    setBusinessSection,
    setCourseInfo,
} from "@/store/business";
import api from "@/apis";

const Courses = () => {
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const handleBack = () => {
        setBusinessSection("unlicensed-intro");
    };

    const handleStartExam = async () => {
        const canExam = await api.business.checkCanExam("56");
        if (!canExam) {
            openConfirm({
                title: "无法考试",
                message: "您尚有学习未完成，无法开始考试",
            });
            return false;
        }
        // 显示考试信息弹窗
        openModal("businessIntro");
    };

    const handleLearn = (id: string) => {
        setCourseInfo(parseInt(id), 56);
        setBusinessSection("unlicensed-learn");
    };

    return (
        <>
            <Catalog
                courseId="56"
                title="合伙人加盟手册课程目录"
                onBack={handleBack}
                onStartExam={handleStartExam}
                onLearn={handleLearn}
            />

            <IntroModal
                title="申请合伙人加盟手册牌照"
                onStartExam={async () => {
                    // 添加考试进程
                    await api.exam.addProcess(
                        "partner_franchise_handbook_license"
                    );
                    setBusinessSection("unlicensed-exam");
                }}
            />
        </>
    );
};

export default Courses;
