import PageHeader from "@/components/member/PageHeader";
import {
    Paper,
    ScrollArea,
    Divider,
    Checkbox,
    Group,
    Image,
    Modal,
    Stack,
    Title,
    Text,
    SimpleGrid,
} from "@mantine/core";
import { useState, useEffect } from "react";
import ExamPdfViewer from "@/components/member/business/ExamPdfViewer";
import { CnaButton } from "@code.8cent/react/components";
import { ArrowRight, WarningCircle } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import api from "@/apis";
import useProfileStore from "@/store/profile";
import dayjs from "dayjs";

const Upgrade = () => {
    const profile = useProfileStore();

    const [pdfFile, setPdfFile] = useState<File | null>(null);
    const [checked, setChecked] = useState(false);
    const navigate = useNavigate();
    const [modalOpened, setModalOpened] = useState(false);

    useEffect(() => {
        fetch("/files/管理合伙人晋升须知.pdf")
            .then((response) => response.blob())
            .then((blob) => {
                const file = new File([blob], "管理合伙人晋升须知.pdf", {
                    type: "application/pdf",
                });
                setPdfFile(file);
            })
            .catch((error) => {
                console.error("Error loading document:", error);
            });
    }, []);

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="晋级管理合伙人"
                subTitle="本栏目您可以通过答题方式获取管理合伙人晋级资格，成功后您将有机会推荐更多合伙人加盟获得津贴分发"
            />

            <Modal
                opened={modalOpened}
                onClose={() => setModalOpened(false)}
                centered
                radius="md"
                size="lg"
                withCloseButton={false}
            >
                <Stack className="tw-items-center">
                    <Title order={3}>晋级管理合伙人答题</Title>
                    <Stack className="tw-bg-white tw-p-2 tw-rounded">
                        <Stack gap={0}>
                            <Text fw={700}>
                                题目类型：单选题、多选题（共5题）
                            </Text>
                            <Text fw={700}>答题时长：10min</Text>
                            <Text fw={700}>通过要求：全部正确</Text>
                            <Text fw={700}>说明：允许再次答题，次数不限</Text>
                        </Stack>
                        <Text className="tw-mt-20">
                            温馨提示：点击答题开始倒计时，中途退出已作答题目不保留，超时未答完视为答题不通过。
                        </Text>
                    </Stack>
                    <SimpleGrid
                        cols={2}
                        className="tw-w-full tw-p-2"
                    >
                        <CnaButton
                            variant="default"
                            className="tw-bg-[#F2F2F2] tw-border-none"
                            onClick={() => setModalOpened(false)}
                        >
                            返回
                        </CnaButton>
                        <CnaButton
                            onClick={async () => {
                                try {
                                    const res = await api.exam.addProcess(
                                        "profile_upgrade_partner_manager"
                                    );

                                    if (res && res.status) {
                                        navigate("/member/upgrade/exam", {
                                            replace: true,
                                        });
                                    }
                                } catch (error) {
                                    console.error(error);
                                }
                            }}
                        >
                            开始答题
                        </CnaButton>
                    </SimpleGrid>
                </Stack>
            </Modal>

            <Stack
                gap={0}
                className="tw-h-full"
            >
                <Image
                    src="/images/station/exam-top.svg"
                    className="tw-mt-8 tw-hidden md:tw-block"
                />
                <Paper
                    className="tw-bg-white tw-w-full md:tw-h-[calc(100%-100px)] tw-h-full tw-relative"
                    p="md"
                >
                    <div className="tw-h-[calc(100%-80px)]">
                        {pdfFile && <ExamPdfViewer file={pdfFile} />}
                    </div>

                    <Divider
                        variant="dotted"
                        my="sm"
                    />
                    <Stack
                        className="tw-absolute tw-bottom-4 tw-left-4 tw-right-4 md:tw-bottom-8"
                        gap={8}
                    >
                        {/* <Group
                            gap={4}
                            c="#DA0101"
                            wrap="nowrap"
                        >
                            <WarningCircle className="tw-size-24 md:tw-size-6" />
                            <Text size="sm">
                                于
                                {dayjs()
                                    .add(profile.manager_time, "day")
                                    .format("YYYY年MM月DD日")}
                                前完成管理合伙人晋级资格获取并成功推荐至少一人加盟，则可全额返还已缴纳的加盟开户费、Ai账号费及业务资格认证费，共计9,000新币。逾期未达成上述条件者，费用恕不返还。
                            </Text>
                        </Group> */}
                        <Group justify="space-between" align="flex-end">
                            <Checkbox
                                label="我已阅读并知悉《晋级申请声明》"
                                checked={checked}
                                onChange={(e) => setChecked(e.target.checked)}
                            />
                            <div className="tw-ml-auto">
                                <CnaButton
                                    disabled={!checked}
                                    rightSection={<ArrowRight />}
                                    size="sm"
                                    fw={700}
                                    onClick={() => {
                                        setModalOpened(true);
                                    }}
                                >
                                    下一步
                                </CnaButton>
                            </div>
                        </Group>
                    </Stack>
                </Paper>
            </Stack>
        </Stack>
    );
};

export default Upgrade;
