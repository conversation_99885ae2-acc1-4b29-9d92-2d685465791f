import cnaRequest from "@code.8cent/utils/cnaRequest";
import noty from "@code.8cent/react/noty";

const register = {
    register: async (params) => {
        let { result, error } = await cnaRequest<
            | {
                  email: string;
                  prefixID: string;
                  phone: string;
                  nationalityID: string;
              }
            | { token: string }
        >("/api/v1/register", "POST", params);

        if (!error) {
            return result.data;
        } else {
            return {
                error: true,
                message: error.message,
            };
        }
    },
    sendVerifyEmail: async (email: string) => {
        let { error } = await cnaRequest("/api/v1/register/sendEmail", "POST", {
            email,
        });

        if (!error) {
            return true;
        } else {
            return false;
        }
    },
    verifyEmail: async (email: string, code: string) => {
        let { error } = await cnaRequest(
            "/api/v1/register/verifyEmail",
            "POST",
            {
                email,
                code,
            }
        );

        if (!error) {
            return true;
        } else {
            return false;
        }
    },
    sendVerifySMS: async (phone: string, prefixID: string) => {
        let { error } = await cnaRequest("/api/v1/register/sendSms", "POST", {
            phone,
            prefixID,
        });

        if (!error) {
            return true;
        } else {
            return false;
        }
    },
    verifyPhone: async (phone: string, prefixID: string, code: string) => {
        let { error } = await cnaRequest("/api/v1/register/verifySms", "POST", {
            phone,
            code,
            prefixID,
        });

        if (!error) {
            return true;
        } else {
            return false;
        }
    },
    confirm: async (params) => {
        let { result, error } = await cnaRequest<{ token: string }>(
            "/api/v1/register/confirm",
            "POST",
            params
        );

        if (!error) {
            return result.data;
        } else {
            return {
                error: true,
                message: error.message,
            };
        }
    },
    generateWechatPayQRCode: async (token: string) => {
        let { error, result } = await cnaRequest<{ qrcode: string }>(
            "/api/v1/wechat/native",
            "POST",
            { token }
        );

        if (!error) {
            return result.data.qrcode;
        } else {
            return null;
        }
    },

    checkPayStatus: async (token: string) => {
        const { result, error } = await cnaRequest<{
            payment_state: boolean;
            /**
             * 初始设置未完成。数组可能值：settingLanguage（系统语言）, profilePassword（登录密码）, profileAvatar（头像）
             */
            register_setting?: string[];
            /**
             * 登录token
             */
            token?: string;
        }>("/api/v1/wechat/transaction", "POST", { token });

        if (error) {
            return {
                payment_state: false,
                register_setting: [],
                token: "",
            };
        } else {
            return result.data;
        }
    },

    getWizardState: async () => {
        const { result, error } = await cnaRequest<{
            state: number;
        }>("/api/v1/register/getRegisterSettingState", "GET");

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    setLanguage: async (languageCode: string) => {
        const { result, error } = await cnaRequest(
            "/api/v1/register/setLanguage",
            "POST",
            {
                languageCode,
            }
        );

        if (error) {
            noty.error("发生错误", error.message);
            return false;
        } else {
            return true;
        }
    },

    setSecurity: async (params: {
        currentPassword?: string;
        newPassword: string;
        passwordConfirmation: string;
    }) => {
        const { result, error } = await cnaRequest(
            "/api/v1/register/setSecurity",
            "POST",
            params
        );

        if (error) {
            noty.error("发生错误", error.message);
            return false;
        } else {
            return true;
        }
    },

    setProfile: async ({ name, phone }) => {
        const formData = new FormData();

        formData.append("name", name);
        formData.append("phone", phone);

        const { result, error } = await cnaRequest(
            "/api/v1/register/setProfile",
            "POST",
            formData
        );

        if (error) {
            noty.error("发生错误", error.message);
            return false;
        } else {
            return true;
        }
    },
    setTemplate: async (templateId) => {
        const formData = new FormData();

        formData.append("templateId", templateId);

        const { result, error } = await cnaRequest(
            "/api/v1/register/setTemplate",
            "POST",
            formData
        );

        if (error) {
            noty.error("发生错误", error.message);
            return false;
        } else {
            return true;
        }
    },
    setBusiness: async ({WebName1,WebName2,WebSlogan1,WebSlogan2}) => {
        const formData = new FormData();

        formData.append("WebName1", WebName1);
        formData.append("WebName2", WebName2);
        formData.append("WebSlogan1", WebSlogan1);
        formData.append("WebSlogan2", WebSlogan2);

        const { result, error } = await cnaRequest(
            "/api/v1/register/setBusiness",
            "POST",
            formData
        );

        if (error) {
            noty.error("发生错误", error.message);
            return false;
        } else {
            return true;
        }
    },

    // 上传身份证照片 1: 身份证人像面，2: 身份证国徽面
    setProfileNRIC: async (file: File, type: "1" | "2") => {
        const formData = new FormData();

        formData.append("file", file);
        formData.append("type", type);

        const { result, error } = await cnaRequest(
            "/api/v1/user/uploadNric",
            "POST",
            formData
        );

        if (error) {
            // noty.error("发生错误", error.message);
            return {
                success: false,
                message: error.message,
            };
        } else {
            // todo result 返回实际识别的身份证正反面 1: 身份证人像面，2: 身份证国徽面
            return {
                success: true,
                message: "ok",
            };
        }
    },
};

export default register;
