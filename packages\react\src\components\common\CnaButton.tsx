import {
    <PERSON>ton,
    PolymorphicComponentProps,
    ButtonProps,
    DefaultMantineColor,
    Group,
    createPolymorphicComponent,
} from "@mantine/core";
import { SpinnerGap } from "@phosphor-icons/react";
import { forwardRef } from "react";

interface CnaButtonProps extends ButtonProps {
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
}

function parseColorProp(color: DefaultMantineColor = "basic") {
    let level = "6";
    let base = "basic";

    const colorParts = color.split(".");

    if (colorParts.length > 1) {
        level = colorParts[1];
        base = colorParts[0];
    } else {
        base = colorParts[0];
    }

    return {
        base,
        level,
    };
}

const CnaButton = createPolymorphicComponent<"button", CnaButtonProps>(
    forwardRef<HTMLButtonElement, CnaButtonProps>((props, ref) => (
        <Button
            ref={ref}
            {...props} 
            color={
                props.loading === true
                    ? `${parseColorProp(props.color).base}.3`
                    : props.color || "basic"
            }
            className={`!tw-block ${
                props.disabled === true ? "tw-cursor-not-allowed" : ""
            } ${props.loading === true ? `tw-cursor-progress` : ""} ${
                props.className ?? ""
            }`}
            classNames={{
                ...props.classNames,
                loader: "tw-flex tw-w-full tw-h-full tw-items-center tw-justify-center !tw-transition-none",
                inner: "!tw-transition-none",
            }}
            onClick={
                props.loading !== true && props.disabled !== true
                    ? props.onClick
                    : undefined
            }
            loaderProps={{
                children: (
                    <Group justify="center" align="center">
                        <SpinnerGap
                            className="tw-animate-spin tw-text-inherit"
                            size={18}
                        />
                        {props.children}
                    </Group>
                ),
            }}
        >
            {props.children}
        </Button>
    ))
);

export default CnaButton;
