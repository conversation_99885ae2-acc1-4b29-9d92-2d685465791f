import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import { Button, Grid, Group, Image, Paper, Stack, Text } from "@mantine/core";
import { ArrowRight, CaretLeft } from "@phosphor-icons/react";
import useBusinessStore, {
    setBusinessSection,
    setCourseInfo,

} from "@/store/business";
import { useRequest } from "ahooks";
interface LicenseCardProps {
    title: string;
    description: string;
    gradientFrom: string;
    gradientTo: string;
    backgroundImage: string;
    onClick?: (type: string) => void;
    pass: boolean;
    disabledGradientFrom: string;
    disabledGradientTo: string;
}

const LicenseCard = ({
    title,
    description,
    gradientFrom,
    gradientTo,
    backgroundImage,
    onClick,
    pass,
    disabledGradientFrom,
    disabledGradientTo,
}: LicenseCardProps) => (
    <Paper
        p="xl"
        radius="lg"
        className="tw-h-[289px] tw-relative"
        style={{
            background: `linear-gradient(to right, ${pass ? gradientFrom : disabledGradientFrom}, ${pass ? gradientTo : disabledGradientTo})`,
        }}
    >
        <Stack
            justify="space-between"
            className="tw-h-full"
        >
            <Stack gap={4}>
                <Text size="xl" c="white" fw={600}  >
                    {title}
                </Text>
                <Text size="sm" c="white"  >
                    {description}
                </Text>
            </Stack>
            <Group justify="flex-start" className="tw-z-50">
                {
                    pass ?
                        <CnaButton
                            variant="transparent"
                            size="sm"
                            radius="xl"
                            className="tw-bg-[#EAEEFF]"
                            onClick={() => onClick('goDetail')}
                        >
                            <ArrowRight
                                color="#19288F"
                                weight="bold"
                            />
                        </CnaButton> :
                        <>
                            <CnaButton
                                variant="transparent"
                                size="sm"
                                radius="xl"
                                className="tw-bg-[#EAEEFF]"
                                onClick={() => onClick('study')}
                            >
                                进入学习
                            </CnaButton>
                            <CnaButton
                                variant="transparent"
                                size="sm"
                                radius="xl"
                                className="tw-bg-[#EAEEFF]"
                                onClick={() => onClick('exam')}
                            >
                                开始考试
                            </CnaButton>
                        </>
                }
            </Group>
        </Stack>

        <Image
            src={backgroundImage}
            w={600}
            className="tw-absolute tw-bottom-0 tw-right-0"
        />
    </Paper >
);

let licenseData = [
    {
        id: "56",
        title: "绿智地球",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的",
        gradientFrom: "#353066",
        gradientTo: "#425097",
        disabledGradientFrom: "#616d7c",
        disabledGradientTo: "#7f97a2",
        backgroundImage: "/images/station/earth.svg",
        pass: true,
        section: "green-earth",
    },
    {
        id: "56",
        title: "合伙人企业高阶课程",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的",
        gradientFrom: "#353066",
        gradientTo: "#425097",
        disabledGradientFrom: "#616d7c",
        disabledGradientTo: "#7f97a2",
        backgroundImage: "/images/station/section.svg",
        pass: false,
        section: "green-earth",
    },
    {
        id: "56",
        title: "AiPlus",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#353066",
        gradientTo: "#425097",
        disabledGradientFrom: "#616d7c",
        disabledGradientTo: "#7f97a2",
        backgroundImage: "/images/station/aiplus.svg",
        pass: false,
        section: "green-earth",
    },
    {
        id: "56",
        title: "其他",
        description: "简单介绍这是干啥的简单介绍这是干啥的简单介绍这是干啥的简单介绍",
        gradientFrom: "#353066",
        gradientTo: "#425097",
        disabledGradientFrom: "#616d7c",
        disabledGradientTo: "#7f97a2",
        backgroundImage: "/images/station/section.svg",
        pass: false,
        section: "green-earth",
    },
];

const BasicLicensedList = () => {

    //todo:需要补充获取查询牌照是否通过
    const { run: getLicensedList } = useRequest(async () => {
        // const res = await business.getBusinessList("partner_franchise_handbook_license");

    });

    const handleLicensedClick = (type: string, license: any) => {
        console.log('点击牌照', license);
        if (type == 'study') {
            setCourseInfo(license.id, license.id);
            setBusinessSection('unlicensed-intro');
        } else if (type == 'exam') {
            setCourseInfo(license.id, license.id);
            setBusinessSection('unlicensed-exam');
        } else if (type == 'goDetail') {
            setBusinessSection(license.section);
        }
    }

    return (
        <Stack className="tw-h-full">
            <PageHeader
                title="基础业务认证牌照"
                subTitle="基础业务认证牌照业务介绍基础业务认证牌照业务介绍"
            />
            <div className="tw-w-20 tw-flex tw-items-center tw-gap-2 text-2xl tw-font-bold tw-cursor-pointer" onClick={() => setBusinessSection("licensed-list")}>
                <CaretLeft size={24} /> 返回
            </div>

            <Grid gutter="xs">
                {
                    licenseData.map((license, index) => (
                        <Grid.Col span={6} key={index}>
                            <LicenseCard
                                {...license}
                                onClick={(type) => handleLicensedClick(type, license)}
                            />
                        </Grid.Col>
                    ))
                }
            </Grid>
        </Stack >
    );
};

export default BasicLicensedList;
