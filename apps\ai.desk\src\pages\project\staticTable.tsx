import React from "react";
import { useLocation } from "react-router-dom";

// 引入所有组件
import SummaryForm from "@/components/apply/static/summary_5";
import CompanyProfile from "@/components/apply/static/companyProfile_6";
import BusinessesForm from "@/components/apply/static/businesses_19";
import Employees_22 from "@/components/apply/static/employees_22";
import FuturePlanning_18 from "@/components/apply/static/futurePlanning_18";
import ProductionCapacityForm from "@/components/apply/static/productionCapacityForm";
import { useTableStore } from "@/components/apply/store/tableStore";
import TableModel from "@/components/apply/model/tableModel";
import AfterSalesService from "@/components/apply/static/afterSalesService_13";
import Other_39 from "@/components/apply/static/other_39";
import CombinedProductionForm from "@/components/apply/static/combinedProductionForm";
// 可选：统一样式容器组件
import Management_49 from "@/components/apply/static/management_49";
import Shoot_64 from "@/components/apply/static/shoot_64";
import { MyComponent } from "@/components/apply/static/test";
const PageWrapper = ({ children }: { children: React.ReactNode }) => {
    return <div className="tw-w-full tw-h-full">{children}</div>;
};

import IntellectualPropertyForm from "@/components/apply/static/intellectualPropertyForm";

const StaticTable = () => {
    const { openedTable, tableFile, closeTable, tableTitle, downloadTitle } =
        useTableStore();
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const key = params.get("key");
    const id = params.get("id");
    const title = params.get("title");
    const word = params.get("word");

    let content: React.ReactNode = <div>StaticTable</div>;

    switch (id) {
        case "5":
            content = <SummaryForm title={title} />;
            break;
        case "6":
            content = <CompanyProfile title={title} />;
            break;
        case "10":
            content = <CombinedProductionForm title={title} />;
            break;
        case "19":
            content = <BusinessesForm title={title} />;
            break;
        case "13":
            content = <AfterSalesService title={title} />;
            break;
        case "22":
            content = <Employees_22 title={title} />;
            break;
        case "23":
            content = <IntellectualPropertyForm title={title} />;
            break;
        case "18":
            content = (
                <FuturePlanning_18
                    title={title}
                    word={word}
                />
            );
            break;
        case "39":
            content = <Other_39 title={title} />;
            break;
        case "49":
            content = <Management_49 title={title} />;
            break;
        case "64":
            content = <Shoot_64 title={title} />;
            break;
    }

    return (
        <PageWrapper>
            {content}
            <TableModel
                openedTable={openedTable}
                closeTable={() => {
                    closeTable();
                }}
                tableFile={tableFile}
                title={{
                    tableTitle,
                    downloadTitle,
                }}
            />
        </PageWrapper>
    );
};

export default StaticTable;
